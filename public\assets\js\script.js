/**
 * Main script file for bus booking application
 * This file uses ES modules to organize code into separate files
 */
import { renderTrips } from './modules/tripRenderer.js';
import { setupFilters } from './modules/filters.js';
import { initCitySearch } from './modules/citySearch.js';
import { setupTripDetails } from './modules/tripDetails.js';
import { setupSeatSelection } from './modules/seatSelection.js';

document.addEventListener("DOMContentLoaded", async function () {
  // Initialize city search functionality
  await initCitySearch();

  // Fetch and render trips
  const allTrips = await renderTrips();

  // Setup filters with the trips data
  setupFilters(renderTrips, allTrips);

  // Setup trip details functionality
  setupTripDetails(allTrips);

  // Setup seat selection functionality
  setupSeatSelection();

  // Toggle between form and results
  document.getElementById("edit-search").addEventListener("click", () => {
    document.querySelector(".search-form").classList.toggle("d-none");
    document.querySelector(".search-header").classList.toggle("d-none");
  });

  // Hide form on load
  document.querySelector(".search-form").classList.add("d-none");
});

