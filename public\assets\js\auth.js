// Configuration de l'API
window.API_BASE_URL = 'api/v1';

// Utilitaires pour les alertes
window.showAlert = function(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) return; // Protection contre l'élément manquant
    const alertId = 'alert-' + Date.now();
    
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" id="${alertId}">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHTML);
    
    // Auto-suppression après 5 secondes
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// Utilitaires pour le stockage local
function setAuthToken(token) {
    localStorage.setItem('authToken', token);
}

function getAuthToken() {
    return localStorage.getItem('authToken');
}

function removeAuthToken() {
    localStorage.removeItem('authToken');
}

function setUserData(userData) {
    localStorage.setItem('userData', JSON.stringify(userData));
}

function getUserData() {
    const userData = localStorage.getItem('userData');
    return userData ? JSON.parse(userData) : null;
}

// Fonction pour faire des requêtes API
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}/${endpoint}`;
    const token = getAuthToken();
    
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            ...(token && { 'Authorization': `Bearer ${token}` })
        }
    };
    
    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erreur de requête');
        }
        
        return data;
    } catch (error) {
        console.error('Erreur API:', error);
        throw error;
    }
}

// Gestion du formulaire de connexion
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            try {
                // Désactiver le bouton de soumission
                const submitBtn = e.target.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Connexion...';

                const response = await apiRequest('auth/login', {
                    method: 'POST',
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        remember_me: rememberMe
                    })
                });

                // Stocker le token et les données utilisateur
                setAuthToken(response.token);
                setUserData(response.user);

                showAlert('Connexion réussie ! Redirection...', 'success');

                // Redirection intelligente basée sur le rôle
                setTimeout(() => {
                    const returnUrl = new URLSearchParams(window.location.search).get('return');
                    let redirectUrl = '/user';

                    // Redirection basée sur le rôle
                    if (response.user.roles && response.user.roles.includes('operator')) {
                        redirectUrl = '/operator';
                    }

                    // Utiliser l'URL de retour si spécifiée
                    if (returnUrl) {
                        redirectUrl = returnUrl;
                    }

                    window.location.href = redirectUrl;
                }, 1500);

            } catch (error) {
                showAlert(error.message, 'danger');

                // Réactiver le bouton
                const submitBtn = e.target.querySelector('button[type="submit"]');
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Se connecter';
            }
        });
    }
});

// Gestion du formulaire d'inscription
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const email = document.getElementById('registerEmail').value;
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const acceptTerms = document.getElementById('acceptTerms').checked;

            // Validations côté client
            if (password !== confirmPassword) {
                showAlert('Les mots de passe ne correspondent pas', 'danger');
                return;
            }

            if (password.length < 8) {
                showAlert('Le mot de passe doit contenir au moins 8 caractères', 'danger');
                return;
            }

            if (!acceptTerms) {
                showAlert('Vous devez accepter les conditions d\'utilisation', 'danger');
                return;
            }

            try {
                // Désactiver le bouton de soumission
                const submitBtn = e.target.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Création du compte...';

                await apiRequest('auth/register', {
                    method: 'POST',
                    body: JSON.stringify({
                        first_name: firstName,
                        last_name: lastName,
                        email: email,
                        phone: phone,
                        password: password
                    })
                });

                showAlert('Compte créé avec succès ! Vous pouvez maintenant vous connecter.', 'success');

                // Basculer vers l'onglet de connexion
                setTimeout(() => {
                    const loginTab = document.getElementById('login-tab');
                    const loginEmail = document.getElementById('loginEmail');
                    if (loginTab && loginEmail) {
                        loginTab.click();
                        loginEmail.value = email;
                    }
                }, 1500);

            } catch (error) {
                showAlert(error.message, 'danger');

                // Réactiver le bouton
                const submitBtn = e.target.querySelector('button[type="submit"]');
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-user-plus me-2"></i>Créer mon compte';
            }
        });
    }
});

// Validation en temps réel du mot de passe
document.getElementById('confirmPassword').addEventListener('input', function() {
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && password !== confirmPassword) {
        this.setCustomValidity('Les mots de passe ne correspondent pas');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Validation du format de l'email
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Validation du numéro de téléphone
function validatePhone(phone) {
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
    return phoneRegex.test(phone);
}

// Validation en temps réel des champs
document.getElementById('registerEmail').addEventListener('blur', function() {
    if (this.value && !validateEmail(this.value)) {
        this.setCustomValidity('Format d\'email invalide');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

document.getElementById('phone').addEventListener('blur', function() {
    if (this.value && !validatePhone(this.value)) {
        this.setCustomValidity('Format de téléphone invalide');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Vérifier si l'utilisateur est déjà connecté
document.addEventListener('DOMContentLoaded', function() {
    const token = getAuthToken();
    const userData = getUserData();
    
    if (token && userData) {
        // L'utilisateur est déjà connecté, rediriger vers le tableau de bord
        const returnUrl = new URLSearchParams(window.location.search).get('return') || '/user';
        window.location.href = returnUrl;
    }
});

// Fonction pour déconnecter l'utilisateur
function logout() {
    removeAuthToken();
    localStorage.removeItem('userData');
    window.location.href = 'index.html';
}

// Fonction pour vérifier si l'utilisateur est connecté
function isAuthenticated() {
    return !!getAuthToken();
}

// Fonction pour obtenir les informations de l'utilisateur connecté
function getCurrentUser() {
    return getUserData();
}
