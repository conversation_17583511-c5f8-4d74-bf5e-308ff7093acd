<?php
require_once __DIR__ . '/../Helpers/db.php';

class PaymentModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    public function createPayment($data) {
        $sql = "INSERT INTO payment (booking_id, amount, currency, payment_method, payment_provider,
                                   payment_status, created_by)
                VALUES (:booking_id, :amount, :currency, :payment_method, :payment_provider,
                        :payment_status, :created_by)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':booking_id' => $data['booking_id'],
            ':amount' => $data['amount'],
            ':currency' => $data['currency'],
            ':payment_method' => $data['payment_method'],
            ':payment_provider' => $data['payment_provider'] ?? null,
            ':payment_status' => $data['payment_status'] ?? 'pending',
            ':created_by' => $data['created_by'] ?? null
        ]);
        return $this->db->lastInsertId();
    }

    /**
     * Récupérer un paiement par son ID
     */
    public function getPaymentById($paymentId) {
        $sql = "SELECT p.*, b.booking_id, b.total_amount as booking_amount
                FROM payment p
                JOIN booking b ON p.booking_id = b.booking_id
                WHERE p.payment_id = :payment_id";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([':payment_id' => $paymentId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Récupérer un paiement par l'ID de transaction du fournisseur
     */
    public function getPaymentByTransactionId($transactionId) {
        $sql = "SELECT * FROM payment WHERE provider_transaction_id = :transaction_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':transaction_id' => $transactionId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }



    /**
     * Mettre à jour le statut d'un paiement
     */
    public function updatePaymentStatus($paymentId, $status) {
        $sql = "UPDATE payment SET payment_status = :status, updated_at = NOW() WHERE payment_id = :payment_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            ':payment_id' => $paymentId,
            ':status' => $status
        ]);
    }



    /**
     * Obtenir les statistiques de revenus
     */
    public function getTotalRevenue() {
        $sql = "SELECT SUM(amount) as total FROM payment WHERE payment_status = 'successful'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    public function getTodayRevenue() {
        $sql = "SELECT SUM(amount) as total FROM payment
                WHERE payment_status = 'successful' AND DATE(payment_date) = CURDATE()";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    public function getMonthRevenue() {
        $sql = "SELECT SUM(amount) as total FROM payment
                WHERE payment_status = 'successful'
                AND YEAR(payment_date) = YEAR(CURDATE())
                AND MONTH(payment_date) = MONTH(CURDATE())";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    public function getLastMonthRevenue() {
        $sql = "SELECT SUM(amount) as total FROM payment
                WHERE payment_status = 'successful'
                AND YEAR(payment_date) = YEAR(CURDATE() - INTERVAL 1 MONTH)
                AND MONTH(payment_date) = MONTH(CURDATE() - INTERVAL 1 MONTH)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'] ?? 0;
    }

    /**
     * Obtenir les paiements en attente
     */
    public function getPendingPayments() {
        $sql = "SELECT p.*, b.booking_id FROM payment p
                JOIN booking b ON p.booking_id = b.booking_id
                WHERE p.payment_status = 'pending'
                ORDER BY p.payment_date DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Récupérer tous les paiements avec pagination et filtres
     */
    public function getAllPayments(int $limit = 20, int $offset = 0, array $filters = []): array {
        $sql = "SELECT p.*, b.booking_id,
                       CONCAT(u.first_name, ' ', u.last_name) as customer_name,
                       u.email as customer_email
                FROM payment p
                JOIN booking b ON p.booking_id = b.booking_id
                JOIN user u ON b.user_id = u.user_id";

        $params = [];
        $whereClauses = [];

        if (!empty($filters['status'])) {
            $whereClauses[] = "p.payment_status = :status";
            $params[':status'] = $filters['status'];
        }

        if (!empty($filters['method'])) {
            $whereClauses[] = "p.payment_method = :method";
            $params[':method'] = $filters['method'];
        }

        if (!empty($filters['date_from'])) {
            $whereClauses[] = "DATE(p.payment_date) >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $whereClauses[] = "DATE(p.payment_date) <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }

        if (!empty($whereClauses)) {
            $sql .= " WHERE " . implode(' AND ', $whereClauses);
        }

        $sql .= " ORDER BY p.payment_date DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Compter le nombre total de paiements avec filtres
     */
    public function countPayments(array $filters = []): int {
        $sql = "SELECT COUNT(*) as total FROM payment p
                JOIN booking b ON p.booking_id = b.booking_id";

        $params = [];
        $whereClauses = [];

        if (!empty($filters['status'])) {
            $whereClauses[] = "p.payment_status = :status";
            $params[':status'] = $filters['status'];
        }

        if (!empty($filters['method'])) {
            $whereClauses[] = "p.payment_method = :method";
            $params[':method'] = $filters['method'];
        }

        if (!empty($filters['date_from'])) {
            $whereClauses[] = "DATE(p.payment_date) >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $whereClauses[] = "DATE(p.payment_date) <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }

        if (!empty($whereClauses)) {
            $sql .= " WHERE " . implode(' AND ', $whereClauses);
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)($result['total'] ?? 0);
    }

    /**
     * Créer un remboursement
     */
    public function createRefund(array $refundData): bool {
        $sql = "INSERT INTO payment_refund (payment_id, amount, refund_reference, processed_by, reason, created_at)
                VALUES (:payment_id, :amount, :refund_reference, :processed_by, :reason, NOW())";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            ':payment_id' => $refundData['payment_id'],
            ':amount' => $refundData['amount'],
            ':refund_reference' => $refundData['refund_reference'],
            ':processed_by' => $refundData['processed_by'],
            ':reason' => $refundData['reason'] ?? null
        ]);
    }

    /**
     * Mettre à jour un paiement
     */
    public function updatePayment(int $paymentId, array $data): bool {
        $allowedFields = ['payment_status', 'payment_reference', 'transaction_id'];
        $setParts = [];
        $params = [':payment_id' => $paymentId];

        foreach ($data as $key => $value) {
            if(in_array($key, $allowedFields)) {
                $setParts[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if(empty($setParts)) return false;

        $sql = "UPDATE payment SET ".implode(', ', $setParts)." WHERE payment_id = :payment_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Obtenir les tendances de revenus
     */
    public function getRevenueTrends($days = 30) {
        $sql = "SELECT DATE(payment_date) as date, SUM(amount) as revenue
                FROM payment
                WHERE payment_status = 'successful'
                AND payment_date >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
                GROUP BY DATE(payment_date)
                ORDER BY date ASC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([':days' => $days]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Rapport de revenus
     */
    public function getRevenueReport($period, $startDate, $endDate) {
        $groupBy = '';
        $dateFormat = '';

        switch ($period) {
            case 'day':
                $groupBy = 'DATE(payment_date)';
                $dateFormat = '%Y-%m-%d';
                break;
            case 'week':
                $groupBy = 'YEARWEEK(payment_date)';
                $dateFormat = '%Y-W%u';
                break;
            case 'month':
                $groupBy = 'YEAR(payment_date), MONTH(payment_date)';
                $dateFormat = '%Y-%m';
                break;
            default:
                $groupBy = 'DATE(payment_date)';
                $dateFormat = '%Y-%m-%d';
        }

        $sql = "SELECT DATE_FORMAT(payment_date, '{$dateFormat}') as period,
                       SUM(amount) as revenue,
                       COUNT(*) as transaction_count
                FROM payment
                WHERE payment_status = 'successful'";

        $params = [];

        if ($startDate) {
            $sql .= " AND payment_date >= :start_date";
            $params[':start_date'] = $startDate;
        }

        if ($endDate) {
            $sql .= " AND payment_date <= :end_date";
            $params[':end_date'] = $endDate;
        }

        $sql .= " GROUP BY {$groupBy} ORDER BY payment_date ASC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}