<?php
require_once __DIR__ . '/../Models/LocationModel.php';

/**
 * Contrôleur pour la gestion des lieux et points d'arrêt
 */
class LocationController {
    private $locationModel;

    public function __construct() {
        $this->locationModel = new LocationModel();
    }

    // -------------------------------
    // Gestion des Lieux (Locations)
    // -------------------------------

    /**
     * Liste tous les lieux (GET /v1/locations)
     * @param array $params Paramètres de la requête
     * @return void
     */
    public function getLocations($params = []): void {
        try {
            $locations = $this->locationModel->getLocations();
            if ($locations) {
                sendResponse(200, [
                    'success' => true,
                    'data' => $locations,
                    'total' => count($locations),
                    'message' => 'Locations récupérées avec succès'
                ]);
            } else {
                sendResponse(404, [
                    'success' => false,
                    'message' => 'Aucun lieu n\'a été trouvé'
                ]);
            }
        } catch (Exception $e) {
            sendResponse(500, [
                'success' => false,
                'message' => 'Erreur lors de la récupération des locations',
                'error' => $e->getMessage()
            ]);
        }
    }

    /** 
     * Liste tous les lieux (GET /v1/locations)
     * @return array Réponse JSON
     */
    public function getAllLocations(): array {
        $limit = $_GET['limit'] ?? 10;
        $page = $_GET['page'] ?? 1;
        $offset = ($page - 1) * $limit;
        
        return $this->locationModel->getAllLocations($limit, $offset);
    }

    /**
     * Récupère un lieu spécifique (GET /v1/locations/{id})
     * @param array $params Paramètres incluant l'ID
     * @return void
     */
    public function getLocationById($params = []): void {
        $locationId = isset($params['id']) ? (int)$params['id'] : 0;

        if ($locationId <= 0) {
            sendResponse(400, [
                'success' => false,
                'message' => 'ID de location invalide'
            ]);
            return;
        }

        try {
            $location = $this->locationModel->getLocationById($locationId);
            var_dump($location);
            exit;
            if (!$location) {
                sendResponse(404, [
                    'success' => false,
                    'message' => 'Lieu non trouvé'
                ]);
                return;
            }

            sendResponse(200, [
                'success' => true,
                'data' => $location,
                'message' => 'Location récupérée avec succès'
            ]);
        } catch (Exception $e) {
            sendResponse(500, [
                'success' => false,
                'message' => 'Erreur lors de la récupération de la location',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Récupère un lieu spécifique (GET /v1/locations/{id}) - Version legacy
     * @param int $locationId ID du lieu
     * @return array Réponse JSON
     */
    public function getLocation(int $locationId): array {
        $location = $this->locationModel->getLocationById($locationId);
        if(!$location) {
            http_response_code(404);
            return ['error' => 'Lieu non trouvé'];
        }
        return $location;
    }

    /**
     * Crée un nouveau lieu (POST /v1/locations)
     * @param array $request Données de la requête
     * @return array Réponse JSON
     */
    public function createLocation(array $request): array {
        AuthMiddleware::authenticate('admin');

        // Validation des données
        $requiredFields = ['location_name', 'region', 'country', 'time_zone', 'latitude', 'longitude'];
        foreach ($requiredFields as $field) {
            if(empty($request[$field])) {
                http_response_code(400);
                return ['error' => "Le champ $field est obligatoire"];
            }
        }

        try {
            $request['created_by'] = $_SERVER['AUTH_USER_ID']; // ID de l'admin
            $locationId = $this->locationModel->createLocation($request);
            return [
                'message' => 'Lieu créé avec succès',
                'location_id' => $locationId
            ];
        } catch (PDOException $e) {
            http_response_code(500);
            return ['error' => 'Erreur lors de la création du lieu'];
        }
    }

    /**
     * Met à jour un lieu (PUT /v1/locations/{id})
     * @param int $locationId ID du lieu
     * @param array $request Données à mettre à jour
     * @return array Réponse JSON
     */
    public function updateLocation(int $locationId, array $request): array {
        AuthMiddleware::authenticate('admin');

        $success = $this->locationModel->updateLocation($locationId, $request);
        if(!$success) {
            http_response_code(400);
            return ['error' => 'Aucune donnée valide à mettre à jour'];
        }
        return ['message' => 'Lieu mis à jour avec succès'];
    }

    /**
     * Supprime un lieu (DELETE /v1/locations/{id})
     * @param int $locationId ID du lieu
     * @return array Réponse JSON
     */
    public function deleteLocation(int $locationId): array {
        AuthMiddleware::authenticate('admin');

        $success = $this->locationModel->deleteLocation($locationId);
        if(!$success) {
            http_response_code(404);
            return ['error' => 'Lieu non trouvé'];
        }
        return ['message' => 'Lieu supprimé avec succès'];
    }

    // --------------------------------
    // Gestion des Points d'arrêt (Stops)
    // --------------------------------

    /**
     * Liste tous les points d'arrêt (GET /v1/stops)
     * @return array Réponse JSON
     */
    public function getAllStops(): array {
        $limit = $_GET['limit'] ?? 10;
        $page = $_GET['page'] ?? 1;
        $offset = ($page - 1) * $limit;
        
        return $this->locationModel->getAllStops($limit, $offset);
    }

    /**
     * Crée un nouveau point d'arrêt (POST /v1/stops)
     * @param array $request Données de la requête
     * @return array Réponse JSON
     */
    public function createStop(array $request): array {
        AuthMiddleware::authenticate('admin');

        $requiredFields = ['stop_name', 'location_id', 'latitude', 'longitude'];
        foreach ($requiredFields as $field) {
            if(empty($request[$field])) {
                http_response_code(400);
                return ['error' => "Le champ $field est obligatoire"];
            }
        }

        try {
            $request['created_by'] = $_SERVER['AUTH_USER_ID'];
            $stopId = $this->locationModel->createStop($request);
            return [
                'message' => 'Point d\'arrêt créé avec succès',
                'stop_id' => $stopId
            ];
        } catch (PDOException $e) {
            http_response_code(500);
            return ['error' => 'Erreur lors de la création du point d\'arrêt'];
        }
    }

    /**
     * Met à jour un point d'arrêt (PUT /v1/stops/{id})
     * @param int $stopId ID du stop
     * @param array $request Données à mettre à jour
     * @return array Réponse JSON
     */
    public function updateStop(int $stopId, array $request): array {
        AuthMiddleware::authenticate('admin');

        $success = $this->locationModel->updateStop($stopId, $request);
        if(!$success) {
            http_response_code(400);
            return ['error' => 'Aucune donnée valide à mettre à jour'];
        }
        return ['message' => 'Point d\'arrêt mis à jour avec succès'];
    }

    /**
     * Supprime un point d'arrêt (DELETE /v1/stops/{id})
     * @param int $stopId ID du stop
     * @return array Réponse JSON
     */
    public function deleteStop(int $stopId): array {
        AuthMiddleware::authenticate('admin');

        $success = $this->locationModel->deleteStop($stopId);
        if(!$success) {
            http_response_code(404);
            return ['error' => 'Point d\'arrêt non trouvé'];
        }
        return ['message' => 'Point d\'arrêt supprimé avec succès'];
    }
}