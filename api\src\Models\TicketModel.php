<?php
require_once __DIR__ . '/../Helpers/db.php';

class TicketModel {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * Récupérer un ticket par son code
     */
    public function getTicketByCode($ticketCode) {
        $sql = "SELECT t.*, s.seat_number, tr.estimated_departure_time, tr.estimated_arrival_time,
                       r.route_name, l1.location_name as departure_location, l2.location_name as destination_location,
                       st1.stop_name as boarding_stop, st2.stop_name as dropping_stop,
                       b.booking_status
                FROM ticket t
                JOIN seat s ON t.seat_id = s.seat_id
                JOIN trip tr ON t.trip_id = tr.trip_id
                JOIN route r ON tr.route_id = r.route_id
                JOIN location l1 ON r.departure_location_id = l1.location_id
                JOIN location l2 ON r.destination_location_id = l2.location_id
                JOIN booking b ON t.booking_id = b.booking_id
                JOIN stop st1 ON b.boarding_stop_id = st1.stop_id
                JOIN stop st2 ON b.dropping_stop_id = st2.stop_id
                WHERE t.ticket_code = :ticket_code";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':ticket_code' => $ticketCode]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Récupérer un ticket par son ID
     */
    public function getTicketById($ticketId) {
        $sql = "SELECT t.*, s.seat_number, tr.estimated_departure_time, tr.estimated_arrival_time,
                       r.route_name, l1.location_name as departure_location, l2.location_name as destination_location
                FROM ticket t
                JOIN seat s ON t.seat_id = s.seat_id
                JOIN trip tr ON t.trip_id = tr.trip_id
                JOIN route r ON tr.route_id = r.route_id
                JOIN location l1 ON r.departure_location_id = l1.location_id
                JOIN location l2 ON r.destination_location_id = l2.location_id
                WHERE t.ticket_id = :ticket_id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':ticket_id' => $ticketId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Marquer un ticket comme embarqué
     */
    public function markTicketAsBoarded($ticketId, $checkedBy) {
        $sql = "UPDATE ticket SET ticket_status = 'boarded', checked_by = :checked_by 
                WHERE ticket_id = :ticket_id";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            ':ticket_id' => $ticketId,
            ':checked_by' => $checkedBy
        ]);
    }
    
    /**
     * Créer un nouveau ticket
     */
    public function createTicket($data) {
        $sql = "INSERT INTO ticket (booking_id, trip_id, seat_id, passenger_name, passenger_email, 
                                   passenger_phone, ticket_code, ticket_status) 
                VALUES (:booking_id, :trip_id, :seat_id, :passenger_name, :passenger_email, 
                        :passenger_phone, :ticket_code, 'valid')";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':booking_id' => $data['booking_id'],
            ':trip_id' => $data['trip_id'],
            ':seat_id' => $data['seat_id'],
            ':passenger_name' => $data['passenger_name'],
            ':passenger_email' => $data['passenger_email'] ?? null,
            ':passenger_phone' => $data['passenger_phone'] ?? null,
            ':ticket_code' => $data['ticket_code']
        ]);
        
        return $this->db->lastInsertId();
    }
    
    /**
     * Générer un code de ticket unique
     */
    public function generateTicketCode() {
        do {
            $code = 'TKT' . strtoupper(substr(md5(uniqid(rand(), true)), 0, 8));
            $exists = $this->ticketCodeExists($code);
        } while ($exists);
        
        return $code;
    }
    
    /**
     * Vérifier si un code de ticket existe
     */
    private function ticketCodeExists($code) {
        $sql = "SELECT COUNT(*) FROM ticket WHERE ticket_code = :code";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':code' => $code]);
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * Récupérer les tickets d'une réservation
     */
    public function getTicketsByBooking($bookingId, $userId = null) {
        $sql = "SELECT t.*, s.seat_number 
                FROM ticket t
                JOIN seat s ON t.seat_id = s.seat_id
                JOIN booking b ON t.booking_id = b.booking_id
                WHERE t.booking_id = :booking_id";
        
        $params = [':booking_id' => $bookingId];
        
        if ($userId) {
            $sql .= " AND b.user_id = :user_id";
            $params[':user_id'] = $userId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Annuler les tickets d'une réservation
     */
    public function cancelTicketsByBooking($bookingId) {
        $sql = "UPDATE ticket SET ticket_status = 'cancelled' WHERE booking_id = :booking_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([':booking_id' => $bookingId]);
    }
    
    /**
     * Obtenir les statistiques des tickets pour un voyage
     */
    public function getTripTicketStats($tripId) {
        $sql = "SELECT 
                    COUNT(*) as total_tickets,
                    SUM(CASE WHEN ticket_status = 'valid' THEN 1 ELSE 0 END) as valid_tickets,
                    SUM(CASE WHEN ticket_status = 'boarded' THEN 1 ELSE 0 END) as boarded_tickets,
                    SUM(CASE WHEN ticket_status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_tickets
                FROM ticket 
                WHERE trip_id = :trip_id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':trip_id' => $tripId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
