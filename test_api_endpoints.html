<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Endpoints API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .endpoint h3 { margin-top: 0; color: #333; }
        .method { display: inline-block; padding: 3px 8px; border-radius: 3px; color: white; font-weight: bold; }
        .get { background-color: #28a745; }
        .post { background-color: #007bff; }
        .put { background-color: #ffc107; color: black; }
        .delete { background-color: #dc3545; }
        button { padding: 8px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .test-btn { background-color: #17a2b8; color: white; }
        .result { margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        pre { background-color: #f1f1f1; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test des Endpoints API - Bus Booking</h1>
    <p>Cette page permet de tester les nouveaux endpoints API pour remplacer les données mockées.</p>

    <!-- Test Tarification -->
    <div class="endpoint">
        <h3><span class="method get">GET</span> /api/v1/operator/pricing</h3>
        <p>Récupérer toutes les tarifications</p>
        <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/operator/pricing', null, 'pricing-result')">Tester</button>
        <div id="pricing-result" class="result" style="display: none;"></div>
    </div>

    <div class="endpoint">
        <h3><span class="method post">POST</span> /api/v1/operator/pricing</h3>
        <p>Créer une nouvelle tarification</p>
        <textarea id="pricing-data" rows="4" cols="50" placeholder='{"bus_type": "standard", "seat_type": "standard", "price": 1500}'></textarea><br>
        <button class="test-btn" onclick="testEndpoint('POST', '/api/v1/operator/pricing', 'pricing-data', 'pricing-create-result')">Tester</button>
        <div id="pricing-create-result" class="result" style="display: none;"></div>
    </div>

    <!-- Test Paiements -->
    <div class="endpoint">
        <h3><span class="method get">GET</span> /api/v1/operator/payments</h3>
        <p>Récupérer tous les paiements</p>
        <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/operator/payments', null, 'payments-result')">Tester</button>
        <div id="payments-result" class="result" style="display: none;"></div>
    </div>

    <!-- Test Utilisateurs -->
    <div class="endpoint">
        <h3><span class="method get">GET</span> /api/v1/operator/users</h3>
        <p>Récupérer tous les utilisateurs</p>
        <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/operator/users', null, 'users-result')">Tester</button>
        <div id="users-result" class="result" style="display: none;"></div>
    </div>

    <!-- Test Sièges -->
    <div class="endpoint">
        <h3><span class="method get">GET</span> /api/v1/operator/seats</h3>
        <p>Récupérer tous les sièges</p>
        <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/operator/seats', null, 'seats-result')">Tester</button>
        <div id="seats-result" class="result" style="display: none;"></div>
    </div>

    <!-- Test Plans de sièges -->
    <div class="endpoint">
        <h3><span class="method get">GET</span> /api/v1/operator/seat-plans</h3>
        <p>Récupérer tous les plans de sièges</p>
        <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/operator/seat-plans', null, 'seat-plans-result')">Tester</button>
        <div id="seat-plans-result" class="result" style="display: none;"></div>
    </div>

    <script>
        // Fonction pour tester un endpoint
        async function testEndpoint(method, url, dataElementId, resultElementId) {
            const resultDiv = document.getElementById(resultElementId);
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<p>Test en cours...</p>';
            resultDiv.className = 'result';

            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        // Ajouter le token d'authentification si nécessaire
                        // 'Authorization': 'Bearer ' + localStorage.getItem('token')
                    }
                };

                // Ajouter les données pour POST/PUT
                if (dataElementId && (method === 'POST' || method === 'PUT')) {
                    const dataElement = document.getElementById(dataElementId);
                    if (dataElement && dataElement.value) {
                        try {
                            options.body = dataElement.value;
                        } catch (e) {
                            resultDiv.innerHTML = '<p class="error">Erreur: Données JSON invalides</p>';
                            resultDiv.className = 'result error';
                            return;
                        }
                    }
                }

                const response = await fetch(url, options);
                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <p class="success">✅ Succès (${response.status})</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `
                        <p class="error">❌ Erreur (${response.status})</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <p class="error">❌ Erreur de connexion</p>
                    <pre>${error.message}</pre>
                `;
                resultDiv.className = 'result error';
            }
        }

        // Fonction pour tester tous les endpoints GET
        async function testAllGET() {
            const endpoints = [
                { url: '/api/v1/operator/pricing', result: 'pricing-result' },
                { url: '/api/v1/operator/payments', result: 'payments-result' },
                { url: '/api/v1/operator/users', result: 'users-result' },
                { url: '/api/v1/operator/seats', result: 'seats-result' },
                { url: '/api/v1/operator/seat-plans', result: 'seat-plans-result' }
            ];

            for (const endpoint of endpoints) {
                await testEndpoint('GET', endpoint.url, null, endpoint.result);
                await new Promise(resolve => setTimeout(resolve, 500)); // Pause de 500ms entre les tests
            }
        }
    </script>

    <div style="margin-top: 30px; padding: 20px; background-color: #e9ecef; border-radius: 5px;">
        <h3>Test automatique</h3>
        <button class="test-btn" onclick="testAllGET()">Tester tous les endpoints GET</button>
        <p><small>Note: Assurez-vous d'être connecté en tant qu'opérateur pour que les tests fonctionnent.</small></p>
    </div>
</body>
</html>
