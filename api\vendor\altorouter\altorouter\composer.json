{"name": "altorouter/altorouter", "description": "A lightning fast router for PHP", "keywords": ["router", "routing", "lightweight"], "homepage": "https://github.com/dannyvankooten/AltoRouter", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://dannyvankooten.com/"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/koenpunt"}, {"name": "niahoo", "homepage": "https://github.com/niahoo"}], "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "9.6.*", "squizlabs/php_codesniffer": "3.6.2"}, "autoload": {"classmap": ["AltoRouter.php"]}, "scripts": {"test": "vendor/bin/phpunit", "check-syntax": "find . -name '*.php' -not -path './vendor/*' -print0 | xargs -0 -n1 php -l"}}