<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Selects - EasyBus</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test du Peuplement des Selects</h1>
        <p class="text-muted">Cette page teste si les selects sont correctement peuplés avec les nouvelles fonctions unifiées.</p>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Selects de Filtres (Sections)</h3>
                <div class="mb-3">
                    <label for="routeDepartureFilter" class="form-label">Filtre Départ (Routes)</label>
                    <select class="form-select" id="routeDepartureFilter">
                        <option value="">Tous les départs</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="pricingRouteFilter" class="form-label">Filtre Route (Pricing)</label>
                    <select class="form-select" id="pricingRouteFilter">
                        <option value="">Tous les itinéraires</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="stopLocationFilter" class="form-label">Filtre Lieu (Stops)</label>
                    <select class="form-select" id="stopLocationFilter">
                        <option value="">Tous les lieux</option>
                    </select>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>Selects de Modaux</h3>
                <div class="mb-3">
                    <label for="stopLocationId" class="form-label">Lieu (Modal Stop)</label>
                    <select class="form-select" id="stopLocationId">
                        <option value="">Sélectionner un lieu...</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="routeDepartureLocationId" class="form-label">Départ (Modal Route)</label>
                    <select class="form-select" id="routeDepartureLocationId">
                        <option value="">Sélectionner...</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="routeDestinationLocationId" class="form-label">Destination (Modal Route)</label>
                    <select class="form-select" id="routeDestinationLocationId">
                        <option value="">Sélectionner...</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <button class="btn btn-primary" onclick="testLoadSectionData()">
                <i class="fas fa-play me-2"></i>Tester loadSectionData('routes')
            </button>
            <button class="btn btn-success" onclick="testEnsureAllDataLoaded()">
                <i class="fas fa-sync me-2"></i>Tester ensureAllDataLoadedAndUpdateSelects()
            </button>
            <button class="btn btn-info" onclick="checkSelectsContent()">
                <i class="fas fa-search me-2"></i>Vérifier le contenu des selects
            </button>
        </div>
        
        <div class="mt-4">
            <h4>Résultats des tests</h4>
            <div id="testResults" class="alert alert-info">
                Cliquez sur les boutons ci-dessus pour tester les fonctions.
            </div>
        </div>
    </div>

    <!-- Messages d'alerte -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="alertContainer"></div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/config.js"></script>
    <script src="../assets/js/auth.js"></script>
    <script src="../assets/js/modules/operator-crud.js"></script>
    
    <script>
        // Fonction de test pour loadSectionData
        async function testLoadSectionData() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test de loadSectionData en cours...';
            
            try {
                await loadSectionData('routes');
                resultsDiv.innerHTML = '<div class="text-success"><i class="fas fa-check"></i> loadSectionData(\'routes\') exécuté avec succès!</div>';
                setTimeout(checkSelectsContent, 1000);
            } catch (error) {
                resultsDiv.innerHTML = `<div class="text-danger"><i class="fas fa-times"></i> Erreur: ${error.message}</div>`;
            }
        }
        
        // Fonction de test pour ensureAllDataLoadedAndUpdateSelects
        async function testEnsureAllDataLoaded() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test de ensureAllDataLoadedAndUpdateSelects en cours...';
            
            try {
                await ensureAllDataLoadedAndUpdateSelects();
                resultsDiv.innerHTML = '<div class="text-success"><i class="fas fa-check"></i> ensureAllDataLoadedAndUpdateSelects() exécuté avec succès!</div>';
                setTimeout(checkSelectsContent, 1000);
            } catch (error) {
                resultsDiv.innerHTML = `<div class="text-danger"><i class="fas fa-times"></i> Erreur: ${error.message}</div>`;
            }
        }
        
        // Fonction pour vérifier le contenu des selects
        function checkSelectsContent() {
            const selects = [
                'routeDepartureFilter',
                'pricingRouteFilter', 
                'stopLocationFilter',
                'stopLocationId',
                'routeDepartureLocationId',
                'routeDestinationLocationId'
            ];
            
            let results = '<h5>État des selects:</h5>';
            
            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    const optionCount = select.options.length;
                    const hasData = optionCount > 1; // Plus que l'option par défaut
                    const status = hasData ? 'success' : 'warning';
                    const icon = hasData ? 'check' : 'exclamation-triangle';
                    
                    results += `<div class="text-${status}">
                        <i class="fas fa-${icon}"></i> 
                        ${selectId}: ${optionCount} option(s) ${hasData ? '✓' : '(vide)'}
                    </div>`;
                } else {
                    results += `<div class="text-danger">
                        <i class="fas fa-times"></i> ${selectId}: Élément non trouvé
                    </div>`;
                }
            });
            
            document.getElementById('testResults').innerHTML = results;
        }
        
        // Fonction d'alerte simplifiée pour les tests
        function showAlert(message, type = 'success') {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page de test chargée');
            checkSelectsContent();
        });
    </script>
</body>
</html>
