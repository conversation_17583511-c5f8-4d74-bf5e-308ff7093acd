/**
 * Module for displaying trip details
 */
import { formatUtils } from './formatUtils.js';
import { fetchTripDetails, fetchSeats, fetchTripStops } from './api.js';

/**
 * Gets status badge HTML
 * @param {string} status - Trip status
 * @returns {string} - HTML for badge
 */
function getStatusBadge(status) {
  const statusMap = {
    "planned": { label: "Planifié", class: "bg-primary" },
    "ongoing": { label: "En cours", class: "bg-info" },
    "completed": { label: "Terminé", class: "bg-success" },
    "delayed": { label: "Retardé", class: "bg-warning" },
    "cancelled": { label: "Ann<PERSON><PERSON>", class: "bg-danger" },
  };

  const { label, class: badgeClass } = statusMap[status] || { label: status, class: "" };
  return `<div class="badge ${badgeClass}">${label}</div>`;
}

/**
 * Renders stops for a trip
 * @param {Array} stops - List of stops
 * @returns {string} - HTML for stops
 */
function renderStops(stops) {
  return stops.map((stop) => `
    <div class="timeline-stop ${stop.stop_type}">
      <div class="fw-bold">${stop.stop_name}</div>
      <div>${stop.location_name}, ${stop.region}, ${stop.country}</div>
      <div class="text-muted">${formatUtils.formatFullDateTime(stop.arrival_time)}</div>
      <div class="small text-muted">${stop.address}</div>
      <div class="small">
        <span class="badge bg-light text-dark">
          ${stop.stop_type === "boarding" ? "Embarquement" :
          stop.stop_type === "dropping" ? "Débarquement" : "Arrêt"}
        </span>
      </div>
    </div>
  `).join('');
}

/**
 * Renders bus information
 * @param {Object} bus - Bus object
 * @returns {string} - HTML for bus info
 */
function renderBusInfo(bus) {
  return `
    <div class="col-md-6">
      <h5 class="mb-3">Informations sur le bus</h5>
      <div class="card mb-3">
        <div class="card-body">
          <div class="d-flex justify-content-between mb-2">
            <div>Marque & Modèle:</div>
            <div class="fw-bold">${bus.brand} ${bus.model}</div>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <div>Immatriculation:</div>
            <div class="fw-bold">${bus.registration_number}</div>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <div>Type:</div>
            <div class="fw-bold">${bus.bus_type === "standard" ? "Standard" : "VIP"}</div>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <div>Année:</div>
            <div class="fw-bold">${bus.year_manufactured}</div>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <div>Capacité:</div>
            <div class="fw-bold">${bus.capacity} sièges</div>
          </div>
          <div class="d-flex justify-content-between">
            <div>Disposition:</div>
            <div class="fw-bold">${bus.layout_details.rows} rangées × ${bus.layout_details.columns} colonnes</div>
          </div>
        </div>
      </div>

      <h5 class="mb-3">Commodités</h5>
      <div class="card">
        <div class="card-body">
          <div class="row">
            ${bus.amenities.map((amenity) => `
              <div class="col-md-6 mb-2">
                <div class="d-flex align-items-center">
                  <div class="me-2">
                    <i class="fas ${formatUtils.getAmenityIcon(amenity.amenity_name)} text-primary"></i>
                  </div>
                  <div>
                    <div class="fw-bold">${amenity.amenity_name}</div>
                    <div class="small text-muted">${amenity.description}</div>
                  </div>
                </div>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    </div>
  `;
}



/**
 * Renders all trip details
 * @param {Object} data - Trip details data
 * @returns {string} - HTML for trip details
 */
function renderAllDetails(data) {
  const { trip, route, bus } = data;

  if (!trip || !route || !bus) {
    return '<div class="alert alert-danger">Des informations sont manquantes pour afficher les détails.</div>';
  }

  return `
    <div class="details-content">
      <!-- Ajouter une croix de fermeture -->
      <div class="text-end mb-2">
        <button class="btn btn-sm btn-close" onclick="this.closest('.trip-details').style.display = 'none'"></button>
      </div>

      <!-- Itinéraire -->
      <div class="tab-content active" id="itinerary-content">
        <div class="row">
          <div class="col-md-6">
            <h5 class="mb-3">Points d'arrêt</h5>
            <div class="stops-timeline">
              ${renderStops(route.stops)}
            </div>
          </div>

          <div class="col-md-6">
            <h5 class="mb-3">Détails du trajet</h5>
            <div class="card mb-3">
              <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                  <div>Distance totale:</div>
                  <div class="fw-bold">${route.distance} km</div>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <div>Durée estimée:</div>
                  <div class="fw-bold">${formatUtils.formatDuration(route.duration)}</div>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <div>Départ estimé:</div>
                  <div class="fw-bold">${formatUtils.formatFullDateTime(trip.estimated_departure_time)}</div>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <div>Arrivée estimée:</div>
                  <div class="fw-bold">${formatUtils.formatFullDateTime(trip.estimated_arrival_time)}</div>
                </div>
                <div class="d-flex justify-content-between">
                  <div>Statut:</div>
                  ${getStatusBadge(trip.status)}
                </div>
              </div>
            </div>

            ${trip.tracking_link ? `
              <a href="${trip.tracking_link}" target="_blank" class="btn btn-outline-primary w-100 mb-3">
                <i class="fas fa-location-arrow me-2"></i> Suivre ce trajet en temps réel
              </a>
            ` : ''}

            <div class="map-container text-center">
              <div>
                <i class="fas fa-map-marked-alt fa-3x text-muted mb-2"></i>
                <p class="text-muted">Carte d'itinéraire</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Bus -->
      <div class="tab-content" id="bus-content">
        <div class="row">
          ${renderBusInfo(bus)}

          <div class="col-md-6">
            <h5 class="mb-3">Photos du bus</h5>
            ${bus.bus_photos.length > 0 ? bus.bus_photos.map(photo => `
              <img src="${photo}" alt="Photo du bus - ${bus.brand} ${bus.model}" class="bus-photo mb-3">
            `).join('') : `
              <div class="text-center p-4 bg-light rounded">
                <i class="fas fa-bus fa-3x text-muted mb-2"></i>
                <p class="text-muted">Aucune photo disponible</p>
              </div>
            `}
          </div>
        </div>
      </div>
      <!-- Voir les Sièges -->
      <div class="tab-content" id="seats-content">
        <div class="row">
          <div class="col-12">
            <div class="text-center p-4">
              <h5>Chargement des sièges...</h5>
              <p class="text-muted">Veuillez patienter pendant le chargement du plan des sièges.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

/**
 * Shows active tab content
 * @param {HTMLElement} tab - Tab element
 * @param {HTMLElement} detailsElement - Details container
 * @param {string} tabName - Tab name
 */
function showActiveTab(tab, detailsElement, tabName) {
  const tabs = detailsElement.parentElement.querySelectorAll('.details-tab');
  tabs.forEach(t => t.classList.remove('active'));
  
  tab.classList.add('active');
  
  const allTabContents = detailsElement.querySelectorAll('.tab-content');
  allTabContents.forEach(content => content.classList.remove('active'));
  
  const activeTabContent = detailsElement.querySelector(`#${tabName}-content`);
  if (activeTabContent) {
    activeTabContent.classList.add('active');
  } else {
    console.warn(`Aucun contenu trouvé pour l'onglet ${tabName}`);
  }
}

/**
 * Loads seat selection content into the seats tab
 * @param {number} tripId - Trip ID
 * @param {HTMLElement} detailsElement - Details container
 * @param {Object} trip - Trip data
 * @returns {Promise<void>}
 */
async function loadSeatsContent(tripId, detailsElement, trip) {
  const seatsContent = detailsElement.querySelector('#seats-content');
  if (!seatsContent) return;

  try {
    // Import the seat selection functions
    const { generateSeatMap, generateMultiStepForm } = await import('./seatSelection.js');

    const tripStops = await fetchTripStops(tripId);
    const seats = await fetchSeats(tripId);
    const { layout_details } = trip;

    seatsContent.innerHTML = `
      <div class="col-12 seat-instruction">
        Cliquez sur un siège disponible pour procéder à votre réservation.
      </div>
      <div class="row">
        <div class="bus-plan-container col-12 col-md-6 mb-3 mb-md-0">
          <div class="seat-legend">
            <div class="legend-item">
              <svg fill="#6c757d" width="25" height="25" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-10 -10 120 120">
                <path d="M90.443,34.848c-2.548,0-4.613,2.065-4.613,4.614v31.534c-0.284,0.098-0.57,0.179-0.846,0.313  c-0.081,0.037-4.414,2.11-11.406,4.046c-2.226-1.561-5.054-2.257-7.933-1.7c-10.579,2.052-20.845,2.078-31.411,0.065  c-2.85-0.537-5.646,0.146-7.857,1.68c-6.969-1.933-11.286-4.014-11.414-4.076c-0.259-0.128-0.526-0.205-0.792-0.297V39.46  c0-2.547-2.065-4.614-4.614-4.614c-2.548,0-4.613,2.066-4.613,4.614v37.678c0,0.222,0.034,0.431,0.064,0.644  c0.096,2.447,1.456,4.772,3.804,5.939c0.398,0.196,5.779,2.828,14.367,5.164c1.438,2.634,3.997,4.626,7.174,5.233  c6.498,1.235,13.021,1.863,19.394,1.863c6.521,0,13.2-0.655,19.851-1.944c3.143-0.607,5.675-2.575,7.109-5.173  c8.575-2.324,13.97-4.931,14.369-5.127c2.187-1.073,3.54-3.146,3.805-5.396c0.104-0.385,0.179-0.784,0.179-1.202V39.46  C95.059,36.913,92.992,34.848,90.443,34.848z M20.733,37.154l-0.001,29.092c0.918,0.355,2.034,0.771,3.371,1.215  c3.577-1.812,7.759-2.428,11.756-1.672c9.628,1.837,18.689,1.814,28.359-0.063c4.035-0.78,8.207-0.165,11.794,1.641  c1.23-0.411,2.274-0.793,3.151-1.132l0.017-29.083c0-5.198,3.85-9.475,8.843-10.226V12.861c0-2.548-1.927-3.75-4.613-4.615  c0,0-14.627-4.23-33.165-4.23c-18.543,0-33.739,4.23-33.739,4.23c-2.619,0.814-4.614,2.065-4.614,4.615v14.066  C16.883,27.678,20.733,31.956,20.733,37.154z"/>
              </svg>
              <span>Disponible</span>
            </div>
            <div class="legend-item">
              <svg fill="#f8b6bc" width="25" height="25" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-10 -10 120 120">
                <path d="M90.443,34.848c-2.548,0-4.613,2.065-4.613,4.614v31.534c-0.284,0.098-0.57,0.179-0.846,0.313  c-0.081,0.037-4.414,2.11-11.406,4.046c-2.226-1.561-5.054-2.257-7.933-1.7c-10.579,2.052-20.845,2.078-31.411,0.065  c-2.85-0.537-5.646,0.146-7.857,1.68c-6.969-1.933-11.286-4.014-11.414-4.076c-0.259-0.128-0.526-0.205-0.792-0.297V39.46  c0-2.547-2.065-4.614-4.614-4.614c-2.548,0-4.613,2.066-4.613,4.614v37.678c0,0.222,0.034,0.431,0.064,0.644  c0.096,2.447,1.456,4.772,3.804,5.939c0.398,0.196,5.779,2.828,14.367,5.164c1.438,2.634,3.997,4.626,7.174,5.233  c6.498,1.235,13.021,1.863,19.394,1.863c6.521,0,13.2-0.655,19.851-1.944c3.143-0.607,5.675-2.575,7.109-5.173  c8.575-2.324,13.97-4.931,14.369-5.127c2.187-1.073,3.54-3.146,3.805-5.396c0.104-0.385,0.179-0.784,0.179-1.202V39.46  C95.059,36.913,92.992,34.848,90.443,34.848z M20.733,37.154l-0.001,29.092c0.918,0.355,2.034,0.771,3.371,1.215  c3.577-1.812,7.759-2.428,11.756-1.672c9.628,1.837,18.689,1.814,28.359-0.063c4.035-0.78,8.207-0.165,11.794,1.641  c1.23-0.411,2.274-0.793,3.151-1.132l0.017-29.083c0-5.198,3.85-9.475,8.843-10.226V12.861c0-2.548-1.927-3.75-4.613-4.615  c0,0-14.627-4.23-33.165-4.23c-18.543,0-33.739,4.23-33.739,4.23c-2.619,0.814-4.614,2.065-4.614,4.615v14.066  C16.883,27.678,20.733,31.956,20.733,37.154z"/>
              </svg>
              <span>Occupé</span>
            </div>
            <div class="legend-item">
              <svg fill="#0d6efd" width="25" height="25" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-10 -10 120 120">
                <path d="M90.443,34.848c-2.548,0-4.613,2.065-4.613,4.614v31.534c-0.284,0.098-0.57,0.179-0.846,0.313  c-0.081,0.037-4.414,2.11-11.406,4.046c-2.226-1.561-5.054-2.257-7.933-1.7c-10.579,2.052-20.845,2.078-31.411,0.065  c-2.85-0.537-5.646,0.146-7.857,1.68c-6.969-1.933-11.286-4.014-11.414-4.076c-0.259-0.128-0.526-0.205-0.792-0.297V39.46  c0-2.547-2.065-4.614-4.614-4.614c-2.548,0-4.613,2.066-4.613,4.614v37.678c0,0.222,0.034,0.431,0.064,0.644  c0.096,2.447,1.456,4.772,3.804,5.939c0.398,0.196,5.779,2.828,14.367,5.164c1.438,2.634,3.997,4.626,7.174,5.233  c6.498,1.235,13.021,1.863,19.394,1.863c6.521,0,13.2-0.655,19.851-1.944c3.143-0.607,5.675-2.575,7.109-5.173  c8.575-2.324,13.97-4.931,14.369-5.127c2.187-1.073,3.54-3.146,3.805-5.396c0.104-0.385,0.179-0.784,0.179-1.202V39.46  C95.059,36.913,92.992,34.848,90.443,34.848z M20.733,37.154l-0.001,29.092c0.918,0.355,2.034,0.771,3.371,1.215  c3.577-1.812,7.759-2.428,11.756-1.672c9.628,1.837,18.689,1.814,28.359-0.063c4.035-0.78,8.207-0.165,11.794,1.641  c1.23-0.411,2.274-0.793,3.151-1.132l0.017-29.083c0-5.198,3.85-9.475,8.843-10.226V12.861c0-2.548-1.927-3.75-4.613-4.615  c0,0-14.627-4.23-33.165-4.23c-18.543,0-33.739,4.23-33.739,4.23c-2.619,0.814-4.614,2.065-4.614,4.615v14.066  C16.883,27.678,20.733,31.956,20.733,37.154z"/>
              </svg>
              <span>Sélectionné</span>
            </div>
          </div>
          <div class="bus-container">
            ${generateSeatMap(seats, layout_details)}
          </div>
        </div>
        <div class="col-12 col-md-6 stop-form-container">
          <h5>Suivez le Procédure</h5>
          <div id="multi-step-form">
            ${generateMultiStepForm(tripStops)}
          </div>
        </div>
      </div>
    `;

    // Store seats data for the booking process - make it accessible globally
    window.currentSeats = seats;

    // Also trigger a custom event to notify seatSelection.js
    const seatsLoadedEvent = new CustomEvent('seatsLoaded', {
      detail: { seats, tripId }
    });
    document.dispatchEvent(seatsLoadedEvent);

  } catch (error) {
    seatsContent.innerHTML = `
      <div class="alert alert-danger m-3">
        Erreur lors du chargement des sièges: ${error.message}
      </div>
    `;
  }
}

/**
 * Fetches and renders trip details
 * @param {number} tripId - Trip ID
 * @param {HTMLElement} detailsElement - Details container
 * @param {string} tabName - Tab name
 * @param {Object} trip - Trip data (optional, for seats tab)
 * @returns {Promise<void>}
 */
export async function fetchDetailsAndRender(tripId, detailsElement, tabName, trip = null) {
  try {
    const data = await fetchTripDetails(tripId);

    detailsElement.innerHTML = renderAllDetails(data);
    detailsElement.dataset.loaded = "true";

    const tab = detailsElement.parentElement.querySelector(`[data-tab="${tabName}"]`);
    showActiveTab(tab, detailsElement, tabName);

    // If it's the seats tab, load the seat selection content
    if (tabName === "seats" && trip) {
      await loadSeatsContent(tripId, detailsElement, trip);
    }
  } catch (error) {
    detailsElement.innerHTML = `<div class="alert alert-danger m-3">Erreur: ${error.message}</div>`;
  }
}

/**
 * Sets up trip details event listeners
 * @param {Array} allTrips - All trips data
 */
export function setupTripDetails(allTrips = []) {
  document.addEventListener("click", async function (e) {
    const tab = e.target.closest(".details-tab");
    if (!tab) return;

    const tripId = parseInt(tab.dataset.tripId);
    const tabName = tab.dataset.tab;
    const detailsElement = document.getElementById(`details-${tripId}`);
    const wasActive = tab.classList.contains("active");

    document.querySelectorAll(".trip-details").forEach((d) => {
      if (d !== detailsElement) d.style.display = "none";
    });

    const bookBtn = detailsElement.closest(".trip-card").querySelector(".book-btn");

    // Si c'est l'onglet "seats", synchroniser avec le bouton principal
    if (tabName === "seats") {
      // Si l'onglet seats est déjà actif et qu'on clique dessus, on le ferme
      if (wasActive) {
        detailsElement.style.display = "none";
        tab.classList.remove("active");
        bookBtn.textContent = "Choisir un siège";
        bookBtn.dataset.mode = "show-seats";
        return;
      }
      // Sinon, on active l'onglet seats et on change le bouton
      bookBtn.textContent = "Masquer les sièges";
      bookBtn.dataset.mode = "hide-seats";
    } else {
      // Pour les autres onglets, remettre le bouton en mode normal
      bookBtn.textContent = "Choisir un siège";
      bookBtn.dataset.mode = "show-seats";
    }

    detailsElement.dataset.mode = "tabs";

    if (!detailsElement.dataset.loaded) {
      const trip = allTrips.find(t => t.trip_id === tripId);
      await fetchDetailsAndRender(tripId, detailsElement, tabName, trip);
    } else {
      if (wasActive && tabName !== "seats") {
        detailsElement.style.display = "none";
        tab.classList.remove("active");
      } else {
        detailsElement.style.display = "block";
        showActiveTab(tab, detailsElement, tabName);

        // Si c'est l'onglet seats et qu'il n'a pas encore de contenu de sièges, le charger
        if (tabName === "seats") {
          const seatsContent = detailsElement.querySelector('#seats-content');
          if (seatsContent && !seatsContent.querySelector('.seat')) {
            const trip = allTrips.find(t => t.trip_id === tripId);
            if (trip) {
              await loadSeatsContent(tripId, detailsElement, trip);
            }
          }
        }
      }
    }
  });
}
