# Solution de Lazy Loading pour les Données des Modals

## Problème identifié

Les données nécessaires pour peupler les selects des formulaires n'étaient disponibles que si l'utilisateur avait préalablement visité la section correspondante. Par exemple :
- Le modal de voyage (`tripModal`) nécessite les données d'itinéraires, bus et utilisateurs
- Le modal de bus (`busModal`) nécessite les données de plans de sièges
- Le modal d'arrêt (`stopModal`) nécessite les données de lieux
- Le modal de tarification (`pricingModal`) nécessite les données d'itinéraires

## Solution implémentée

### 1. Fonctions de chargement conditionnel

Ajout de fonctions `ensureXxxLoaded()` qui vérifient si les données sont déjà chargées avant de les recharger :

```javascript
async function ensureLocationsLoaded() {
    if (!locationsData || locationsData.length === 0) {
        await loadLocations();
    }
}

async function ensureRoutesLoaded() {
    if (!routesData || routesData.length === 0) {
        await loadRoutes();
    }
}

async function ensureBusesLoaded() {
    if (!busesData || busesData.length === 0) {
        await loadBuses();
    }
}

async function ensureUsersLoaded() {
    if (!usersData || usersData.length === 0) {
        await loadUsers();
    }
}

async function ensureSeatPlansLoaded() {
    if (!seatPlansData || seatPlansData.length === 0) {
        await loadSeatPlans();
    }
}
```

### 2. Modification des fonctions d'ouverture de modals

Chaque fonction `showCreateXxxModal()` et `editXxx()` a été modifiée pour :

1. **Afficher un indicateur de chargement** pendant le chargement des données
2. **Charger les données nécessaires** avec `Promise.all()` pour optimiser les performances
3. **Restaurer le contenu original** du modal
4. **Peupler les selects** avec les données fraîchement chargées
5. **Configurer le formulaire** avec les valeurs appropriées

#### Exemple pour le modal de voyage :

```javascript
async function showCreateTripModal() {
    try {
        // Afficher un indicateur de chargement
        const modalElement = document.getElementById('tripModal');
        const modalBody = modalElement.querySelector('.modal-body');
        const originalContent = modalBody.innerHTML;
        
        modalBody.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div><p class="mt-2">Chargement des données...</p></div>';
        
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Charger les données nécessaires
        await Promise.all([
            ensureRoutesLoaded(),
            ensureBusesLoaded(),
            ensureUsersLoaded()
        ]);

        // Restaurer le contenu original
        modalBody.innerHTML = originalContent;

        // Configurer le formulaire
        document.getElementById('tripModalTitle').textContent = 'Nouveau Voyage';
        document.getElementById('tripForm').reset();
        document.getElementById('tripId').value = '';
        document.getElementById('tripStatus').value = 'planned';

        // Peupler les selects avec les données chargées
        populateRouteSelects();
        populateBusSelects();
        populateUserSelects();

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal voyage:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}
```

### 3. Mise à jour des fonctions de population des selects

La fonction `populateLocationSelects()` a été mise à jour pour inclure tous les selects qui utilisent les lieux :

```javascript
function populateLocationSelects() {
    const selects = [
        'stopLocationId', 
        'routeDepartureFilter', 
        'stopLocationFilter',
        'routeDepartureLocationId',
        'routeDestinationLocationId'
    ];
    // ... reste de la fonction
}
```

### 4. Exposition des variables globales pour le débogage

Les variables globales ont été exposées sur `window` pour faciliter le débogage et les tests :

```javascript
// Exposer les variables globalement pour le débogage et les tests
window.locationsData = locationsData;
window.routesData = routesData;
window.busesData = busesData;
window.usersData = usersData;
window.seatPlansData = seatPlansData;
```

### 5. Mise à jour des références globales

Chaque fonction de chargement met à jour la référence globale :

```javascript
async function loadLocations() {
    try {
        const response = await apiRequest('operator/locations');
        locationsData = response.locations || [];
        window.locationsData = locationsData; // Mettre à jour la référence globale
        displayLocations(locationsData);
        populateLocationSelects();
    } catch (error) {
        // ...
    }
}
```

## Modals modifiés

Les modals suivants ont été mis à jour avec le lazy loading :

1. **Modal Voyage** (`tripModal`)
   - Charge : itinéraires, bus, utilisateurs
   - Fonctions : `showCreateTripModal()`, `editTrip()`

2. **Modal Bus** (`busModal`)
   - Charge : plans de sièges
   - Fonctions : `showCreateBusModal()`, `editBus()`

3. **Modal Arrêt** (`stopModal`)
   - Charge : lieux
   - Fonctions : `showCreateStopModal()`, `editStop()`

4. **Modal Tarification** (`pricingModal`)
   - Charge : itinéraires
   - Fonctions : `showCreatePricingModal()`, `editPricing()`

5. **Modal Itinéraire** (`routeModal`)
   - Charge : lieux (pour départ/destination)
   - Fonctions : `showCreateRouteModal()`, `editRoute()`

## Avantages de cette solution

1. **Performance optimisée** : Les données ne sont chargées que quand nécessaire
2. **Cache en mémoire** : Les données déjà chargées ne sont pas rechargées
3. **UX améliorée** : Indicateur de chargement pendant l'attente
4. **Robustesse** : Gestion d'erreurs appropriée
5. **Maintenabilité** : Code modulaire et réutilisable

## Test de la solution

Un fichier de test `test_lazy_loading.html` a été créé pour vérifier le bon fonctionnement du lazy loading. Il permet de :
- Tester l'ouverture de chaque modal
- Visualiser l'état des données en temps réel
- Vérifier que les données sont chargées uniquement quand nécessaire

## Fichiers modifiés

- `public/assets/js/modules/operator-crud.js` : Implémentation principale du lazy loading
- `test_lazy_loading.html` : Fichier de test pour validation

Cette solution résout complètement le problème de disponibilité des données dans les selects des formulaires, tout en optimisant les performances et l'expérience utilisateur.
