// Configuration de l'API
const API_BASE_URL = 'api/v1';

// Variables globales
let bookingData = {};
let selectedPaymentMethod = '';

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    initializeCheckout();
    setupEventListeners();
});

// Initialiser le processus de checkout
function initializeCheckout() {
    try {
        // Récupérer les paramètres de l'URL
        const urlParams = new URLSearchParams(window.location.search);
        
        bookingData = {
            seats: urlParams.get('seats')?.split(',') || [],
            boarding: urlParams.get('boarding') || '',
            dropping: urlParams.get('dropping') || '',
            tripId: urlParams.get('tripId') || '',
            total: parseFloat(urlParams.get('total')) || 0
        };

        // Générer les formulaires de passagers
        generatePassengerForms();
        
        // Afficher le résumé de la réservation
        displayBookingSummary();
        
        // Charger les détails du voyage
        loadTripDetails();

    } catch (error) {
        console.error('Erreur lors de l\'initialisation:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Configurer les écouteurs d'événements
function setupEventListeners() {
    // Gestion du formulaire de checkout
    document.getElementById('checkoutForm').addEventListener('submit', handleCheckoutSubmit);
    
    // Gestion de la sélection du mode de paiement
    document.querySelectorAll('.payment-method').forEach(method => {
        method.addEventListener('click', function() {
            selectPaymentMethod(this.dataset.method);
        });
    });
}

// Générer les formulaires pour chaque passager
function generatePassengerForms() {
    const container = document.getElementById('passengersContainer');
    const seatCount = bookingData.seats.length;
    
    let html = '';
    
    for (let i = 0; i < seatCount; i++) {
        const seatNumber = bookingData.seats[i];
        html += `
            <div class="passenger-form">
                <h6 class="mb-3">
                    <i class="fas fa-user me-2"></i>Passager ${i + 1} - Siège ${seatNumber}
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="passenger_${i}_name" class="form-label">Nom complet *</label>
                            <input type="text" class="form-control" id="passenger_${i}_name" 
                                   name="passengers[${i}][name]" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="passenger_${i}_age" class="form-label">Âge</label>
                            <input type="number" class="form-control" id="passenger_${i}_age" 
                                   name="passengers[${i}][age]" min="1" max="120">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="passenger_${i}_gender" class="form-label">Genre</label>
                            <select class="form-select" id="passenger_${i}_gender" name="passengers[${i}][gender]">
                                <option value="">Sélectionner</option>
                                <option value="M">Homme</option>
                                <option value="F">Femme</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="passenger_${i}_phone" class="form-label">Téléphone</label>
                            <input type="tel" class="form-control" id="passenger_${i}_phone" 
                                   name="passengers[${i}][phone]">
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    container.innerHTML = html;
}

// Afficher le résumé de la réservation
function displayBookingSummary() {
    const seatCount = bookingData.seats.length;
    const seatPrice = bookingData.total / seatCount;
    
    // Mettre à jour les éléments d'affichage
    document.getElementById('passengerCount').textContent = seatCount;
    document.getElementById('seatPrice').textContent = formatCurrency(seatPrice);
    document.getElementById('totalAmount').textContent = formatCurrency(bookingData.total);
    document.getElementById('totalAmountDetail').textContent = formatCurrency(bookingData.total);
}

// Charger les détails du voyage
async function loadTripDetails() {
    try {
        const response = await apiRequest(`trips/${bookingData.tripId}`);
        const trip = response.trip;
        
        const summaryHtml = `
            <div class="mb-3">
                <h6 class="mb-2">${trip.route_name}</h6>
                <div class="d-flex justify-content-between mb-1">
                    <small class="text-muted">Départ:</small>
                    <small>${formatDateTime(trip.estimated_departure_time)}</small>
                </div>
                <div class="d-flex justify-content-between mb-1">
                    <small class="text-muted">Arrivée:</small>
                    <small>${formatDateTime(trip.estimated_arrival_time)}</small>
                </div>
                <div class="d-flex justify-content-between">
                    <small class="text-muted">Bus:</small>
                    <small>${trip.bus_registration || 'N/A'}</small>
                </div>
            </div>
            <div class="mb-2">
                <strong>Sièges sélectionnés:</strong>
                <div class="mt-1">
                    ${bookingData.seats.map(seat => `<span class="badge bg-primary me-1">${seat}</span>`).join('')}
                </div>
            </div>
        `;
        
        document.getElementById('tripSummary').innerHTML = summaryHtml;
        
    } catch (error) {
        console.error('Erreur lors du chargement des détails:', error);
        showAlert('Erreur lors du chargement des détails du voyage', 'warning');
    }
}

// Sélectionner un mode de paiement
function selectPaymentMethod(method) {
    // Retirer la sélection précédente
    document.querySelectorAll('.payment-method').forEach(el => {
        el.classList.remove('selected');
    });
    
    // Ajouter la sélection au nouveau mode
    document.querySelector(`[data-method="${method}"]`).classList.add('selected');
    selectedPaymentMethod = method;
}

// Gérer la soumission du formulaire
async function handleCheckoutSubmit(e) {
    e.preventDefault();
    
    if (!selectedPaymentMethod) {
        showAlert('Veuillez sélectionner un mode de paiement', 'warning');
        return;
    }
    
    try {
        // Afficher le modal de chargement
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();
        
        // Collecter les données du formulaire
        const formData = collectFormData();
        
        // Créer la réservation
        const bookingResponse = await createBooking(formData);
        
        // Traiter le paiement selon la méthode sélectionnée
        if (selectedPaymentMethod === 'fedapay') {
            await processOnlinePayment(bookingResponse.booking_id);
        } else if (selectedPaymentMethod === 'cash') {
            // Paiement en espèces - rediriger directement vers la confirmation
            redirectToConfirmation(bookingResponse);
        } else {
            // Autres méthodes de paiement
            await processAlternativePayment(bookingResponse.booking_id);
        }
        
    } catch (error) {
        console.error('Erreur lors de la réservation:', error);
        showAlert(error.message || 'Erreur lors de la réservation', 'danger');
        
        // Fermer le modal de chargement
        const loadingModal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
        if (loadingModal) {
            loadingModal.hide();
        }
    }
}

// Collecter les données du formulaire
function collectFormData() {
    const form = document.getElementById('checkoutForm');
    const formData = new FormData(form);
    
    // Collecter les données des passagers
    const passengers = [];
    for (let i = 0; i < bookingData.seats.length; i++) {
        const passenger = {
            name: document.getElementById(`passenger_${i}_name`).value,
            age: document.getElementById(`passenger_${i}_age`).value || null,
            gender: document.getElementById(`passenger_${i}_gender`).value || null,
            phone: document.getElementById(`passenger_${i}_phone`).value || null
        };
        passengers.push(passenger);
    }
    
    return {
        trip_id: parseInt(bookingData.tripId),
        boarding_stop_id: parseInt(bookingData.boarding),
        dropping_stop_id: parseInt(bookingData.dropping),
        seat_ids: bookingData.seats.map(seat => parseInt(seat)),
        passengers: passengers,
        contact_email: document.getElementById('contactEmail').value,
        contact_phone: document.getElementById('contactPhone').value,
        total_amount: bookingData.total,
        payment_method: selectedPaymentMethod
    };
}

// Créer la réservation
async function createBooking(data) {
    const response = await apiRequest('bookings', {
        method: 'POST',
        body: JSON.stringify(data)
    });
    
    return response;
}

// Traiter le paiement en ligne (FedaPay)
async function processOnlinePayment(bookingId) {
    try {
        const paymentResponse = await apiRequest('payments', {
            method: 'POST',
            body: JSON.stringify({
                booking_id: bookingId,
                amount: bookingData.total,
                payment_method: 'fedapay'
            })
        });
        
        // Rediriger vers FedaPay
        if (paymentResponse.payment_url) {
            window.location.href = paymentResponse.payment_url;
        } else {
            throw new Error('URL de paiement non reçue');
        }
        
    } catch (error) {
        throw new Error('Erreur lors du traitement du paiement: ' + error.message);
    }
}

// Traiter les paiements alternatifs
async function processAlternativePayment(bookingId) {
    // Pour l'instant, rediriger vers la confirmation
    // Dans une implémentation complète, intégrer les APIs des opérateurs mobiles
    showAlert('Paiement mobile en cours de développement. Redirection...', 'info');
    setTimeout(() => {
        window.location.href = `booking-confirmation.html?booking=${bookingId}`;
    }, 2000);
}

// Rediriger vers la page de confirmation
function redirectToConfirmation(bookingResponse) {
    const params = new URLSearchParams({
        booking: bookingResponse.booking_id,
        email: bookingResponse.search_info.email,
        phone: bookingResponse.search_info.phone
    });
    
    window.location.href = `booking-confirmation.html?${params.toString()}`;
}

// Retourner à la page précédente
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = 'search.html';
    }
}

// Fonctions utilitaires
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 0
    }).format(amount) + ' FCFA';
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Fonction pour faire des requêtes API (réutilisée depuis auth.js)
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}/${endpoint}`;
    const token = getAuthToken ? getAuthToken() : null;
    
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            ...(token && { 'Authorization': `Bearer ${token}` })
        }
    };
    
    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erreur de requête');
        }
        
        return data;
    } catch (error) {
        console.error('Erreur API:', error);
        throw error;
    }
}

// Fonction pour afficher les alertes
function showAlert(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" id="${alertId}">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHTML);
    
    // Auto-suppression après 5 secondes
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
