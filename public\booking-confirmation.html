<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmation de réservation - EasyBus</title>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="assets/css/all.min.css"/>
    <link rel="stylesheet" href="assets/css/style.css"/>
    <style>
        .confirmation-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
        }
        .confirmation-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            width: 100%;
        }
        .confirmation-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .confirmation-icon {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
        }
        .booking-details {
            padding: 2rem;
        }
        .detail-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .ticket-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: white;
        }
        .qr-code {
            text-align: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }
        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="confirmation-container">
        <div class="confirmation-card">
            <!-- En-tête de confirmation -->
            <div class="confirmation-header">
                <div class="confirmation-icon">
                    <i class="fas fa-check"></i>
                </div>
                <h2 class="mb-2">Réservation confirmée !</h2>
                <p class="mb-0">Votre réservation a été enregistrée avec succès</p>
            </div>

            <!-- Détails de la réservation -->
            <div class="booking-details">
                <!-- Informations de réservation -->
                <div class="detail-section">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle me-2 text-primary"></i>Informations de réservation
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Numéro de réservation:</strong> <span id="bookingId" class="text-primary">#</span></p>
                            <p><strong>Date de réservation:</strong> <span id="bookingDate"></span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Email de contact:</strong> <span id="contactEmail"></span></p>
                            <p><strong>Téléphone:</strong> <span id="contactPhone"></span></p>
                        </div>
                    </div>
                </div>

                <!-- Détails du voyage -->
                <div class="detail-section">
                    <h5 class="mb-3">
                        <i class="fas fa-route me-2 text-primary"></i>Détails du voyage
                    </h5>
                    <div id="tripDetails">
                        <!-- Sera rempli dynamiquement -->
                    </div>
                </div>

                <!-- Tickets -->
                <div class="detail-section">
                    <h5 class="mb-3">
                        <i class="fas fa-ticket-alt me-2 text-primary"></i>Vos tickets
                    </h5>
                    <div id="ticketsContainer">
                        <!-- Sera rempli dynamiquement -->
                    </div>
                </div>

                <!-- Instructions importantes -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-exclamation-circle me-2"></i>Instructions importantes</h6>
                    <ul class="mb-0">
                        <li>Présentez-vous à la gare <strong>30 minutes avant le départ</strong></li>
                        <li>Munissez-vous d'une <strong>pièce d'identité valide</strong></li>
                        <li>Conservez vos tickets jusqu'à la fin du voyage</li>
                        <li>En cas de problème, contactez notre service client</li>
                    </ul>
                </div>

                <!-- Boutons d'action -->
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="downloadTickets()">
                        <i class="fas fa-download me-2"></i>Télécharger les tickets
                    </button>
                    <button class="btn btn-outline-primary" onclick="sendTicketsByEmail()">
                        <i class="fas fa-envelope me-2"></i>Envoyer par email
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printTickets()">
                        <i class="fas fa-print me-2"></i>Imprimer
                    </button>
                </div>

                <div class="text-center mt-4">
                    <a href="index.html" class="btn btn-success">
                        <i class="fas fa-home me-2"></i>Retour à l'accueil
                    </a>
                    <a href="search-booking.html" class="btn btn-outline-primary ms-2">
                        <i class="fas fa-search me-2"></i>Mes réservations
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages d'alerte -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="alertContainer"></div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/auth.js"></script>
    <script src="assets/js/booking-confirmation.js"></script>
</body>
</html>

