<?php
require_once __DIR__ . '/../Helpers/db.php';

/**
 * Modèle pour la gestion des trajets (routes) et leurs points d'arrêt
 */
class RouteModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    // -------------------------------
    // Méthodes pour les Trajets (Routes)
    // -------------------------------

    /**
     * Récupère tous les trajets (GET /v1/routes)
     * @param int $limit Limite de résultats
     * @param int $offset Offset de pagination
     * @return array Liste des trajets
     */
    public function getAllRoutes(int $limit = 20, int $offset = 0): array {
        $sql = "SELECT r.*, dl.location_name AS departure_location_name, al.location_name AS arrival_location_name
                FROM route r
                JOIN location dl ON r.departure_location_id = dl.location_id
                JOIN location al ON r.destination_location_id = al.location_id
                LIMIT :limit OFFSET :offset";
        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Récupère un trajet par ID (GET /v1/routes/{id})
     * @param int $routeId ID du trajet
     * @return array|null Données du trajet ou null
     */
    public function getRouteById(int $routeId): ?array {
        $sql = "SELECT r.*, dl.location_name AS departure_location_name, al.location_name AS arrival_location_name
                FROM route r
                JOIN location dl ON r.departure_location_id = dl.location_id
                JOIN location al ON r.destination_location_id = al.location_id
                WHERE r.route_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$routeId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Crée un nouveau trajet (POST /v1/routes)
     * @param array $data Données du trajet
     * @return int ID du trajet créé
     * @throws PDOException
     */
    public function createRoute($data) {
        $sql = "INSERT INTO route (route_name, description, departure_location_id, destination_location_id, distance, duration, created_by) 
                VALUES (:route_name, :description, :departure_location_id, :destination_location_id, :distance, :duration, :created_by)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':route_name' => $data['route_name'],
            ':description' => $data['description'] ?? null,
            ':departure_location_id' => $data['departure_location_id'],
            ':destination_location_id' => $data['destination_location_id'],
            ':distance' => $data['distance'],
            ':duration' => $data['duration'],
            ':created_by' => $data['created_by']
        ]);
        return $this->db->lastInsertId();
    }

    /**
     * Met à jour un trajet (PUT /v1/routes/{id})
     * @param int $routeId ID du trajet
     * @param array $data Données à mettre à jour
     * @return bool Succès de l'opération
     */
    public function updateRoute(int $routeId, array $data): bool {
        $allowedFields = ['route_name', 'description', 'distance', 'duration', 'status'];
        $setParts = [];
        $params = [':route_id' => $routeId];

        foreach ($data as $key => $value) {
            if(in_array($key, $allowedFields)) {
                $setParts[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if(empty($setParts)) return false;

        $sql = "UPDATE route SET ".implode(', ', $setParts)." WHERE route_id = :route_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Supprime un trajet (DELETE /v1/routes/{id})
     * @param int $routeId ID du trajet
     * @return bool Succès de l'opération
     */
    public function deleteRoute(int $routeId): bool {
        $stmt = $this->db->prepare("DELETE FROM route WHERE route_id = ?");
        return $stmt->execute([$routeId]);
    }

    /**
     * Vérifie si le nom de la route existe déjà
     * @param string $route_name Nom de la route à vérifier
     * @return bool Existe ou non
     */
    public function routeNameExists(string $route_name): bool {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM route WHERE route_name = ?");
        $stmt->execute([$route_name]);
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Compter le nombre total de trajets (routes) avec filtres
     * @param array $filters Filtres de recherche (ex: search)
     * @return int Nombre total
     */
    public function countRoutes(array $filters = []): int {
        $sql = "SELECT COUNT(*) as total FROM route WHERE 1=1";
        $params = [];
        if (!empty($filters['search'])) {
            $sql .= " AND (route_name LIKE :search OR description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }
        $stmt = $this->db->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        $stmt->execute();
        $result = $stmt->fetch();
        return (int)$result['total'];
    }

    /**
     * Vérifie si un nom de trajet est unique (hors d'un ID donné)
     * @param string $route_name Nom du trajet
     * @param int|null $excludeId ID à exclure (pour update)
     * @return bool true si unique, false sinon
     */
    public function isRouteNameUnique(string $route_name, ?int $excludeId = null): bool {
        $sql = "SELECT COUNT(*) as count FROM route WHERE route_name = :route_name";
        $params = ['route_name' => $route_name];
        if ($excludeId) {
            $sql .= " AND route_id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        $stmt = $this->db->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['count'] == 0;
    }

    // ----------------------------------------
    // Méthodes pour les Points d'arrêt (RouteStops)
    // ----------------------------------------

    /**
     * Récupère les points d'arrêt d'un trajet (GET /v1/routes/{id}/stops)
     * @param int $routeId ID du trajet
     * @return array Liste des points d'arrêt
     */
    public function getRouteStops(int $routeId): array {
        $stmt = $this->db->prepare("
            SELECT rs.*, s.stop_name 
            FROM route_stop rs
            JOIN stop s ON rs.stop_id = s.stop_id 
            WHERE rs.route_id = ?
            ORDER BY rs.stop_order
        ");
        $stmt->execute([$routeId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Ajoute un point d'arrêt à un trajet (POST /v1/routes/{id}/stops)
     * @param int $routeId ID du trajet
     * @param array $data Données du point d'arrêt
     * @return int ID du point d'arrêt créé
     * @throws PDOException
     */
    public function addRouteStop(int $routeId, array $data): int {
        $sql = "INSERT INTO route_stop (
            route_id, 
            stop_id, 
            stop_order, 
            stop_type, 
            created_by
        ) VALUES (?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $routeId,
            $data['stop_id'],
            $data['stop_order'],
            $data['stop_type'],
            $data['created_by']
        ]);
        return $this->db->lastInsertId();
    }

    /**
     * Supprime un point d'arrêt d'un trajet (DELETE /v1/routes/{id}/stops/{stopId})
     * @param int $routeId ID du trajet
     * @param int $stopId ID du point d'arrêt
     * @return bool Succès de l'opération
     */
    public function deleteRouteStop(int $routeId, int $stopId): bool {
        $stmt = $this->db->prepare("DELETE FROM route_stop WHERE route_id = ? AND stop_id = ?");
        return $stmt->execute([$routeId, $stopId]);
    }
}
