/**
 * Module for city search functionality
 */
import { fetchCities } from './api.js';

/**
 * Filters the list of cities based on search text
 * @param {Array} cities - List of cities
 * @param {string} searchText - Text to search for
 * @returns {Array} - Filtered list of cities
 */
function filterCities(cities, searchText) {
  const lowerSearchText = searchText.toLowerCase();
  return cities.filter((city) =>
    city.location_name.toLowerCase().includes(lowerSearchText)
  );
}

/**
 * Generates HTML for the city dropdown
 * @param {Array} citiesList - List of cities to display
 * @param {number|null} excludeId - ID of city to exclude
 * @returns {string} - HTML for dropdown
 */
function generateCityDropdown(citiesList, excludeId = null) {
  return citiesList
    .filter((city) => city.location_id !== excludeId)
    .map(
      (city) => `
          <div class="city-item" data-id="${city.location_id}">
              <div class="city-name">${city.location_name}</div>
              <div class="city-details">${city.region}, ${city.country}</div>
          </div>
      `
    )
    .join("");
}

/**
 * Shows the city dropdown
 * @param {Array} cities - List of all cities
 * @param {string} inputId - ID of input field
 * @param {string} dropdownId - ID of dropdown
 * @param {number|null} excludeId - ID of city to exclude
 */
function showDropdown(cities, inputId, dropdownId, excludeId) {
  const dropdown = document.getElementById(dropdownId);
  const searchText = document.getElementById(inputId).value.trim();
  const filteredCities = filterCities(cities, searchText);
  dropdown.innerHTML = generateCityDropdown(filteredCities, excludeId);
  dropdown.classList.add("show");
}

/**
 * Hides all city dropdowns
 */
function hideDropdowns() {
  document.querySelectorAll(".city-dropdown").forEach((dropdown) => {
    dropdown.classList.remove("show");
  });
}

/**
 * Sets up city field functionality
 * @param {string} field - Field name ('departure' or 'destination')
 * @param {Array} cities - List of all cities
 */
function setupCityField(field, cities) {
  const input = document.getElementById(field);
  const dropdown = document.getElementById(`${field}-dropdown`);
  const hiddenInput = document.getElementById(`${field}-id`);

  input.addEventListener("focus", async () => {
    const otherField = field === "departure" ? "destination" : "departure";
    const excludeId = document.getElementById(`${otherField}-id`).value;
    showDropdown(cities, field, `${field}-dropdown`, excludeId ? parseInt(excludeId) : null);
  });

  input.addEventListener("input", () => {
    const otherField = field === "departure" ? "destination" : "departure";
    const excludeId = document.getElementById(`${otherField}-id`).value;
    showDropdown(cities, field, `${field}-dropdown`, excludeId ? parseInt(excludeId) : null);
  });

  dropdown.addEventListener("click", (e) => {
    const cityItem = e.target.closest(".city-item");
    if (cityItem) {
      const id = cityItem.dataset.id;
      const city = cities.find((c) => c.location_id == id);
      input.value = city.location_name;
      hiddenInput.value = id;
      hideDropdowns();
    }
  });

  // Clear hidden input when text input is cleared
  input.addEventListener("input", () => {
    if (input.value.trim() === "") {
      hiddenInput.value = "";
    }
  });
}

/**
 * Initializes city search functionality
 */
export async function initCitySearch() {
  try {
    const cities = await fetchCities();
    
    // Setup both city fields
    ["departure", "destination"].forEach(field => setupCityField(field, cities));
    
    // Handle clicks outside dropdowns
    document.addEventListener("mousedown", (e) => {
      const departureInput = document.getElementById("departure");
      const departureDropdown = document.getElementById("departure-dropdown");
      const destinationInput = document.getElementById("destination");
      const destinationDropdown = document.getElementById("destination-dropdown");
  
      if (
        !departureInput.contains(e.target) &&
        !departureDropdown.contains(e.target) &&
        !destinationInput.contains(e.target) &&
        !destinationDropdown.contains(e.target)
      ) {
        hideDropdowns();
      }
    });
    
    // Set today as default date
    const today = new Date().toISOString().split("T")[0];
    document.getElementById("departure-date").value = today;
    document.getElementById("departure-date").min = today;
    
  } catch (error) {
    console.error("Error initializing city search:", error);
  }
}
