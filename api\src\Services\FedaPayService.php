<?php
require_once __DIR__ . '/../../vendor/autoload.php';

class FedaPayService {
    private $publicKey;
    private $secretKey;
    private $environment;
    private $webhookSecret;
    
    public function __construct() {
        $this->publicKey = $_ENV['FEDAPAY_PUBLIC_KEY'] ?? '';
        $this->secretKey = $_ENV['FEDAPAY_SECRET_KEY'] ?? '';
        $this->environment = $_ENV['FEDAPAY_ENVIRONMENT'] ?? 'sandbox';
        $this->webhookSecret = $_ENV['FEDAPAY_WEBHOOK_SECRET'] ?? '';
        
        $this->initializeFedaPay();
    }
    
    private function initializeFedaPay() {
        // Configuration FedaPay
        // Note: Vous devrez installer le SDK FedaPay via composer
        // composer require fedapay/fedapay-php
        
        if (class_exists('FedaPay\FedaPay')) {
            \FedaPay\FedaPay::setApiKey($this->secretKey);
            \FedaPay\FedaPay::setEnvironment($this->environment);
        }
    }
    
    /**
     * Créer une transaction de paiement
     */
    public function createPayment($data) {
        try {
            if (!class_exists('FedaPay\Transaction')) {
                // Simulation si le SDK n'est pas installé
                return $this->simulatePayment($data);
            }
            
            $transaction = \FedaPay\Transaction::create([
                'description' => $data['description'],
                'amount' => $data['amount'],
                'currency' => [
                    'iso' => $data['currency'] ?? 'XOF'
                ],
                'callback_url' => $data['callback_url'] ?? '',
                'customer' => [
                    'firstname' => $data['customer']['firstname'],
                    'lastname' => $data['customer']['lastname'],
                    'email' => $data['customer']['email'],
                    'phone_number' => [
                        'number' => $data['customer']['phone'],
                        'country' => $data['customer']['country'] ?? 'BJ'
                    ]
                ]
            ]);
            
            return [
                'success' => true,
                'transaction_id' => $transaction->id,
                'payment_url' => $transaction->generateToken()->url,
                'reference' => $transaction->reference,
                'status' => $transaction->status
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Vérifier le statut d'une transaction
     */
    public function getTransactionStatus($transactionId) {
        try {
            if (!class_exists('FedaPay\Transaction')) {
                return $this->simulateTransactionStatus($transactionId);
            }
            
            $transaction = \FedaPay\Transaction::retrieve($transactionId);
            
            return [
                'success' => true,
                'transaction_id' => $transaction->id,
                'reference' => $transaction->reference,
                'status' => $transaction->status,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency->iso,
                'created_at' => $transaction->created_at,
                'updated_at' => $transaction->updated_at
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Traiter un webhook FedaPay
     */
    public function handleWebhook($payload, $signature) {
        try {
            // Vérifier la signature du webhook
            if (!$this->verifyWebhookSignature($payload, $signature)) {
                return [
                    'success' => false,
                    'error' => 'Signature invalide'
                ];
            }
            
            $data = json_decode($payload, true);
            
            if (!$data || !isset($data['entity'])) {
                return [
                    'success' => false,
                    'error' => 'Données invalides'
                ];
            }
            
            $event = $data['entity'];
            
            switch ($event['name']) {
                case 'transaction.approved':
                    return $this->handleTransactionApproved($event['data']);
                    
                case 'transaction.declined':
                    return $this->handleTransactionDeclined($event['data']);
                    
                case 'transaction.canceled':
                    return $this->handleTransactionCanceled($event['data']);
                    
                default:
                    return [
                        'success' => true,
                        'message' => 'Événement non traité: ' . $event['name']
                    ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Vérifier la signature du webhook
     */
    private function verifyWebhookSignature($payload, $signature) {
        if (empty($this->webhookSecret)) {
            return true; // Pas de vérification si pas de secret configuré
        }
        
        $expectedSignature = hash_hmac('sha256', $payload, $this->webhookSecret);
        return hash_equals($expectedSignature, $signature);
    }
    
    /**
     * Traiter une transaction approuvée
     */
    private function handleTransactionApproved($transactionData) {
        // Mettre à jour le statut du paiement dans la base de données
        // Confirmer la réservation
        // Envoyer l'email de confirmation
        
        return [
            'success' => true,
            'action' => 'transaction_approved',
            'transaction_id' => $transactionData['id']
        ];
    }
    
    /**
     * Traiter une transaction refusée
     */
    private function handleTransactionDeclined($transactionData) {
        // Mettre à jour le statut du paiement
        // Annuler la réservation temporaire
        // Envoyer une notification d'échec
        
        return [
            'success' => true,
            'action' => 'transaction_declined',
            'transaction_id' => $transactionData['id']
        ];
    }
    
    /**
     * Traiter une transaction annulée
     */
    private function handleTransactionCanceled($transactionData) {
        // Mettre à jour le statut du paiement
        // Annuler la réservation
        
        return [
            'success' => true,
            'action' => 'transaction_canceled',
            'transaction_id' => $transactionData['id']
        ];
    }
    
    /**
     * Simulation de paiement (pour les tests)
     */
    private function simulatePayment($data) {
        $transactionId = 'sim_' . uniqid();
        $reference = 'REF' . strtoupper(substr(md5($transactionId), 0, 8));
        
        return [
            'success' => true,
            'transaction_id' => $transactionId,
            'payment_url' => "https://checkout.fedapay.com/simulate/{$transactionId}",
            'reference' => $reference,
            'status' => 'pending'
        ];
    }
    
    /**
     * Simulation du statut de transaction
     */
    private function simulateTransactionStatus($transactionId) {
        // Simulation simple - en réalité, vous stockeriez l'état en base
        $statuses = ['pending', 'approved', 'declined'];
        $randomStatus = $statuses[array_rand($statuses)];
        
        return [
            'success' => true,
            'transaction_id' => $transactionId,
            'reference' => 'REF' . strtoupper(substr(md5($transactionId), 0, 8)),
            'status' => $randomStatus,
            'amount' => 10000,
            'currency' => 'XOF',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Obtenir l'URL de paiement pour une transaction
     */
    public function getPaymentUrl($transactionId) {
        if (!class_exists('FedaPay\Transaction')) {
            return "https://checkout.fedapay.com/simulate/{$transactionId}";
        }
        
        try {
            $transaction = \FedaPay\Transaction::retrieve($transactionId);
            $token = $transaction->generateToken();
            return $token->url;
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * Rembourser une transaction
     */
    public function refundTransaction($transactionId, $amount = null) {
        try {
            if (!class_exists('FedaPay\Transaction')) {
                return [
                    'success' => true,
                    'message' => 'Remboursement simulé',
                    'refund_id' => 'sim_refund_' . uniqid()
                ];
            }
            
            $transaction = \FedaPay\Transaction::retrieve($transactionId);
            $refund = $transaction->refund(['amount' => $amount]);
            
            return [
                'success' => true,
                'refund_id' => $refund->id,
                'amount' => $refund->amount,
                'status' => $refund->status
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
