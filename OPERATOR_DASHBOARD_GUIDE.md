# Guide du Tableau de Bord Opérateur - EasyBus

## Vue d'ensemble

Le tableau de bord opérateur EasyBus est une interface complète permettant aux opérateurs de bus de gérer tous les aspects de leur activité : flotte, itinéraires, réservations, tarification, et bien plus.

## Fonctionnalités Principales

### 🏢 **Gestion Géographique**

#### **Lieux/Villes**
- **Créer** : Ajouter de nouvelles villes avec coordonnées GPS
- **Modifier** : Mettre à jour les informations des lieux existants
- **Supprimer** : Retirer les lieux non utilisés
- **Filtrer** : Par pays, statut, ou recherche textuelle

#### **Arrêts**
- **Créer** : Ajouter des arrêts spécifiques dans chaque lieu
- **Modifier** : Mettre à jour les adresses et coordonnées
- **Supprimer** : Retirer les arrêts obsolètes
- **Filtrer** : Par lieu ou recherche textuelle

### 🚌 **Gestion de la Flotte**

#### **Commodités**
- **Créer** : Ajouter de nouvelles commodités (WiFi, Climatisation, etc.)
- **Modifier** : Mettre à jour les descriptions
- **Supprimer** : Retirer les commodités non utilisées
- **Assigner** : Lier les commodités aux bus

#### **Plans de Sièges**
- **Créer** : Définir des configurations de sièges
- **Visualiser** : Voir la disposition des sièges
- **Modifier** : Ajuster les configurations
- **Assigner** : Lier aux bus

#### **Bus**
- **Créer** : Ajouter de nouveaux bus à la flotte
- **Modifier** : Mettre à jour les informations (immatriculation, capacité, etc.)
- **Supprimer** : Retirer les bus hors service
- **Gérer les sièges** : Configuration individuelle des sièges
- **Assigner commodités** : Lier les commodités disponibles

### 🛣️ **Gestion des Voyages**

#### **Itinéraires**
- **Créer** : Définir de nouveaux itinéraires
- **Modifier** : Ajuster les points de passage
- **Supprimer** : Retirer les itinéraires obsolètes
- **Gérer les arrêts** : Ordre et type d'arrêts

#### **Voyages**
- **Planifier** : Créer de nouveaux voyages
- **Assigner** : Lier bus, chauffeur, contrôleur
- **Modifier** : Ajuster horaires et statuts
- **Suivre** : Monitoring en temps réel

#### **Tarification**
- **Créer** : Définir les prix par itinéraire
- **Modifier** : Ajuster selon la demande
- **Gérer** : Prix par type de bus et siège
- **Planifier** : Tarifs saisonniers

### 💼 **Gestion Opérationnelle**

#### **Réservations**
- **Consulter** : Vue détaillée des réservations
- **Modifier** : Changer le statut des réservations
- **Annuler** : Gérer les annulations
- **Filtrer** : Par statut, date, voyage

#### **Paiements**
- **Suivre** : Monitoring des paiements
- **Vérifier** : Statuts des transactions
- **Exporter** : Rapports financiers
- **Filtrer** : Par méthode, statut, date

#### **Utilisateurs**
- **Gérer** : Chauffeurs, contrôleurs, opérateurs
- **Modifier** : Informations et statuts
- **Assigner** : Rôles et permissions
- **Filtrer** : Par rôle et statut

#### **Validation des Tickets**
- **Scanner** : Validation par QR code
- **Saisie manuelle** : Code de ticket
- **Historique** : Suivi des validations

### 📊 **Analyses et Rapports**

#### **Tableau de Bord Principal**
- **Statistiques temps réel** : Réservations, revenus, voyages
- **Graphiques** : Évolution des revenus
- **Alertes** : Notifications importantes
- **Voyages du jour** : Vue d'ensemble quotidienne

#### **Rapports Détaillés**
- **Revenus** : Analyse par période
- **Occupation** : Taux de remplissage
- **Performance** : Métriques par itinéraire
- **Export** : Données au format Excel/PDF

## Architecture Technique

### **Frontend**
- **HTML5/CSS3** : Interface responsive
- **Bootstrap 5** : Framework CSS
- **JavaScript Vanilla** : Logique métier
- **Chart.js** : Graphiques et visualisations
- **Font Awesome** : Icônes

### **Backend**
- **PHP 8+** : Logique serveur
- **MySQL** : Base de données
- **Architecture MVC** : Séparation des préoccupations
- **API REST** : Communication frontend/backend
- **JWT** : Authentification sécurisée

### **Sécurité**
- **Authentification** : Vérification des rôles
- **Autorisation** : Accès basé sur les permissions
- **Validation** : Données côté client et serveur
- **Protection CSRF** : Sécurité des formulaires

## Installation et Configuration

### **Prérequis**
- PHP 8.0+
- MySQL 8.0+
- Serveur web (Apache/Nginx)
- Composer (gestionnaire de dépendances PHP)

### **Installation**
1. Cloner le repository
2. Installer les dépendances : `composer install`
3. Configurer la base de données
4. Importer le schéma : `mysql < bus_booking.sql`
5. Configurer les variables d'environnement
6. Démarrer le serveur web

### **Configuration**
- **Base de données** : Modifier `api/src/config/database.php`
- **API** : Ajuster `API_BASE_URL` dans les fichiers JS
- **Authentification** : Configurer JWT dans `api/src/config/auth.php`

## Utilisation

### **Connexion**
1. Accéder à `/operator/index.html`
2. Se connecter avec un compte opérateur
3. Vérification automatique des permissions

### **Navigation**
- **Menu latéral** : Navigation par catégorie
- **Sections organisées** : Géographie, Flotte, Voyages, Opérations, Analyses
- **Filtres** : Recherche et tri dans chaque section
- **Actions** : Boutons CRUD intuitifs

### **Workflow Typique**
1. **Configuration initiale** : Lieux → Arrêts → Commodités
2. **Gestion flotte** : Plans de sièges → Bus
3. **Planification** : Itinéraires → Voyages → Tarification
4. **Opérations** : Suivi réservations → Validation tickets
5. **Analyse** : Rapports et métriques

## API Endpoints

### **Lieux**
- `GET /v1/operator/locations` - Liste des lieux
- `POST /v1/operator/locations` - Créer un lieu
- `PUT /v1/operator/locations/{id}` - Modifier un lieu
- `DELETE /v1/operator/locations/{id}` - Supprimer un lieu

### **Arrêts**
- `GET /v1/operator/stops` - Liste des arrêts
- `POST /v1/operator/stops` - Créer un arrêt
- `PUT /v1/operator/stops/{id}` - Modifier un arrêt
- `DELETE /v1/operator/stops/{id}` - Supprimer un arrêt

### **Commodités**
- `GET /v1/operator/amenities` - Liste des commodités
- `POST /v1/operator/amenities` - Créer une commodité
- `PUT /v1/operator/amenities/{id}` - Modifier une commodité
- `DELETE /v1/operator/amenities/{id}` - Supprimer une commodité

### **Bus**
- `GET /v1/operator/buses` - Liste des bus
- `POST /v1/operator/buses` - Créer un bus
- `PUT /v1/operator/buses/{id}` - Modifier un bus
- `DELETE /v1/operator/buses/{id}` - Supprimer un bus

## Support et Maintenance

### **Logs**
- **Erreurs** : Consultables dans les outils développeur
- **API** : Logs serveur dans `/api/logs/`
- **Base de données** : Logs MySQL

### **Dépannage**
- **Problèmes de connexion** : Vérifier les credentials
- **Erreurs API** : Consulter les logs serveur
- **Performance** : Optimiser les requêtes DB

### **Mises à jour**
- **Code** : Git pull + composer update
- **Base de données** : Scripts de migration
- **Cache** : Vider le cache navigateur

## Bonnes Pratiques

### **Sécurité**
- Changer les mots de passe par défaut
- Utiliser HTTPS en production
- Sauvegarder régulièrement la base de données
- Mettre à jour les dépendances

### **Performance**
- Optimiser les images
- Utiliser la pagination
- Indexer les colonnes de recherche
- Monitorer les requêtes lentes

### **Maintenance**
- Sauvegardes automatiques
- Monitoring des erreurs
- Tests réguliers des fonctionnalités
- Documentation des changements

---

**Version** : 1.0  
**Dernière mise à jour** : Janvier 2025  
**Support** : <EMAIL>
