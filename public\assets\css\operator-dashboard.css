/* Styles spécifiques pour le tableau de bord opérateur */

/* Variables CSS */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --sidebar-width: 280px;
    --header-height: 70px;
}

/* Layout principal */
.operator-layout {
    min-height: 100vh;
    background-color: var(--light-color);
}

/* Header */
.operator-header {
    height: var(--header-height);
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
}

.operator-header .navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.operator-header .user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.operator-header .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Sidebar */
.operator-sidebar {
    width: var(--sidebar-width);
    background: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: var(--header-height);
    left: 0;
    bottom: 0;
    overflow-y: auto;
    z-index: 1020;
}

.operator-sidebar .nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--secondary-color);
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    padding: 0 1rem;
}

.operator-sidebar .nav-link {
    color: var(--dark-color);
    padding: 0.75rem 1rem;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.operator-sidebar .nav-link:hover {
    background-color: rgba(13, 110, 253, 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.operator-sidebar .nav-link.active {
    background-color: rgba(13, 110, 253, 0.15);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    font-weight: 600;
}

.operator-sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* Contenu principal */
.operator-content {
    margin-left: var(--sidebar-width);
    margin-top: var(--header-height);
    padding: 2rem;
    min-height: calc(100vh - var(--header-height));
}

/* Cartes de statistiques */
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.stat-card .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 0;
}

.stat-card .stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* Cartes de contenu */
.chart-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: none;
    margin-bottom: 1.5rem;
}

.chart-container h5 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Tableaux */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

/* Boutons */
.btn {
    border-radius: 8px;
    font-weight: 600;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    transform: translateY(-1px);
}

/* Alertes */
.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem 1.25rem;
}

.alert-item {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Formulaires */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Modales */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 2rem;
}

/* Grilles de commodités */
.amenities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.amenity-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.amenity-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

/* Progress bars */
.progress {
    height: 6px;
    border-radius: 3px;
    background-color: rgba(0, 0, 0, 0.1);
}

.progress-bar {
    border-radius: 3px;
    background: linear-gradient(90deg, var(--primary-color), #0056b3);
}

/* Responsive */
@media (max-width: 768px) {
    .operator-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .operator-sidebar.show {
        transform: translateX(0);
    }
    
    .operator-content {
        margin-left: 0;
        padding: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .chart-container {
        padding: 1rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-section {
    animation: fadeIn 0.5s ease;
}

/* Scrollbar personnalisée */
.operator-sidebar::-webkit-scrollbar {
    width: 6px;
}

.operator-sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.operator-sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.operator-sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* États de chargement */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Validation des tickets */
.validation-scanner {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.validation-input {
    font-size: 1.2rem;
    padding: 1rem;
    text-align: center;
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    margin-bottom: 1rem;
}

.validation-result {
    margin-top: 1.5rem;
    padding: 1.5rem;
    border-radius: 8px;
}

/* Icônes colorées */
.icon-primary { color: var(--primary-color); }
.icon-success { color: var(--success-color); }
.icon-warning { color: var(--warning-color); }
.icon-danger { color: var(--danger-color); }
.icon-info { color: var(--info-color); }

/* Backgrounds colorés */
.bg-primary-light { background-color: rgba(13, 110, 253, 0.1); }
.bg-success-light { background-color: rgba(25, 135, 84, 0.1); }
.bg-warning-light { background-color: rgba(255, 193, 7, 0.1); }
.bg-danger-light { background-color: rgba(220, 53, 69, 0.1); }
.bg-info-light { background-color: rgba(13, 202, 240, 0.1); }
