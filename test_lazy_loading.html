<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Lazy Loading - Operator Dashboard</title>
    <link href="public/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="public/assets/css/operator-dashboard.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test du Lazy Loading des Données</h1>
        <p>Ce test vérifie que les données sont chargées uniquement quand nécessaire pour les modals.</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Modal Voyage</h5>
                    </div>
                    <div class="card-body">
                        <p>Cliquez pour ouvrir le modal de création de voyage. Les données d'itinéraires, bus et utilisateurs doivent être chargées automatiquement.</p>
                        <button class="btn btn-primary" onclick="showCreateTripModal()">
                            <i class="fas fa-plus me-2"></i>Nouveau Voyage
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Modal Bus</h5>
                    </div>
                    <div class="card-body">
                        <p>Cliquez pour ouvrir le modal de création de bus. Les données de plans de sièges doivent être chargées automatiquement.</p>
                        <button class="btn btn-success" onclick="showCreateBusModal()">
                            <i class="fas fa-plus me-2"></i>Nouveau Bus
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Modal Arrêt</h5>
                    </div>
                    <div class="card-body">
                        <p>Cliquez pour ouvrir le modal de création d'arrêt. Les données de lieux doivent être chargées automatiquement.</p>
                        <button class="btn btn-info" onclick="showCreateStopModal()">
                            <i class="fas fa-plus me-2"></i>Nouvel Arrêt
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Modal Tarification</h5>
                    </div>
                    <div class="card-body">
                        <p>Cliquez pour ouvrir le modal de création de tarification. Les données d'itinéraires doivent être chargées automatiquement.</p>
                        <button class="btn btn-warning" onclick="showCreatePricingModal()">
                            <i class="fas fa-plus me-2"></i>Nouvelle Tarification
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>État des Données</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <strong>Lieux:</strong>
                                <span id="locationsStatus" class="badge bg-secondary">Non chargé</span>
                                <div id="locationsCount">0 éléments</div>
                            </div>
                            <div class="col-md-2">
                                <strong>Itinéraires:</strong>
                                <span id="routesStatus" class="badge bg-secondary">Non chargé</span>
                                <div id="routesCount">0 éléments</div>
                            </div>
                            <div class="col-md-2">
                                <strong>Bus:</strong>
                                <span id="busesStatus" class="badge bg-secondary">Non chargé</span>
                                <div id="busesCount">0 éléments</div>
                            </div>
                            <div class="col-md-2">
                                <strong>Utilisateurs:</strong>
                                <span id="usersStatus" class="badge bg-secondary">Non chargé</span>
                                <div id="usersCount">0 éléments</div>
                            </div>
                            <div class="col-md-2">
                                <strong>Plans sièges:</strong>
                                <span id="seatPlansStatus" class="badge bg-secondary">Non chargé</span>
                                <div id="seatPlansCount">0 éléments</div>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-sm btn-outline-primary" onclick="updateDataStatus()">
                                    <i class="fas fa-refresh"></i> Actualiser
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals (copies simplifiées pour le test) -->
    
    <!-- Modal pour Voyage -->
    <div class="modal fade" id="tripModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tripModalTitle">Nouveau Voyage</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="tripForm">
                        <input type="hidden" id="tripId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tripRouteId" class="form-label">Itinéraire *</label>
                                <select class="form-select" id="tripRouteId" required>
                                    <option value="">Sélectionner un itinéraire...</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tripBusId" class="form-label">Bus *</label>
                                <select class="form-select" id="tripBusId" required>
                                    <option value="">Sélectionner un bus...</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tripDriverId" class="form-label">Chauffeur *</label>
                                <select class="form-select" id="tripDriverId" required>
                                    <option value="">Sélectionner un chauffeur...</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tripControllerId" class="form-label">Contrôleur</label>
                                <select class="form-select" id="tripControllerId">
                                    <option value="">Sélectionner un contrôleur...</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveTrip()">Sauvegarder</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Bus -->
    <div class="modal fade" id="busModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="busModalTitle">Nouveau Bus</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="busForm">
                        <input type="hidden" id="busId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="busSeatPlanId" class="form-label">Plan de sièges</label>
                                <select class="form-select" id="busSeatPlanId">
                                    <option value="">Sélectionner un plan...</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveBus()">Sauvegarder</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Arrêt -->
    <div class="modal fade" id="stopModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="stopModalTitle">Nouvel Arrêt</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="stopForm">
                        <input type="hidden" id="stopId">
                        <div class="mb-3">
                            <label for="stopLocationId" class="form-label">Lieu *</label>
                            <select class="form-select" id="stopLocationId" required>
                                <option value="">Sélectionner un lieu...</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveStop()">Sauvegarder</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Tarification -->
    <div class="modal fade" id="pricingModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="pricingModalTitle">Nouvelle Tarification</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="pricingForm">
                        <input type="hidden" id="pricingId">
                        <div class="mb-3">
                            <label for="pricingRouteId" class="form-label">Itinéraire *</label>
                            <select class="form-select" id="pricingRouteId" required>
                                <option value="">Sélectionner un itinéraire...</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="savePricing()">Sauvegarder</button>
                </div>
            </div>
        </div>
    </div>

    <script src="public/assets/js/bootstrap.bundle.min.js"></script>
    <script src="public/assets/js/auth.js"></script>
    <script src="public/assets/js/api.js"></script>
    <script src="public/assets/js/modules/operator-crud.js"></script>
    
    <script>
        // Fonction pour mettre à jour l'état des données
        function updateDataStatus() {
            // Locations
            const locationsStatus = document.getElementById('locationsStatus');
            const locationsCount = document.getElementById('locationsCount');
            if (window.locationsData && window.locationsData.length > 0) {
                locationsStatus.textContent = 'Chargé';
                locationsStatus.className = 'badge bg-success';
                locationsCount.textContent = `${window.locationsData.length} éléments`;
            } else {
                locationsStatus.textContent = 'Non chargé';
                locationsStatus.className = 'badge bg-secondary';
                locationsCount.textContent = '0 éléments';
            }
            
            // Routes
            const routesStatus = document.getElementById('routesStatus');
            const routesCount = document.getElementById('routesCount');
            if (window.routesData && window.routesData.length > 0) {
                routesStatus.textContent = 'Chargé';
                routesStatus.className = 'badge bg-success';
                routesCount.textContent = `${window.routesData.length} éléments`;
            } else {
                routesStatus.textContent = 'Non chargé';
                routesStatus.className = 'badge bg-secondary';
                routesCount.textContent = '0 éléments';
            }
            
            // Buses
            const busesStatus = document.getElementById('busesStatus');
            const busesCount = document.getElementById('busesCount');
            if (window.busesData && window.busesData.length > 0) {
                busesStatus.textContent = 'Chargé';
                busesStatus.className = 'badge bg-success';
                busesCount.textContent = `${window.busesData.length} éléments`;
            } else {
                busesStatus.textContent = 'Non chargé';
                busesStatus.className = 'badge bg-secondary';
                busesCount.textContent = '0 éléments';
            }
            
            // Users
            const usersStatus = document.getElementById('usersStatus');
            const usersCount = document.getElementById('usersCount');
            if (window.usersData && window.usersData.length > 0) {
                usersStatus.textContent = 'Chargé';
                usersStatus.className = 'badge bg-success';
                usersCount.textContent = `${window.usersData.length} éléments`;
            } else {
                usersStatus.textContent = 'Non chargé';
                usersStatus.className = 'badge bg-secondary';
                usersCount.textContent = '0 éléments';
            }
            
            // Seat Plans
            const seatPlansStatus = document.getElementById('seatPlansStatus');
            const seatPlansCount = document.getElementById('seatPlansCount');
            if (window.seatPlansData && window.seatPlansData.length > 0) {
                seatPlansStatus.textContent = 'Chargé';
                seatPlansStatus.className = 'badge bg-success';
                seatPlansCount.textContent = `${window.seatPlansData.length} éléments`;
            } else {
                seatPlansStatus.textContent = 'Non chargé';
                seatPlansStatus.className = 'badge bg-secondary';
                seatPlansCount.textContent = '0 éléments';
            }
        }
        
        // Mettre à jour l'état au chargement
        document.addEventListener('DOMContentLoaded', function() {
            updateDataStatus();
            
            // Mettre à jour l'état toutes les 2 secondes
            setInterval(updateDataStatus, 2000);
        });
        
        // Fonctions de sauvegarde simplifiées pour le test
        function saveTrip() {
            console.log('Sauvegarde voyage (test)');
        }
        
        function saveBus() {
            console.log('Sauvegarde bus (test)');
        }
        
        function saveStop() {
            console.log('Sauvegarde arrêt (test)');
        }
        
        function savePricing() {
            console.log('Sauvegarde tarification (test)');
        }
    </script>
</body>
</html>
