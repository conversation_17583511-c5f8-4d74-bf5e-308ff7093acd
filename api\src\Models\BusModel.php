<?php
require_once __DIR__ . '/../Helpers/db.php';

/**
 * Modèle pour la gestion des bus, plans de sièges, sièges et équipements
 */
class BusModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    // --------------------------
    // Méthodes pour les Bus (Routes /v1/buses)
    // --------------------------

    /** GET /v1/buses */
    public function getBuses(int $limit = 10, int $offset = 0): array {
        $stmt = $this->db->prepare("SELECT * FROM bus LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** GET /v1/buses/{id} */
    public function getBusDetails(int $busId): ?array {
        $stmt = $this->db->prepare("SELECT * FROM bus WHERE bus_id = ?");
        $stmt->execute([$busId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    public function getBusbyId(int $busId): ?array {
        $stmt = $this->db->prepare("SELECT * FROM bus WHERE bus_id = ?");
        $stmt->execute([$busId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /** POST /v1/buses */
    public function createBus(array $data): int {
        $sql = "INSERT INTO bus (
            registration_number,
            brand,
            model,
            capacity,
            bus_type,
            seat_plan_id,
            year_manufactured,
            created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['registration_number'],
            $data['brand'],
            $data['model'],
            $data['capacity'],
            $data['bus_type'] ?? 'standard',
            $data['seat_plan_id'] ?? null,
            $data['year_manufactured'],
            $data['created_by']
        ]);
        return $this->db->lastInsertId();
    }

    /** PUT /v1/buses/{id} */
    public function updateBus(int $busId, array $data): bool {
        $allowedFields = ['brand', 'model', 'capacity', 'bus_type', 'seat_plan_id', 'status'];
        $setParts = [];
        $params = [':bus_id' => $busId];

        foreach ($data as $key => $value) {
            if(in_array($key, $allowedFields)) {
                $setParts[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if(empty($setParts)) return false;

        $sql = "UPDATE bus SET ".implode(', ', $setParts)." WHERE bus_id = :bus_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /** DELETE /v1/buses/{id} */
    public function deleteBus(int $busId): bool {
        $stmt = $this->db->prepare("DELETE FROM bus WHERE bus_id = ?");
        return $stmt->execute([$busId]);
    }

    // ------------------------------
    // Méthodes pour les Plans de sièges (Routes /v1/seat-plans)
    // ------------------------------

    /** GET /v1/seat-plans
     * @param int $limit Limit
     * @param int $offset Offset
     * @return array
    */
    public function getSeatPlans(int $limit = 10, int $offset = 0): array {
        $stmt = $this->db->prepare("SELECT * FROM seat_plan LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /** GET /v1/seat-plans/{id}
     * @param int $bus_id Bus ID
     * @return ?array or null
     */
    public function getSeatPlanDetails(int $seat_plan_id): ?array {
        $stmt = $this->db->prepare("SELECT * FROM seat_plan WHERE seat_plan_id = :seat_plan_id");
        $stmt->bindValue(':seat_plan_id', $seat_plan_id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }



    // ------------------------------
    // Méthodes pour les Sièges (Routes /v1/buses/{id}/seats)
    // ------------------------------

    /** GET /v1/buses/{id}/seats */
    public function getBusSeats(int $busId): array {
        $stmt = $this->db->prepare("SELECT seat_id, seat_number, seat_type FROM seat WHERE bus_id = ?");
        $stmt->execute([$busId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** POST /v1/buses/{id}/seats */
    public function addSeat(int $busId, array $data): int {
        $sql = "INSERT INTO seat (bus_id, seat_number, seat_type) VALUES (?, ?, ?)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$busId, $data['seat_number'], $data['seat_type'] ?? 'standard']);
        return $this->db->lastInsertId();
    }

    /**
     * Créer un siège pour un bus
     * @param array $data Données du siège (bus_id, seat_number, seat_type, status, created_by)
     * @return int seat_id créé
     */
    public function createSeat(array $data): int {
        $sql = "INSERT INTO seat (bus_id, seat_number, seat_type, status, created_by) VALUES (?, ?, ?, ?, ?)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['bus_id'],
            $data['seat_number'],
            $data['seat_type'] ?? 'standard',
            $data['status'] ?? 'active',
            $data['created_by'] ?? null
        ]);
        return $this->db->lastInsertId();
    }

    /**
     * Crée plusieurs sièges pour un bus en une seule transaction.
     * @param array $seatsArray Tableau de données des sièges.
     *                         Chaque élément doit contenir 'bus_id', 'seat_number', 'seat_type', 'status', 'created_by'.
     * @return array Tableau des IDs des sièges créés.
     * @throws PDOException
     */
    public function createMultipleSeats(array $seatsArray): array {
        if (empty($seatsArray)) {
            return [];
        }

        $sql = "INSERT INTO seat (bus_id, seat_number, seat_type, status, created_by) 
                VALUES (:bus_id, :seat_number, :seat_type, :status, :created_by)";
        
        $createdSeatIds = [];

        try {
            $this->db->beginTransaction();
            $stmt = $this->db->prepare($sql);

            foreach ($seatsArray as $seatData) {
                $stmt->execute([
                    ':bus_id' => $seatData['bus_id'],
                    ':seat_number' => $seatData['seat_number'],
                    ':seat_type' => $seatData['seat_type'] ?? 'standard',
                    ':status' => $seatData['status'] ?? 'active',
                    ':created_by' => $seatData['created_by'] ?? null
                ]);
                $createdSeatIds[] = $this->db->lastInsertId();
            }

            $this->db->commit();
            return $createdSeatIds;
        } catch (PDOException $e) {
            $this->db->rollBack();
            error_log("Erreur lors de la création de plusieurs sièges: " . $e->getMessage());
            throw $e; // Relancer pour que le contrôleur puisse la gérer
        }
    }

    /**
     * Mettre à jour un siège
     * @param int $seatId
     * @param array $data Données à mettre à jour (seat_number, seat_type, status, updated_by)
     * @return bool
     */
    public function updateSeat(int $seatId, array $data): bool {
        $allowedFields = ['seat_number', 'seat_type', 'status', 'updated_by'];
        $setParts = [];
        $params = [':seat_id' => $seatId];
        foreach ($data as $key => $value) {
            if (in_array($key, $allowedFields)) {
                $setParts[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }
        if (empty($setParts)) return false;
        $sql = "UPDATE seat SET ".implode(', ', $setParts)." WHERE seat_id = :seat_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Sauvegarde ou met à jour en masse les sièges pour un bus donné.
     * @param int $busId L'ID du bus.
     * @param array $seatsDataArray Tableau de données des sièges. Chaque élément doit contenir 'seat_number' et 'seat_type'.
     * @param int $userId L'ID de l'utilisateur effectuant l'opération.
     * @return array Nombre de sièges créés et mis à jour.
     */
    public function saveOrUpdateSeatsForBus(int $busId, array $seatsDataArray, int $userId): array {
        $createdCount = 0;
        $updatedCount = 0;

        // Préparer la requête pour vérifier l'existence d'un siège
        $checkSql = "SELECT seat_id, seat_type, status FROM seat WHERE bus_id = :bus_id AND seat_number = :seat_number";
        $checkStmt = $this->db->prepare($checkSql);

        // Préparer la requête d'insertion
        $insertSql = "INSERT INTO seat (bus_id, seat_number, seat_type, status, created_by, created_at) 
                      VALUES (:bus_id, :seat_number, :seat_type, :status, :user_id, CURRENT_TIMESTAMP)";
        $insertStmt = $this->db->prepare($insertSql);

        // Préparer la requête de mise à jour
        $updateSql = "UPDATE seat SET seat_type = :seat_type, status = :status, updated_by = :user_id, updated_at = CURRENT_TIMESTAMP
                      WHERE seat_id = :seat_id";
        $updateStmt = $this->db->prepare($updateSql);

        foreach ($seatsDataArray as $seatData) {
            $seatNumber = $seatData['seat_number'];
            $seatType = $seatData['seat_type'];
            $status = 'active'; // Par défaut, les sièges gérés via cette interface sont actifs

            $checkStmt->execute([':bus_id' => $busId, ':seat_number' => $seatNumber]);
            $existingSeat = $checkStmt->fetch(PDO::FETCH_ASSOC);

            if ($existingSeat) {
                // Mettre à jour le siège existant si nécessaire
                if ($existingSeat['seat_type'] !== $seatType || $existingSeat['status'] !== $status) {
                    $updateStmt->execute([
                        ':seat_type' => $seatType,
                        ':status' => $status,
                        ':user_id' => $userId,
                        ':seat_id' => $existingSeat['seat_id']
                    ]);
                    $updatedCount++;
                }
            } else {
                // Créer un nouveau siège
                $insertStmt->execute([
                    ':bus_id' => $busId,
                    ':seat_number' => $seatNumber,
                    ':seat_type' => $seatType,
                    ':status' => $status,
                    ':user_id' => $userId
                ]);
                $createdCount++;
            }
        }

        return ['created' => $createdCount, 'updated' => $updatedCount];
    }

    /**
     * Supprimer un siège
     * @param int $seatId
     * @return bool
     */
    public function deleteSeat(int $seatId): bool {
        $stmt = $this->db->prepare("DELETE FROM seat WHERE seat_id = ?");
        return $stmt->execute([$seatId]);
    }

    // ------------------------------
    // Méthodes pour les Équipements (Routes /v1/amenities)
    // ------------------------------

    /** GET /v1/amenities */
    public function getAllAmenities(int $limit = 10, int $offset = 0): array {
        $stmt = $this->db->prepare("SELECT amenity_id, amenity_name, description FROM amenity LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** POST /v1/amenities */
    public function createAmenity(array $data): int {
        $stmt = $this->db->prepare("INSERT INTO amenity (amenity_name, description, created_by) VALUES (?, ?, ?)");
        $stmt->execute([$data['amenity_name'], $data['description'], $data['created_by']]);
        return $this->db->lastInsertId();
    }

    // ------------------------------
    // Méthodes pour les Associations Bus-Équipements
    // ------------------------------

    /** GET /v1/buses/{id}/amenities */
    public function getBusAmenities(int $busId): array {
        $stmt = $this->db->prepare("
            SELECT a.amenity_name, a.description FROM bus_amenity ba
            JOIN amenity a ON ba.amenity_id = a.amenity_id
            WHERE ba.bus_id = ?
        ");
        $stmt->execute([$busId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** POST /v1/buses/{id}/amenities */
    public function addBusAmenity(int $busId, int $amenityId, int $adminId): bool {
        $stmt = $this->db->prepare("INSERT INTO bus_amenity (bus_id, amenity_id, created_by) VALUES (?, ?, ?)");
        return $stmt->execute([$busId, $amenityId, $adminId]);
    }

    /** DELETE /v1/buses/{id}/amenities/{amenityId} */
    public function removeBusAmenity(int $busId, int $amenityId): bool {
        $stmt = $this->db->prepare("DELETE FROM bus_amenity WHERE bus_id = ? AND amenity_id = ?");
        return $stmt->execute([$busId, $amenityId]);
    }

    // --------------------------
    // Méthodes statistiques avancées pour les Bus
    // --------------------------

    /** Nombre total de bus */
    public function getTotalBuses(): int {
        $stmt = $this->db->query("SELECT COUNT(*) as total FROM bus");
        $row = $stmt->fetch();
        return (int)($row['total'] ?? 0);
    }

    /** Nombre de bus actifs */
    public function getActiveBuses(): int {
        $stmt = $this->db->query("SELECT COUNT(*) as total FROM bus WHERE status = 'active'");
        $row = $stmt->fetch();
        return (int)($row['total'] ?? 0);
    }

    /** Nombre de bus en maintenance */
    public function getBusesInMaintenance(): array {
        $stmt = $this->db->query("SELECT * FROM bus WHERE status = 'under_maintenance'");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** Taux d'utilisation de la flotte (bus utilisés aujourd'hui / total) */
    public function getBusUtilizationRate(): float {
        $stmtTotal = $this->db->query("SELECT COUNT(*) as total FROM bus WHERE status = 'active'");
        $total = (int)($stmtTotal->fetch()['total'] ?? 0);
        $stmtUsed = $this->db->query("SELECT COUNT(DISTINCT bus_id) as used FROM trip WHERE DATE(estimated_departure_time) = CURDATE() AND status IN ('planned','ongoing','completed')");
        $used = (int)($stmtUsed->fetch()['used'] ?? 0);
        return $total > 0 ? round($used / $total * 100, 1) : 0.0;
    }

    /** Métriques de performance des bus (nombre de voyages, taux d'occupation moyen, etc.) */
    public function getBusPerformanceMetrics(): array {
        $sql = "SELECT b.bus_id, b.registration_number, b.brand, b.model, b.capacity, b.status,
            COUNT(t.trip_id) as total_trips,
            SUM(CASE WHEN t.status = 'completed' THEN 1 ELSE 0 END) as completed_trips,
            SUM(CASE WHEN t.status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_trips,
            ROUND(AVG(
                (SELECT COUNT(*) FROM booking bk WHERE bk.trip_id = t.trip_id AND bk.booking_status = 'confirmed')/b.capacity*100
            ),1) as avg_occupancy_rate
        FROM bus b
        LEFT JOIN trip t ON b.bus_id = t.bus_id
        GROUP BY b.bus_id
        ORDER BY total_trips DESC";
        $stmt = $this->db->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // --------------------------
    // Méthodes CRUD pour les sièges
    // --------------------------

    /** Récupérer tous les sièges avec pagination et filtres */
    public function getAllSeats(int $limit = 50, int $offset = 0, array $filters = []): array {
        $sql = "SELECT s.*, b.registration_number as bus_registration, b.brand, b.model
                FROM seat s
                JOIN bus b ON s.bus_id = b.bus_id";

        $params = [];
        $whereClauses = [];

        if (!empty($filters['bus_id'])) {
            $whereClauses[] = "s.bus_id = :bus_id";
            $params[':bus_id'] = $filters['bus_id'];
        }

        if (!empty($filters['seat_type'])) {
            $whereClauses[] = "s.seat_type = :seat_type";
            $params[':seat_type'] = $filters['seat_type'];
        }

        if (!empty($filters['status'])) {
            $whereClauses[] = "s.status = :status";
            $params[':status'] = $filters['status'];
        }

        if (!empty($whereClauses)) {
            $sql .= " WHERE " . implode(' AND ', $whereClauses);
        }

        $sql .= " ORDER BY b.registration_number, s.seat_number LIMIT :limit OFFSET :offset";

        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** Compter le nombre total de sièges avec filtres */
    public function countSeats(array $filters = []): int {
        $sql = "SELECT COUNT(*) as total FROM seat s
                JOIN bus b ON s.bus_id = b.bus_id";

        $params = [];
        $whereClauses = [];

        if (!empty($filters['bus_id'])) {
            $whereClauses[] = "s.bus_id = :bus_id";
            $params[':bus_id'] = $filters['bus_id'];
        }

        if (!empty($filters['seat_type'])) {
            $whereClauses[] = "s.seat_type = :seat_type";
            $params[':seat_type'] = $filters['seat_type'];
        }

        if (!empty($filters['status'])) {
            $whereClauses[] = "s.status = :status";
            $params[':status'] = $filters['status'];
        }

        if (!empty($whereClauses)) {
            $sql .= " WHERE " . implode(' AND ', $whereClauses);
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)($result['total'] ?? 0);
    }

    // --------------------------
    // Méthodes CRUD pour les plans de sièges
    // --------------------------

    /** Récupérer tous les plans de sièges avec pagination et filtres */
    public function getAllSeatPlans(int $limit = 20, int $offset = 0, array $filters = []): array {
        $sql = "SELECT sp.*,
                       COUNT(b.bus_id) as buses_using_plan
                FROM seat_plan sp
                LEFT JOIN bus b ON sp.seat_plan_id = b.seat_plan_id";

        $params = [];
        $whereClauses = [];

        if (!empty($filters['search'])) {
            $whereClauses[] = "sp.plan_config LIKE :search";
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        if (!empty($whereClauses)) {
            $sql .= " WHERE " . implode(' AND ', $whereClauses);
        }

        $sql .= " GROUP BY sp.seat_plan_id ORDER BY sp.seat_plan_id DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /** Compter le nombre total de plans de sièges avec filtres */
    public function countSeatPlans(array $filters = []): int {
        $sql = "SELECT COUNT(*) as total FROM seat_plan sp";

        $params = [];
        $whereClauses = [];

        if (!empty($filters['search'])) {
            $whereClauses[] = "sp.plan_config LIKE :search";
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        if (!empty($whereClauses)) {
            $sql .= " WHERE " . implode(' AND ', $whereClauses);
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)($result['total'] ?? 0);
    }

    /** Créer un nouveau plan de sièges */
    public function createSeatPlan(array $data): int {
        $sql = "INSERT INTO seat_plan (plan_config, layout_details, created_by)
                VALUES (:plan_config, :layout_details, :created_by)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':plan_config' => $data['plan_config'],
            ':layout_details' => is_array($data['layout_details']) ? json_encode($data['layout_details']) : $data['layout_details'],
            ':created_by' => $data['created_by'] ?? null
        ]);

        return $this->db->lastInsertId();
    }

    /** Mettre à jour un plan de sièges */
    public function updateSeatPlan(int $seatPlanId, array $data): bool {
        $allowedFields = ['plan_config', 'layout_details', 'updated_by'];
        $setParts = [];
        $params = [':seat_plan_id' => $seatPlanId];

        foreach ($data as $key => $value) {
            if(in_array($key, $allowedFields)) {
                if ($key === 'layout_details' && is_array($value)) {
                    $value = json_encode($value);
                }
                $setParts[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if(empty($setParts)) return false;

        $sql = "UPDATE seat_plan SET ".implode(', ', $setParts)." WHERE seat_plan_id = :seat_plan_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /** Supprimer un plan de sièges */
    public function deleteSeatPlan(int $seatPlanId): bool {
        // Vérifier d'abord si le plan est utilisé par des bus
        $sql = "SELECT COUNT(*) as count FROM bus WHERE seat_plan_id = :seat_plan_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':seat_plan_id' => $seatPlanId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result['count'] > 0) {
            throw new Exception("Ce plan de sièges est utilisé par " . $result['count'] . " bus et ne peut pas être supprimé");
        }

        $sql = "DELETE FROM seat_plan WHERE seat_plan_id = :seat_plan_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([':seat_plan_id' => $seatPlanId]);
    }
}