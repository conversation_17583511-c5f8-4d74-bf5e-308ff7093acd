<?php
require_once __DIR__ . '/response.php';

class Validator {
    // Vérifier si un champ est non vide
    public static function required($value, $fieldName) {
        if (empty($value)) {
            sendResponse(400, ['message' => "Le champ '$fieldName' est requis"]);
            exit;
        }
        return true;
    }

    // Vérifier si une valeur est un email valide
    public static function email($value, $fieldName) {
        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
            sendResponse(400, ['message' => "Le champ '$fieldName' doit être un email valide"]);
            exit;
        }
        return true;
    }

    // Vérifier si une valeur est un entier positif
    public static function positiveInt($value, $fieldName) {
        if (!is_numeric($value) || $value <= 0) {
            sendResponse(400, ['message' => "Le champ '$fieldName' doit être un entier positif"]);
            exit;
        }
        return true;
    }

    // Vérifier si une date est valide et dans le futur
    public static function futureTime($value, $fieldName) {
        $date = DateTime::createFromFormat('Y-m-d H:i:s', $value);
        if (!$date || $date < new DateTime()) {
            sendResponse(400, ['message' => "Le champ '$fieldName' doit être une date future valide (YYYY-MM-DD HH:MM:SS)"]);
            exit;
        }
        return true;
    }

    public static function futureDate($value, $fieldName) {
        $date = DateTime::createFromFormat('Y-m-d', $value);
        $today = new DateTime();
        $today->setTime(0, 0, 0);
        if (!$date || $date < $today) {
            sendResponse(400, ['message' => "Le champ '$fieldName' doit être une date actuelle ou future valide (YYYY-MM-DD)"]);
            exit;
        }
        return true;
    }
}

// Fonctions utilitaires pour valider un tableau de données
function validateFields($data, $rules) {
    foreach ($rules as $field => $rule) {
        $value = $data[$field] ?? null;
        switch ($rule) {
            case 'required':
                Validator::required($value, $field);
                break;
            case 'email':
                Validator::email($value, $field);
                break;
            case 'positiveInt':
                Validator::positiveInt($value, $field);
                break;
            case 'futureDate':
                Validator::futureDate($value, $field);
                break;
            case 'futureTime':
                Validator::futureDate($value, $field);
                break;
        }
    }
}