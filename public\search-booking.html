<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retrouver ma réservation - EasyBus</title>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="assets/css/all.min.css"/>
    <link rel="stylesheet" href="assets/css/style.css"/>
    <style>
        .search-container {
            min-height: 80vh;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        .search-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 2rem;
            max-width: 500px;
            width: 100%;
        }
        .search-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            color: white;
            font-size: 2rem;
        }
        .booking-result {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #28a745;
        }
        .booking-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .status-confirmed { background-color: #d4edda; color: #155724; }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-cancelled { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-bus text-primary me-2"></i>EasyBus
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="auth.html">Se connecter</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="search-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="search-card">
                        <div class="search-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        
                        <div class="text-center mb-4">
                            <h2 class="fw-bold text-primary">Retrouver ma réservation</h2>
                            <p class="text-muted">Saisissez votre email ou numéro de téléphone pour retrouver vos réservations</p>
                        </div>

                        <form id="searchForm">
                            <div class="mb-3">
                                <label for="searchInput" class="form-label">Email ou numéro de téléphone</label>
                                <input type="text" class="form-control form-control-lg" id="searchInput" 
                                       placeholder="<EMAIL> ou +229 XX XX XX XX" required>
                            </div>

                            <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                                <i class="fas fa-search me-2"></i>Rechercher mes réservations
                            </button>
                        </form>

                        <div class="text-center">
                            <p class="text-muted mb-2">Vous avez un compte ?</p>
                            <a href="auth.html" class="btn btn-outline-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Résultats de recherche -->
    <div class="container my-5" id="resultsSection" style="display: none;">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3><i class="fas fa-list me-2"></i>Vos réservations</h3>
                    <button class="btn btn-outline-secondary" onclick="newSearch()">
                        <i class="fas fa-search me-2"></i>Nouvelle recherche
                    </button>
                </div>
                
                <div id="bookingsContainer">
                    <!-- Les résultats seront affichés ici -->
                </div>
            </div>
        </div>
    </div>

    <!-- Messages d'alerte -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="alertContainer"></div>
    </div>

    <!-- Modal détails réservation -->
    <div class="modal fade" id="bookingModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Détails de la réservation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="bookingModalBody">
                    <!-- Contenu du modal -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-primary" onclick="downloadTickets()">
                        <i class="fas fa-download me-2"></i>Télécharger les tickets
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/search-booking.js"></script>
</body>
</html>

