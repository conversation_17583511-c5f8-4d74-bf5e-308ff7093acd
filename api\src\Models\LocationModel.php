<?php
require_once __DIR__ . '/../Helpers/db.php';

/**
 * Modèle pour la gestion des lieux (locations)
 */
class LocationModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    // -------------------------------------------
    // Méthodes pour les Lieux (Routes /locations)
    // -------------------------------------------

    /**
     * Récupère tous les lieux (GET /v1/locations)
     * @param int $limit  Limite de résultats
     * @param int $offset Offset de pagination
     * @return array Liste des lieux
     */
    public function getLocations(): ?array {
        $sql = "SELECT location_id, location_name, region, country FROM location WHERE status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC) ?: null;
    }
    
    /**
     * Récupère tous les lieux avec filtres et pagination
     * @param int $limit  Limite de résultats
     * @param int $offset Offset de pagination
     * @param array $filters Filtres à appliquer
     * @return array Liste des lieux
     */
    public function getAllLocations(int $limit = 50, int $offset = 0, array $filters = []): array {
        $sql = "SELECT location_id, location_name, region, country, time_zone,
                       ST_X(coordinates) as longitude, ST_Y(coordinates) as latitude,
                       status, created_at, updated_at
                FROM location WHERE 1=1";

        $params = [];

        // Appliquer les filtres
        if (!empty($filters['status'])) {
            $sql .= " AND status = :status";
            $params['status'] = $filters['status'];
        }

        if (!empty($filters['country'])) {
            $sql .= " AND country = :country";
            $params['country'] = $filters['country'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (location_name LIKE :search OR region LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        $sql .= " ORDER BY location_name ASC LIMIT :limit OFFSET :offset";

        $stmt = $this->db->prepare($sql);

        // Bind des paramètres
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        $stmt->bindValue(':limit', (int)$limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', (int)$offset, PDO::PARAM_INT);

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Récupère un lieu par ID (GET /v1/locations/{id})
     * @param int $locationId ID du lieu
     * @return array|null Données du lieu ou null
     */
    public function getLocationById(int $locationId): ?array {
        $sql = "SELECT location_id, location_name, region, country, time_zone,
                       ST_X(coordinates) as longitude, ST_Y(coordinates) as latitude,
                       status, created_at, updated_at
                FROM location WHERE location_id = :location_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':location_id', $locationId, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Crée un nouveau lieu (POST /v1/locations)
     * @param array $data Données du lieu
     * @return int ID du lieu créé
     */
    public function createLocation(array $data): int {
        $sql = "INSERT INTO location (location_name, region, country, time_zone, coordinates, status, created_by)
                VALUES (:location_name, :region, :country, :time_zone, POINT(:longitude, :latitude), :status, :created_by)";

        $status = $data['status'] ?? 'active';
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':location_name', $data['location_name']);
        $stmt->bindParam(':region', $data['region']);
        $stmt->bindParam(':country', $data['country']);
        $stmt->bindParam(':time_zone', $data['time_zone']);
        $stmt->bindParam(':longitude', $data['longitude']);
        $stmt->bindParam(':latitude', $data['latitude']);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':created_by', $data['created_by']);

        $stmt->execute();
        return $this->db->lastInsertId();
    }

    /**
     * Met à jour un lieu (PUT /v1/locations/{id})
     * @param int $locationId ID du lieu
     * @param array $data Données à mettre à jour
     * @return bool Succès de l'opération
     */
    public function updateLocation(int $locationId, array $data): bool {
        $sql = "UPDATE location SET
                location_name = :location_name,
                region = :region,
                country = :country,
                time_zone = :time_zone,
                coordinates = POINT(:longitude, :latitude),
                status = :status,
                updated_by = :updated_by,
                updated_at = CURRENT_TIMESTAMP
                WHERE location_id = :location_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':location_id', $locationId, PDO::PARAM_INT);
        $stmt->bindParam(':location_name', $data['location_name']);
        $stmt->bindParam(':region', $data['region']);
        $stmt->bindParam(':country', $data['country']);
        $stmt->bindParam(':time_zone', $data['time_zone']);
        $stmt->bindParam(':longitude', $data['longitude']);
        $stmt->bindParam(':latitude', $data['latitude']);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':updated_by', $data['updated_by']);

        return $stmt->execute();
    }

    /**
     * Supprime un lieu (DELETE /v1/locations/{id})
     * @param int $locationId ID du lieu
     * @return bool Succès de l'opération
     */
    public function deleteLocation(int $locationId): bool {
        // Vérifier s'il y a des arrêts liés
        $checkSql = "SELECT COUNT(*) as count FROM stop WHERE location_id = :location_id";
        $checkStmt = $this->db->prepare($checkSql);
        $checkStmt->bindParam(':location_id', $locationId, PDO::PARAM_INT);
        $checkStmt->execute();
        $result = $checkStmt->fetch();

        if ($result['count'] > 0) {
            throw new Exception('Impossible de supprimer ce lieu car il contient des arrêts');
        }

        $sql = "DELETE FROM location WHERE location_id = :location_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':location_id', $locationId, PDO::PARAM_INT);

        return $stmt->execute();
    }

    /**
     * Récupérer tous les pays
     */
    public function getCountries(): array {
        $sql = "SELECT DISTINCT country FROM location ORDER BY country";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    /**
     * Récupérer les lieux actifs pour les selects
     */
    public function getActiveLocations(): array {
        $sql = "SELECT location_id, location_name, region, country
                FROM location
                WHERE status = 'active'
                ORDER BY location_name";

        $stmt = $this->db->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Compter le nombre total de lieux
     */
    public function countLocations(array $filters = []): int {
        $sql = "SELECT COUNT(*) as total FROM location WHERE 1=1";
        $params = [];

        if (!empty($filters['status'])) {
            $sql .= " AND status = :status";
            $params['status'] = $filters['status'];
        }

        if (!empty($filters['country'])) {
            $sql .= " AND country = :country";
            $params['country'] = $filters['country'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (location_name LIKE :search OR region LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        $stmt = $this->db->prepare($sql);

        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch();

        return (int)$result['total'];
    }

    /**
     * Vérifier si un nom de lieu existe déjà
     */
    public function locationNameExists(string $locationName, ?int $excludeId = null): bool {
        $sql = "SELECT COUNT(*) as count FROM location WHERE location_name = :location_name";
        $params = ['location_name' => $locationName];

        if ($excludeId) {
            $sql .= " AND location_id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }

        $stmt = $this->db->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        $stmt->execute();

        $result = $stmt->fetch();
        return $result['count'] > 0;
    }

    // ------------------------------------------
    // Méthodes pour les Points d'arrêt (Stops)
    // ------------------------------------------

    /**
     * Récupère tous les points d'arrêt avec filtres et pagination
     * @param int $limit  Limite de résultats
     * @param int $offset Offset de pagination
     * @param array $filters Filtres à appliquer
     * @return array Liste des stops
     */
    public function getAllStops(int $limit = 50, int $offset = 0, array $filters = []): array {
        $sql = "SELECT s.stop_id, s.stop_name, s.address,
                       ST_X(s.coordinates) as longitude, ST_Y(s.coordinates) as latitude,
                       s.location_id, l.location_name, s.created_at, s.updated_at
                FROM stop s
                LEFT JOIN location l ON s.location_id = l.location_id
                WHERE 1=1";

        $params = [];

        // Appliquer les filtres
        if (!empty($filters['location_id'])) {
            $sql .= " AND s.location_id = :location_id";
            $params['location_id'] = $filters['location_id'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (s.stop_name LIKE :search OR s.address LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        $sql .= " ORDER BY s.stop_name ASC LIMIT :limit OFFSET :offset";

        $stmt = $this->db->prepare($sql);

        // Bind des paramètres
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        $stmt->bindValue(':limit', (int)$limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', (int)$offset, PDO::PARAM_INT);

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Récupère un points d'arrêt par ID (GET /v1/stops/{id})
     * @param int $stopId ID du stop
     * @return array|null Données du point d'arrêt ou null
     */
    public function getStopById(int $stopId): ?array {
        $sql = "SELECT s.stop_id, s.stop_name, s.address,
                       ST_X(s.coordinates) as longitude, ST_Y(s.coordinates) as latitude,
                       s.location_id, l.location_name, s.created_at, s.updated_at
                FROM stop s
                LEFT JOIN location l ON s.location_id = l.location_id
                WHERE s.stop_id = :stop_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':stop_id', $stopId, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Crée un nouveau point d'arrêt (POST /v1/stops)
     * @param array $data Données du stop
     * @return int ID du stop créé
     */
    public function createStop(array $data): int {
        $sql = "INSERT INTO stop (stop_name, address, coordinates, location_id, created_by)
                VALUES (:stop_name, :address, POINT(:longitude, :latitude), :location_id, :created_by)";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':stop_name', $data['stop_name']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':longitude', $data['longitude']);
        $stmt->bindParam(':latitude', $data['latitude']);
        $stmt->bindParam(':location_id', $data['location_id']);
        $stmt->bindParam(':created_by', $data['created_by']);

        $stmt->execute();
        return $this->db->lastInsertId();
    }

    /**
     * Met à jour un point d'arrêt (PUT /v1/stops/{id})
     * @param int $stopId ID du stop
     * @param array $data Données à mettre à jour
     * @return bool Succès de l'opération
     */
    public function updateStop(int $stopId, array $data): bool {
        $sql = "UPDATE stop SET
                stop_name = :stop_name,
                address = :address,
                coordinates = POINT(:longitude, :latitude),
                location_id = :location_id,
                updated_by = :updated_by,
                updated_at = CURRENT_TIMESTAMP
                WHERE stop_id = :stop_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':stop_id', $stopId, PDO::PARAM_INT);
        $stmt->bindParam(':stop_name', $data['stop_name']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':longitude', $data['longitude']);
        $stmt->bindParam(':latitude', $data['latitude']);
        $stmt->bindParam(':location_id', $data['location_id']);
        $stmt->bindParam(':updated_by', $data['updated_by']);

        return $stmt->execute();
    }

    /**
     * Supprime un point d'arrêt (DELETE /v1/stops/{id})
     * @param int $stopId ID du stop
     * @return bool Succès de l'opération
     */
    public function deleteStop(int $stopId): bool {
        // Vérifier s'il y a des routes liées
        $checkSql = "SELECT COUNT(*) as count FROM route_stop WHERE stop_id = :stop_id";
        $checkStmt = $this->db->prepare($checkSql);
        $checkStmt->bindParam(':stop_id', $stopId, PDO::PARAM_INT);
        $checkStmt->execute();
        $result = $checkStmt->fetch();

        if ($result['count'] > 0) {
            throw new Exception('Impossible de supprimer cet arrêt car il est utilisé dans des itinéraires');
        }

        $sql = "DELETE FROM stop WHERE stop_id = :stop_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':stop_id', $stopId, PDO::PARAM_INT);

        return $stmt->execute();
    }

    /**
     * Compter le nombre total d'arrêts
     */
    public function countStops(array $filters = []): int {
        $sql = "SELECT COUNT(*) as total FROM stop s WHERE 1=1";
        $params = [];

        if (!empty($filters['location_id'])) {
            $sql .= " AND s.location_id = :location_id";
            $params['location_id'] = $filters['location_id'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (s.stop_name LIKE :search OR s.address LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }

        $stmt = $this->db->prepare($sql);

        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch();

        return (int)$result['total'];
    }
}