<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EasyBus - Réservation de billets de bus en Afrique de l'Ouest</title>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="assets/css/all.min.css"/>
    <link rel="stylesheet" href="assets/css/style.css"/>
  </head>

  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
      <div class="container">
        <a class="navbar-brand fw-bold" href="#">
          <i class="fas fa-bus text-primary me-2"></i>EasyBus
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link active" href="#">Accueil</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#">Destinations</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#">Services</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#">Compagnies</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#">Contact</a>
            </li>
          </ul>
          <div class="ms-lg-3 mt-3 mt-lg-0">
            <a href="#" class="btn btn-outline-primary me-2">Se connecter</a>
            <a href="#" class="btn btn-primary">S'inscrire</a>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <div class="search-banner py-4">
      <div class="container">
        <h2 class="text-white mb-0">Résultats de recherche</h2>
      </div>
    </div>

    <!-- Search Form -->
    <div class="container search-form-container sticky-top">
      <div class="search-form">
        <form id="trip-search-form">
          <div class="row g-3">
            <div class="col-md-4 position-relative">
              <input
                type="text"
                class="form-control"
                id="departure"
                placeholder="D'où partez-vous ?"
                autocomplete="off"
              />
              <input type="hidden" id="departure-id" name="from" />
              <div class="city-dropdown" id="departure-dropdown"></div>
            </div>
            <div class="col-md-4 position-relative">
              <input
                type="text"
                class="form-control"
                id="destination"
                placeholder="Où allez-vous ?"
                autocomplete="off"
              />
              <input type="hidden" id="destination-id" name="to" />
              <div class="city-dropdown" id="destination-dropdown"></div>
            </div>
            <div class="col-md-2">
              <input
                type="date"
                class="form-control"
                id="departure-date"
                name="date"
                min=""
                value=""
              />
            </div>
            <div class="col-md-2 d-flex align-items-end">
              <button type="submit" class="btn btn-primary search-btn w-100">
                <i class="fas fa-search me-2"></i>Rechercher
              </button>
            </div>
          </div>
        </form>
      </div>
      <div class="search-header mb-4">
        <div
          class="d-flex align-items-center justify-content-between flex-wrap"
        >
          <div class="route-info">
            <h4 class="mb-0">
              <i class="fas fa-map-marker-alt me-2 text-primary"></i>Cotonou -
              Parakou
            </h4>
            <p class="text-muted mb-0">
              <i class="fas fa-calendar me-2 text-primary"></i>
              Vendredi, 30 Mai 2025</p>
          </div>
          <div class="d-flex mt-3 mt-md-0">
            <button class="btn btn-outline-secondary btn-sm me-2" id="edit-search" >
              <i class="fas fa-edit me-1"></i> Modifier
            </button>
            <button class="btn btn-outline-secondary btn-sm me-2" data-bs-toggle="modal" data-bs-target="#filterModal">
              <i class="fas fa-filter me-1"></i> Filtrer
            </button>
            <button class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#sortModal">
              <i class="fas fa-sort me-1"></i> Trier
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Popular Routes Section -->
    <section class="mt-5">
      <div class="container py-5">
        <div id="trip-results" class="row">
          <!-- Trip results will be loaded here -->
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer>
      <div class="container">
        <div class="row">
          <div class="col-lg-3 col-md-6 mb-4 mb-lg-0">
            <h3 class="footer-title">EasyBus</h3>
            <p>
              La plateforme de réservation de billets de bus en ligne leader en
              Afrique de l'Ouest.
            </p>
            <div class="social-links">
              <a href="#"><i class="fab fa-facebook-f"></i></a>
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
              <a href="#"><i class="fab fa-linkedin-in"></i></a>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-4 mb-lg-0">
            <h4 class="footer-title">Liens rapides</h4>
            <ul class="footer-links">
              <li><a href="#">Accueil</a></li>
              <li><a href="#">À propos</a></li>
              <li><a href="#">Compagnies partenaires</a></li>
              <li><a href="#">Destinations</a></li>
              <li><a href="#">Contact</a></li>
            </ul>
          </div>
          <div class="col-lg-3 col-md-6 mb-4 mb-lg-0">
            <h4 class="footer-title">Assistance</h4>
            <ul class="footer-links">
              <li><a href="#">Centre d'aide</a></li>
              <li><a href="#">FAQ</a></li>
              <li><a href="#">Comment réserver</a></li>
              <li><a href="#">Politique d'annulation</a></li>
              <li><a href="#">Réclamations</a></li>
            </ul>
          </div>
          <div class="col-lg-3 col-md-6">
            <h4 class="footer-title">Contact</h4>
            <ul class="footer-links">
              <li>
                <i class="fas fa-map-marker-alt me-2"></i> Rue 123, Cotonou,
                Bénin
              </li>
              <li><i class="fas fa-phone me-2"></i> +229 01 21 00 00 00</li>
              <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
            </ul>
            <div class="mt-3">
              <a href="#" class="btn btn-outline-light btn-sm"
                >Devenir partenaire</a
              >
            </div>
          </div>
        </div>
        <div class="footer-bottom text-center">
          <p class="mb-0">&copy; 2025 EasyBus. Tous droits réservés.</p>
        </div>
      </div>
    </footer>

    <!-- Modal pour les filtres -->
<div class="modal fade" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="filterModalLabel">Filtrer les trajets</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="filterForm">
          <div class="mb-3">
            <label for="busType" class="form-label">Type de bus</label>
            <select class="form-select" id="busType">
              <option value="">Tous</option>
              <option value="standard">Standard</option>
              <option value="vip">VIP</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="amenities" class="form-label">Commodités</label>
            <div id="amenities">
              <!-- Les commodités seront chargées dynamiquement -->
            </div>
          </div>
          <div class="mb-3">
            <label for="priceRange" class="form-label">Fourchette de prix</label>
            <input type="range" class="form-range" id="priceRange" min="0" max="100000" step="1000" value="100000">
            <div id="priceRangeValue">0 - 100000 FCFA</div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
        <button type="button" class="btn btn-primary" id="applyFilters">Appliquer les filtres</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal pour le tri -->
<div class="modal fade" id="sortModal" tabindex="-1" aria-labelledby="sortModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="sortModalLabel">Trier les trajets</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="sortForm">
          <div class="mb-3">
            <label for="sortBy" class="form-label">Trier par</label>
            <select class="form-select" id="sortBy">
              <option value="departureTime">Heure de départ</option>
              <option value="arrivalTime">Heure d'arrivée</option>
              <option value="price">Prix</option>
              <option value="duration">Durée</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="sortOrder" class="form-label">Ordre</label>
            <select class="form-select" id="sortOrder">
              <option value="asc">Croissant</option>
              <option value="desc">Décroissant</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
        <button type="button" class="btn btn-primary" id="applySort">Appliquer le tri</button>
      </div>
    </div>
  </div>
</div>
</body>

<script src="assets/js/bootstrap.bundle.min.js"></script>
<script src="assets/js/script.js" type="module"></script>
</html>

