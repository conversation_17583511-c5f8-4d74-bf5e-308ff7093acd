<?php
require_once __DIR__ . '/../Helpers/db.php';

function checkRole($user_id, $required_role) {
    $db = getDB();
    $sql = "SELECT role_type FROM user_role WHERE user_id = :user_id LIMIT 1";
    $stmt = $db->prepare($sql);
    $stmt->execute([':user_id' => $user_id]);
    $role = $stmt->fetchColumn();

    if ($role !== $required_role) {
        sendResponse(403, ['message' => 'Accès interdit']);
        exit;
    }
}