<?php
require_once __DIR__ . '/../Models/BookingModel.php';
require_once __DIR__ . '/../Models/BookedSeatModel.php';
require_once __DIR__ . '/../Models/TicketModel.php';
require_once __DIR__ . '/../Models/UserModel.php';
require_once __DIR__ . '/../Controllers/NotificationController.php';
require_once __DIR__ . '/../Helpers/response.php';
require_once __DIR__ . '/../Helpers/validate.php';
require_once __DIR__ . '/../Middlewares/AuthMiddleware.php';

class BookingController {
    private $bookingModel;
    private $bookedSeatModel;
    private $ticketModel;
    private $userModel;
    private $notificationController;

    public function __construct() {
        $this->bookingModel = new BookingModel();
        $this->bookedSeatModel = new BookedSeatModel();
        $this->ticketModel = new TicketModel();
        $this->userModel = new UserModel();
        $this->notificationController = new NotificationController();
    }

    public function getUserBookings($user_id) {
        $authData = AuthMiddleware::authenticate();
        if($authData['sub'] != $user_id) {
            sendResponse(403, ['message' => 'Accès non autorisé']);
        }
        $bookings = $this->bookingModel->getUserBookings($user_id);
        sendResponse(200, ['bookings' => $bookings]);
    }

    public function getBookingById($booking_id, $user_id) {
        $authData = AuthMiddleware::authenticate();
        if($authData['sub'] != $user_id) {
            sendResponse(403, ['message' => 'Accès non autorisé']);
        }
        $booking = $this->bookingModel->getUserBookingById($booking_id, $user_id);
        if ($booking) {
            sendResponse(200, ['booking' => $booking]);
        } else {
            sendResponse(404, ['message' => 'Réservation non trouvée ou action non autorisée']);
        }
    }

    public function cancelBooking($booking_id, $user_id) {
        $authData = AuthMiddleware::authenticate();
        if($authData['sub'] != $user_id) {
            sendResponse(403, ['message' => 'Accès non autorisé']);
        }
        $affected = $this->bookingModel->cancelUserBooking($booking_id, $user_id);
        if ($affected) {
            sendResponse(200, ['message' => 'Réservation annulée']);
        } else {
            sendResponse(404, ['message' => 'Réservation non trouvée ou action non autorisée']);
        }
    }

    /**
     * Créer une réservation
     * POST /v1/bookings
     */
    public function createBooking($params = []) {
        try {
            $data = !empty($params) ? $params : json_decode(file_get_contents('php://input'), true);

            validateFields($data, [
                'trip_id' => 'positiveInt',
                'boarding_stop_id' => 'positiveInt',
                'dropping_stop_id' => 'positiveInt',
                'total_amount' => 'positiveInt',
                'seat_ids' => 'required',
                'passengers' => 'required',
                'contact_email' => 'email',
                'contact_phone' => 'required'
            ]);

            // Vérifications similaires à createBooking
            if (!is_array($data['seat_ids']) || empty($data['seat_ids'])) {
                sendResponse(400, ['message' => 'seat_ids doit être un tableau non vide']);
                return;
            }

            if (!is_array($data['passengers']) || empty($data['passengers'])) {
                sendResponse(400, ['message' => 'passengers doit être un tableau non vide']);
                return;
            }

            if (count($data['passengers']) !== count($data['seat_ids'])) {
                sendResponse(400, ['message' => 'Le nombre de passagers doit correspondre au nombre de sièges']);
                return;
            }

            // Créer un utilisateur temporaire ou utiliser un ID spécial pour les utilisateurs anonymes
            $userId = $this->createOrGetUser($data);
            $data['user_id'] = $userId;

            $booking_id = $this->bookingModel->createBooking($data);

            // Créer les tickets et réserver les sièges
            $tickets = [];
            foreach ($data['seat_ids'] as $index => $seat_id) {
                $this->bookedSeatModel->createBookedSeat([
                    'seat_id' => $seat_id,
                    'trip_id' => $data['trip_id'],
                    'booking_id' => $booking_id,
                    'user_id' => $userId
                ]);

                $passenger = $data['passengers'][$index];
                $ticketCode = $this->ticketModel->generateTicketCode();

                $ticketId = $this->ticketModel->createTicket([
                    'booking_id' => $booking_id,
                    'trip_id' => $data['trip_id'],
                    'seat_id' => $seat_id,
                    'passenger_name' => $passenger['name'],
                    'passenger_email' => $data['contact_email'],
                    'passenger_phone' => $data['contact_phone'],
                    'ticket_code' => $ticketCode
                ]);

                $tickets[] = [
                    'ticket_id' => $ticketId,
                    'ticket_code' => $ticketCode,
                    'passenger_name' => $passenger['name'],
                    'seat_number' => $seat_id
                ];
            }

            // Envoyer l'email de confirmation
            $this->sendBookingConfirmationEmail($booking_id, $tickets, $data);

            sendResponse(201, [
                'message' => 'Réservation créée avec succès',
                'booking_id' => $booking_id,
                'tickets' => $tickets,
                'search_info' => [
                    'email' => $data['contact_email'],
                    'phone' => $data['contact_phone']
                ]
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Rechercher des réservations par email ou téléphone
     * GET /v1/bookings/search?email=...&phone=...
     */
    public function searchBookings($params = []) {
        try {
            $email = $params['email'] ?? $_GET['email'] ?? '';
            $phone = $params['phone'] ?? $_GET['phone'] ?? '';

            if (empty($email) && empty($phone)) {
                sendResponse(400, ['message' => 'Email ou téléphone requis pour la recherche']);
                return;
            }

            $bookings = $this->bookingModel->searchBookingsByContact($email, $phone);

            if (empty($bookings)) {
                sendResponse(404, ['message' => 'Aucune réservation trouvée']);
                return;
            }

            // Enrichir les données avec les tickets
            foreach ($bookings as &$booking) {
                $booking['tickets'] = $this->ticketModel->getTicketsByBooking($booking['booking_id']);
            }

            sendResponse(200, ['bookings' => $bookings]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Mettre à jour une réservation
     * PUT /v1/bookings/{id}
     */
    public function updateBooking($params) {
        try {
            $user = AuthMiddleware::authenticate();
            $bookingId = $params['id'] ?? 0;

            if (!$bookingId) {
                sendResponse(400, ['message' => 'ID réservation requis']);
                return;
            }

            $data = json_decode(file_get_contents('php://input'), true);

            // Vérifier que la réservation appartient à l'utilisateur
            $booking = $this->bookingModel->getUserBookingById($bookingId, $user->sub);
            if (!$booking) {
                sendResponse(404, ['message' => 'Réservation non trouvée']);
                return;
            }

            $success = $this->bookingModel->updateBooking($bookingId, $data, $user->sub);

            if ($success) {
                sendResponse(200, ['message' => 'Réservation mise à jour']);
            } else {
                sendResponse(500, ['message' => 'Erreur lors de la mise à jour']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Créer ou récupérer un utilisateur
     */
    private function createOrGetUser(array $params = []) {
        // Vérifier si un utilisateur avec cet email existe déjà
        $existingUser = $this->userModel->findByEmailOrPhone($params['email']);

        if ($existingUser) {
            return $existingUser['user_id'];
        }

        // Créer un nouvel utilisateur
        $userData = [
            'email' => $params['email'],
            'phone' => $params['phone'],
            'first_name' => 'Anonyme',
            'last_name' => '',
            'password_hash' => null, // Pas de mot de passe pour les utilisateurs anonymes
            'verification_status' => 'pending',
            'status' => 'active'
        ];

        return $this->userModel->createUser($userData);
    }

    /**
     * Envoyer l'email de confirmation pour une réservation normale
     */
    private function sendBookingConfirmationEmail($bookingId, $tickets, $data) {
        try {
            $booking = $this->bookingModel->getBookingDetailsForEmail($bookingId);

            if ($booking && !empty($booking['email'])) {
                $this->notificationController->sendBookingConfirmation($booking, $tickets, $data);
            }
        } catch (Exception $e) {
            error_log('Erreur envoi email confirmation: ' . $e->getMessage());
        }
    }

    /**
     * Envoyer l'email de confirmation pour une réservation anonyme
     */
    private function sendGuestBookingConfirmationEmail($bookingId, $tickets, $data) {
        try {
            $booking = $this->bookingModel->getBookingDetailsForEmail($bookingId);

            if ($booking) {
                $booking['passenger_email'] = $data['contact_email'];
                $booking['passenger_name'] = $data['passengers'][0]['name'] ?? 'Invité';

                $this->notificationController->sendBookingConfirmation($booking, $tickets, $data);
            }
        } catch (Exception $e) {
            error_log('Erreur envoi email confirmation invité: ' . $e->getMessage());
        }
    }
}