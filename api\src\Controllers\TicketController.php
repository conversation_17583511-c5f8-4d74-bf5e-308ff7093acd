<?php
require_once __DIR__ . '/../Models/TicketModel.php';
require_once __DIR__ . '/../Helpers/response.php';
require_once __DIR__ . '/../Helpers/validate.php';
require_once __DIR__ . '/../Middlewares/AuthMiddleware.php';
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

class TicketController {
    private $ticketModel;
    
    public function __construct() {
        $this->ticketModel = new TicketModel();
    }
    
    /**
     * Récupérer un ticket par son code
     * GET /v1/tickets/{code}
     */
    public function getTicketByCode($params) {
        try {
            $code = $params['code'] ?? '';
            
            if (empty($code)) {
                sendResponse(400, ['message' => 'Code ticket requis']);
                return;
            }
            
            $ticket = $this->ticketModel->getTicketByCode($code);
            
            if (!$ticket) {
                sendResponse(404, ['message' => 'Ticket non trouvé']);
                return;
            }
            
            sendResponse(200, ['ticket' => $ticket]);
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * Valider un ticket
     * POST /v1/tickets/validate
     */
    public function validateTicket($params) {
        try {
            // Authentification requise (contrôleur ou chauffeur)
            $user = AuthMiddleware::authenticate();
            
            if (!in_array($user->role, ['controller', 'driver'])) {
                sendResponse(403, ['message' => 'Accès non autorisé']);
                return;
            }
            
            $ticketCode = $params['ticket_code'] ?? '';
            
            if (empty($ticketCode)) {
                sendResponse(400, ['message' => 'Code ticket requis']);
                return;
            }
            
            $ticket = $this->ticketModel->getTicketByCode($ticketCode);
            
            if (!$ticket) {
                sendResponse(404, ['message' => 'Ticket non trouvé']);
                return;
            }
            
            if ($ticket['ticket_status'] === 'cancelled') {
                sendResponse(400, ['message' => 'Ticket annulé']);
                return;
            }
            
            if ($ticket['ticket_status'] === 'boarded') {
                sendResponse(400, ['message' => 'Ticket déjà utilisé']);
                return;
            }
            
            // Marquer le ticket comme utilisé
            $success = $this->ticketModel->markTicketAsBoarded($ticket['ticket_id'], $user->sub);
            
            if ($success) {
                sendResponse(200, [
                    'message' => 'Ticket validé avec succès',
                    'passenger_name' => $ticket['passenger_name'],
                    'seat_number' => $ticket['seat_number']
                ]);
            } else {
                sendResponse(500, ['message' => 'Erreur lors de la validation']);
            }
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * Générer un QR code pour un ticket
     * GET /v1/tickets/{id}/qr
     */
    public function generateQRCode($params) {
        try {
            $ticketId = $params['id'] ?? 0;
            
            if (!$ticketId) {
                sendResponse(400, ['message' => 'ID ticket requis']);
                return;
            }
            
            $ticket = $this->ticketModel->getTicketById($ticketId);
            
            if (!$ticket) {
                sendResponse(404, ['message' => 'Ticket non trouvé']);
                return;
            }
            
            // Créer les données du QR code
            $qrData = json_encode([
                'ticket_code' => $ticket['ticket_code'],
                'passenger_name' => $ticket['passenger_name'],
                'trip_id' => $ticket['trip_id'],
                'seat_number' => $ticket['seat_number'],
                'generated_at' => time()
            ]);
            
            // Générer le QR code
            $qrCode = new QrCode($qrData);
            $qrCode->setSize(300);
            $qrCode->setMargin(10);
            
            $writer = new PngWriter();
            $result = $writer->write($qrCode);
            
            // Retourner l'image en base64
            $qrCodeBase64 = base64_encode($result->getString());
            
            sendResponse(200, [
                'qr_code' => 'data:image/png;base64,' . $qrCodeBase64,
                'ticket_code' => $ticket['ticket_code']
            ]);
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur lors de la génération du QR code', 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * Obtenir les tickets d'une réservation
     * GET /v1/bookings/{id}/tickets
     */
    public function getBookingTickets($params) {
        try {
            $user = AuthMiddleware::authenticate();
            $bookingId = $params['booking_id'] ?? 0;
            
            if (!$bookingId) {
                sendResponse(400, ['message' => 'ID réservation requis']);
                return;
            }
            
            $tickets = $this->ticketModel->getTicketsByBooking($bookingId, $user->sub);
            
            sendResponse(200, ['tickets' => $tickets]);
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
}
