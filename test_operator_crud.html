<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CRUD Opérateur</title>
    <link href="public/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Test des fonctionnalités CRUD Opérateur</h1>
        
        <!-- Section Test Lieux -->
        <div class="card mb-4">
            <div class="card-header">
                <h3><i class="fas fa-map-marker-alt me-2"></i>Test Gestion des Lieux</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>C<PERSON>er un lieu</h5>
                        <form id="createLocationForm">
                            <div class="mb-3">
                                <label class="form-label">Nom du lieu</label>
                                <input type="text" class="form-control" id="locationName" value="Test Ville" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Région</label>
                                <input type="text" class="form-control" id="locationRegion" value="Test Région" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Pays</label>
                                <input type="text" class="form-control" id="locationCountry" value="Bénin" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Fuseau horaire</label>
                                <select class="form-select" id="locationTimeZone" required>
                                    <option value="Africa/Porto-Novo">Africa/Porto-Novo</option>
                                </select>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label">Latitude</label>
                                    <input type="number" step="any" class="form-control" id="locationLatitude" value="6.3703" required>
                                </div>
                                <div class="col-6">
                                    <label class="form-label">Longitude</label>
                                    <input type="number" step="any" class="form-control" id="locationLongitude" value="2.3912" required>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary mt-3">Créer le lieu</button>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <h5>Résultats</h5>
                        <div id="locationResults" class="border p-3 bg-light">
                            <p class="text-muted">Aucun test effectué</p>
                        </div>
                        <button class="btn btn-info mt-2" onclick="loadLocations()">Charger les lieux</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Test Commodités -->
        <div class="card mb-4">
            <div class="card-header">
                <h3><i class="fas fa-star me-2"></i>Test Gestion des Commodités</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Créer une commodité</h5>
                        <form id="createAmenityForm">
                            <div class="mb-3">
                                <label class="form-label">Nom de la commodité</label>
                                <input type="text" class="form-control" id="amenityName" value="WiFi Gratuit" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" id="amenityDescription" rows="3">Connexion WiFi gratuite disponible pendant tout le voyage</textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Créer la commodité</button>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <h5>Résultats</h5>
                        <div id="amenityResults" class="border p-3 bg-light">
                            <p class="text-muted">Aucun test effectué</p>
                        </div>
                        <button class="btn btn-info mt-2" onclick="loadAmenities()">Charger les commodités</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Test Arrêts -->
        <div class="card mb-4">
            <div class="card-header">
                <h3><i class="fas fa-map-pin me-2"></i>Test Gestion des Arrêts</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Créer un arrêt</h5>
                        <form id="createStopForm">
                            <div class="mb-3">
                                <label class="form-label">Nom de l'arrêt</label>
                                <input type="text" class="form-control" id="stopName" value="Gare Routière Test" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Lieu</label>
                                <select class="form-select" id="stopLocationId" required>
                                    <option value="">Sélectionner un lieu...</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Adresse</label>
                                <textarea class="form-control" id="stopAddress" rows="2">Rue de la Gare, Test Ville</textarea>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label">Latitude</label>
                                    <input type="number" step="any" class="form-control" id="stopLatitude" value="6.3703" required>
                                </div>
                                <div class="col-6">
                                    <label class="form-label">Longitude</label>
                                    <input type="number" step="any" class="form-control" id="stopLongitude" value="2.3912" required>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary mt-3">Créer l'arrêt</button>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <h5>Résultats</h5>
                        <div id="stopResults" class="border p-3 bg-light">
                            <p class="text-muted">Aucun test effectué</p>
                        </div>
                        <button class="btn btn-info mt-2" onclick="loadStops()">Charger les arrêts</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Logs -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-terminal me-2"></i>Logs de Test</h3>
            </div>
            <div class="card-body">
                <div id="testLogs" class="bg-dark text-light p-3" style="height: 300px; overflow-y: auto; font-family: monospace;">
                    <div class="text-success">[INFO] Page de test chargée</div>
                </div>
                <button class="btn btn-secondary mt-2" onclick="clearLogs()">Effacer les logs</button>
            </div>
        </div>
    </div>

    <script src="public/assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // Configuration API
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        
        // Token de test (à remplacer par un vrai token)
        const TEST_TOKEN = 'test-operator-token';

        // Fonction pour logger
        function log(message, type = 'info') {
            const logsContainer = document.getElementById('testLogs');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
            
            logsContainer.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('testLogs').innerHTML = '<div class="text-success">[INFO] Logs effacés</div>';
        }

        // Fonction pour faire des requêtes API
        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE_URL}/${endpoint}`;
            
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${TEST_TOKEN}`
                }
            };
            
            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };
            
            try {
                log(`Requête ${options.method || 'GET'} vers ${endpoint}`);
                const response = await fetch(url, finalOptions);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || 'Erreur de requête');
                }
                
                log(`Succès: ${JSON.stringify(data)}`, 'success');
                return data;
            } catch (error) {
                log(`Erreur: ${error.message}`, 'error');
                throw error;
            }
        }

        // Test création de lieu
        document.getElementById('createLocationForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const locationData = {
                location_name: document.getElementById('locationName').value,
                region: document.getElementById('locationRegion').value,
                country: document.getElementById('locationCountry').value,
                time_zone: document.getElementById('locationTimeZone').value,
                latitude: parseFloat(document.getElementById('locationLatitude').value),
                longitude: parseFloat(document.getElementById('locationLongitude').value),
                status: 'active',
                created_by: 1 // ID utilisateur test
            };

            try {
                const result = await apiRequest('operator/locations', {
                    method: 'POST',
                    body: JSON.stringify(locationData)
                });
                
                document.getElementById('locationResults').innerHTML = `
                    <div class="alert alert-success">
                        <strong>Lieu créé avec succès!</strong><br>
                        ID: ${result.location_id}<br>
                        Message: ${result.message}
                    </div>
                `;
                
                // Recharger la liste des lieux pour les selects
                await loadLocationsForSelect();
                
            } catch (error) {
                document.getElementById('locationResults').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Erreur:</strong> ${error.message}
                    </div>
                `;
            }
        });

        // Test création de commodité
        document.getElementById('createAmenityForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const amenityData = {
                amenity_name: document.getElementById('amenityName').value,
                description: document.getElementById('amenityDescription').value,
                created_by: 1 // ID utilisateur test
            };

            try {
                const result = await apiRequest('operator/amenities', {
                    method: 'POST',
                    body: JSON.stringify(amenityData)
                });
                
                document.getElementById('amenityResults').innerHTML = `
                    <div class="alert alert-success">
                        <strong>Commodité créée avec succès!</strong><br>
                        ID: ${result.amenity_id}<br>
                        Message: ${result.message}
                    </div>
                `;
                
            } catch (error) {
                document.getElementById('amenityResults').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Erreur:</strong> ${error.message}
                    </div>
                `;
            }
        });

        // Test création d'arrêt
        document.getElementById('createStopForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const stopData = {
                stop_name: document.getElementById('stopName').value,
                location_id: parseInt(document.getElementById('stopLocationId').value),
                address: document.getElementById('stopAddress').value,
                latitude: parseFloat(document.getElementById('stopLatitude').value),
                longitude: parseFloat(document.getElementById('stopLongitude').value),
                created_by: 1 // ID utilisateur test
            };

            try {
                const result = await apiRequest('operator/stops', {
                    method: 'POST',
                    body: JSON.stringify(stopData)
                });
                
                document.getElementById('stopResults').innerHTML = `
                    <div class="alert alert-success">
                        <strong>Arrêt créé avec succès!</strong><br>
                        ID: ${result.stop_id}<br>
                        Message: ${result.message}
                    </div>
                `;
                
            } catch (error) {
                document.getElementById('stopResults').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Erreur:</strong> ${error.message}
                    </div>
                `;
            }
        });

        // Charger les lieux
        async function loadLocations() {
            try {
                const result = await apiRequest('operator/locations');
                
                document.getElementById('locationResults').innerHTML = `
                    <div class="alert alert-info">
                        <strong>Lieux chargés:</strong><br>
                        ${result.locations.map(loc => `• ${loc.location_name} (${loc.country})`).join('<br>')}
                    </div>
                `;
                
            } catch (error) {
                document.getElementById('locationResults').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Erreur:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Charger les commodités
        async function loadAmenities() {
            try {
                const result = await apiRequest('operator/amenities');
                
                document.getElementById('amenityResults').innerHTML = `
                    <div class="alert alert-info">
                        <strong>Commodités chargées:</strong><br>
                        ${result.amenities.map(amenity => `• ${amenity.amenity_name}`).join('<br>')}
                    </div>
                `;
                
            } catch (error) {
                document.getElementById('amenityResults').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Erreur:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Charger les arrêts
        async function loadStops() {
            try {
                const result = await apiRequest('operator/stops');
                
                document.getElementById('stopResults').innerHTML = `
                    <div class="alert alert-info">
                        <strong>Arrêts chargés:</strong><br>
                        ${result.stops.map(stop => `• ${stop.stop_name} (${stop.location_name})`).join('<br>')}
                    </div>
                `;
                
            } catch (error) {
                document.getElementById('stopResults').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Erreur:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Charger les lieux pour le select
        async function loadLocationsForSelect() {
            try {
                const result = await apiRequest('operator/locations');
                const select = document.getElementById('stopLocationId');
                
                // Garder la première option
                const firstOption = select.querySelector('option');
                select.innerHTML = '';
                select.appendChild(firstOption);
                
                result.locations.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.location_id;
                    option.textContent = location.location_name;
                    select.appendChild(option);
                });
                
            } catch (error) {
                log(`Erreur lors du chargement des lieux pour le select: ${error.message}`, 'error');
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            log('Page de test initialisée');
            loadLocationsForSelect();
        });
    </script>
</body>
</html>
