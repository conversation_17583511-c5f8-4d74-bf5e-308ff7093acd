<?php
/**
 * Script de test pour l'API du tableau de bord opérateur
 */

// Configuration
$API_BASE_URL = 'http://localhost:8000/api/v1';
$TEST_TOKEN = 'test-operator-token'; // Token de test

// Fonction pour faire des requêtes API
function apiRequest($endpoint, $method = 'GET', $data = null) {
    global $API_BASE_URL, $TEST_TOKEN;
    
    $url = $API_BASE_URL . '/' . $endpoint;
    
    $options = [
        'http' => [
            'method' => $method,
            'header' => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $TEST_TOKEN
            ]
        ]
    ];
    
    if ($data && in_array($method, ['POST', 'PUT'])) {
        $options['http']['content'] = json_encode($data);
    }
    
    $context = stream_context_create($options);
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        return ['error' => 'Erreur de requête'];
    }
    
    return json_decode($response, true);
}

// Fonction pour afficher les résultats
function displayResult($title, $result) {
    echo "\n" . str_repeat("=", 50) . "\n";
    echo $title . "\n";
    echo str_repeat("=", 50) . "\n";
    
    if (isset($result['error'])) {
        echo "❌ ERREUR: " . $result['error'] . "\n";
    } else {
        echo "✅ SUCCÈS\n";
        echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
}

// Tests
echo "🚀 Début des tests API Opérateur\n";

// Test 1: Vérifier la connectivité API
echo "\n📡 Test de connectivité API...\n";
$testResult = apiRequest('test');
displayResult("Test de connectivité", $testResult);

// Test 2: Créer un lieu
echo "\n🏢 Test création d'un lieu...\n";
$locationData = [
    'location_name' => 'Test Ville API',
    'region' => 'Test Région API',
    'country' => 'Bénin',
    'time_zone' => 'Africa/Porto-Novo',
    'latitude' => 6.3703,
    'longitude' => 2.3912,
    'status' => 'active',
    'created_by' => 1
];

$createLocationResult = apiRequest('operator/locations', 'POST', $locationData);
displayResult("Création d'un lieu", $createLocationResult);

$createdLocationId = isset($createLocationResult['location_id']) ? $createLocationResult['location_id'] : null;

// Test 3: Récupérer les lieux
echo "\n📍 Test récupération des lieux...\n";
$locationsResult = apiRequest('operator/locations');
displayResult("Récupération des lieux", $locationsResult);

// Test 4: Créer une commodité
echo "\n⭐ Test création d'une commodité...\n";
$amenityData = [
    'amenity_name' => 'WiFi Test API',
    'description' => 'Connexion WiFi gratuite pour les tests API',
    'created_by' => 1
];

$createAmenityResult = apiRequest('operator/amenities', 'POST', $amenityData);
displayResult("Création d'une commodité", $createAmenityResult);

$createdAmenityId = isset($createAmenityResult['amenity_id']) ? $createAmenityResult['amenity_id'] : null;

// Test 5: Récupérer les commodités
echo "\n🌟 Test récupération des commodités...\n";
$amenitiesResult = apiRequest('operator/amenities');
displayResult("Récupération des commodités", $amenitiesResult);

// Test 6: Créer un arrêt (si un lieu a été créé)
if ($createdLocationId) {
    echo "\n📌 Test création d'un arrêt...\n";
    $stopData = [
        'stop_name' => 'Gare Test API',
        'location_id' => $createdLocationId,
        'address' => 'Rue de la Gare, Test Ville API',
        'latitude' => 6.3703,
        'longitude' => 2.3912,
        'created_by' => 1
    ];

    $createStopResult = apiRequest('operator/stops', 'POST', $stopData);
    displayResult("Création d'un arrêt", $createStopResult);

    $createdStopId = isset($createStopResult['stop_id']) ? $createStopResult['stop_id'] : null;
}

// Test 7: Récupérer les arrêts
echo "\n🚏 Test récupération des arrêts...\n";
$stopsResult = apiRequest('operator/stops');
displayResult("Récupération des arrêts", $stopsResult);

// Test 8: Mettre à jour le lieu créé (si disponible)
if ($createdLocationId) {
    echo "\n✏️ Test mise à jour d'un lieu...\n";
    $updateLocationData = [
        'location_name' => 'Test Ville API Modifiée',
        'region' => 'Test Région API Modifiée',
        'country' => 'Bénin',
        'time_zone' => 'Africa/Porto-Novo',
        'latitude' => 6.3703,
        'longitude' => 2.3912,
        'status' => 'active',
        'updated_by' => 1
    ];

    $updateLocationResult = apiRequest('operator/locations/' . $createdLocationId, 'PUT', $updateLocationData);
    displayResult("Mise à jour d'un lieu", $updateLocationResult);
}

// Test 9: Mettre à jour la commodité créée (si disponible)
if ($createdAmenityId) {
    echo "\n🔄 Test mise à jour d'une commodité...\n";
    $updateAmenityData = [
        'amenity_name' => 'WiFi Test API Modifié',
        'description' => 'Connexion WiFi gratuite modifiée pour les tests API',
        'updated_by' => 1
    ];

    $updateAmenityResult = apiRequest('operator/amenities/' . $createdAmenityId, 'PUT', $updateAmenityData);
    displayResult("Mise à jour d'une commodité", $updateAmenityResult);
}

// Test 10: Test du tableau de bord
echo "\n📊 Test tableau de bord opérateur...\n";
$dashboardResult = apiRequest('operator/dashboard');
displayResult("Tableau de bord opérateur", $dashboardResult);

// Nettoyage (optionnel) - Supprimer les éléments créés
echo "\n🧹 Nettoyage des données de test...\n";

if (isset($createdStopId)) {
    $deleteStopResult = apiRequest('operator/stops/' . $createdStopId, 'DELETE');
    displayResult("Suppression de l'arrêt de test", $deleteStopResult);
}

if ($createdAmenityId) {
    $deleteAmenityResult = apiRequest('operator/amenities/' . $createdAmenityId, 'DELETE');
    displayResult("Suppression de la commodité de test", $deleteAmenityResult);
}

if ($createdLocationId) {
    $deleteLocationResult = apiRequest('operator/locations/' . $createdLocationId, 'DELETE');
    displayResult("Suppression du lieu de test", $deleteLocationResult);
}

echo "\n🎉 Tests terminés!\n";
echo "\n" . str_repeat("=", 50) . "\n";
echo "RÉSUMÉ DES TESTS\n";
echo str_repeat("=", 50) . "\n";
echo "✅ Test de connectivité API\n";
echo "✅ Test CRUD Lieux (Créer, Lire, Modifier, Supprimer)\n";
echo "✅ Test CRUD Commodités (Créer, Lire, Modifier, Supprimer)\n";
echo "✅ Test CRUD Arrêts (Créer, Lire, Supprimer)\n";
echo "✅ Test tableau de bord opérateur\n";
echo "✅ Test nettoyage des données\n";
echo "\n📝 Note: Vérifiez les logs ci-dessus pour les détails de chaque test.\n";
echo "🔧 Si des erreurs persistent, vérifiez la configuration de l'API et de la base de données.\n";

?>
