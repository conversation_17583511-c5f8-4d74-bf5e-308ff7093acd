<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finaliser la réservation - EasyBus</title>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="assets/css/all.min.css"/>
    <link rel="stylesheet" href="assets/css/style.css"/>
    <style>
        .checkout-container {
            background-color: #f8f9fa;
            min-height: 100vh;
            padding: 2rem 0;
        }
        .checkout-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .step.active .step-number {
            background: #0d6efd;
            color: white;
        }
        .step.completed .step-number {
            background: #198754;
            color: white;
        }
        .booking-summary {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .passenger-form {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .payment-method {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .payment-method:hover,
        .payment-method.selected {
            border-color: #0d6efd;
            background-color: #f8f9ff;
        }
        .total-section {
            background: linear-gradient(135deg, #0d6efd, #6610f2);
            color: white;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }
        #embed iframe {
  width: 100%;
  height: 550px;
  border: none;
}
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-bus text-primary me-2"></i>EasyBus
            </a>
            <div class="ms-auto">
                <a href="index.html" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour
                </a>
            </div>
        </div>
    </nav>

    <div class="checkout-container">
        <div class="container">
            <!-- Indicateur d'étapes -->
            <div class="step-indicator">
                <div class="step completed">
                    <div class="step-number">1</div>
                    <span>Sélection</span>
                </div>
                <div class="step completed">
                    <div class="step-number">2</div>
                    <span>Sièges</span>
                </div>
                <div class="step active">
                    <div class="step-number">3</div>
                    <span>Paiement</span>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <span>Confirmation</span>
                </div>
            </div>

            <div class="row">
                <!-- Formulaire de réservation -->
                <div id="embed" class="col-lg-6 mb-3">
                </div>
                <!-- Résumé de la réservation -->
                <div class="col-lg-6">
                    <div class="checkout-card">
                        <h4 class="mb-3">
                            <i class="fas fa-ticket-alt me-2"></i>Résumé de la réservation
                        </h4>
                        
                        <div class="booking-summary">
                            <div id="tripSummary">
                                <!-- Résumé du voyage sera généré dynamiquement -->
                            </div>
                        </div>

                        <div class="total-section">
                            <h5 class="mb-2">Total à payer</h5>
                            <h2 class="mb-0" id="totalAmount">0 FCFA</h2>
                            <small>Taxes incluses</small>
                        </div>

                        <div class="mt-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Nombre de passagers:</span>
                                <span id="passengerCount">0</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Prix par siège:</span>
                                <span id="seatPrice">0 FCFA</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between fw-bold">
                                <span>Total:</span>
                                <span id="totalAmountDetail">0 FCFA</span>
                            </div>
                        </div>
                    </div>

                    <!-- Informations de sécurité -->
                    <div class="checkout-card">
                        <h6><i class="fas fa-shield-alt me-2 text-success"></i>Paiement sécurisé</h6>
                        <small class="text-muted">
                            Vos informations de paiement sont protégées par un cryptage SSL 256 bits.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages d'alerte -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="alertContainer"></div>
    </div>

    <!-- Modal de chargement -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <h5>Traitement de votre réservation...</h5>
                    <p class="text-muted mb-0">Veuillez patienter, cela peut prendre quelques instants.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.fedapay.com/checkout.js?v=1.1.7"></script>
    <script src="assets/js/auth.js"></script>
    <script src="assets/js/checkout.js"></script>

    <script type="text/javascript">
      FedaPay.init({
        public_key: 'pk_sandbox_cFbGlBOcniZjNeI_BsosWC0i',
        transaction: {
          amount: 1000,
          description: 'Acheter mon produit',
          id: '328797'
        },
        customer: {
          email: '<EMAIL>',
          lastname: 'Doe',
          firstname: 'John'
        },
        container: '#embed'
     });
  </script>
</body>
</html>

