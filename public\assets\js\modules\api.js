/**
 * API module for handling all API requests
 */

const API_BASE_URL = '/api/v1';

/**
 * Cache for API responses
 */
const cache = {
  trips: [],
  amenities: null,
  tripDetails: {},
  seats: new Map(),
  tripStops: {},
  cities: []
};

/**
 * Fetches trips based on search parameters
 * @param {string} date - Departure date
 * @param {string} departureId - Departure location ID
 * @param {string} destinationId - Destination location ID
 * @returns {Promise<Array>} - Array of trips
 */
export async function fetchTrips(date, departureId, destinationId) {
  if (cache.trips.length > 0) {
    return cache.trips;
  }

  try {
    const response = await fetch(`${API_BASE_URL}/trips/search?date=${date}&from=${departureId}&to=${destinationId}`, {
      headers: {
        'Accept-Encoding': 'br, gzip, deflate'
      }
    });

    if (!response.ok) {
      throw new Error("Impossible de charger les trajets, échec de la requête.");
    }

    const responseData = await response.json();
    cache.trips = responseData.data;
    return responseData.data;
  } catch (error) {
    console.error(error);
    return [];
  }
}

/**
 * Fetches available amenities
 * @returns {Promise<Array>} - Array of amenities
 */
export async function fetchAmenities() {
  if (cache.amenities) {
    return cache.amenities;
  }

  try {
    const response = await fetch(`${API_BASE_URL}/amenities`, {
      headers: {
        'Accept-Encoding': 'br, gzip, deflate'
      }
    });

    if (!response.ok) {
      throw new Error("Erreur lors de la récupération des commodités.");
    }

    const responseData = await response.json();
    cache.amenities = responseData.data;
    return responseData.data;
  } catch (error) {
    console.error("Erreur lors du chargement des commodités:", error);
    return [];
  }
}

/**
 * Fetches details for a specific trip
 * @param {number} tripId - Trip ID
 * @returns {Promise<Object>} - Trip details
 */
export async function fetchTripDetails(tripId) {
  if (cache.tripDetails[tripId]) {
    return cache.tripDetails[tripId];
  }

  try {
    const response = await fetch(`${API_BASE_URL}/trips/${tripId}`);
    const responseData = await response.json();
    cache.tripDetails[tripId] = responseData.data;
    return responseData.data;
  } catch (error) {
    console.error("Erreur lors du chargement des détails du trajet", error);
    throw error;
  }
}

/**
 * Fetches seats for a specific trip
 * @param {number} trip_id - Trip ID
 * @returns {Promise<Array>} - Array of seats
 */
export async function fetchSeats(trip_id) {
  const cacheKey = `${trip_id}`;
  if (cache.seats.has(cacheKey)) {
    return cache.seats.get(cacheKey);
  }

  try {
    const response = await fetch(`${API_BASE_URL}/trips/${trip_id}/seats`, {
      headers: {
        'Accept-Encoding': 'br, gzip, deflate'
      }
    });
    const responseData = await response.json();
    cache.seats.set(cacheKey, responseData.data);
    return responseData.data;
  } catch (error) {
    console.error("Erreur lors du chargement des sièges", error);
    throw error;
  }
}

/**
 * Fetches stops for a specific trip
 * @param {number} tripId - Trip ID
 * @returns {Promise<Array>} - Array of stops
 */
export async function fetchTripStops(tripId) {
  let tripStops = cache.tripDetails[tripId]?.route?.stops || [];

  if (tripStops.length > 0) {
    return tripStops;
  }

  if (cache.tripStops[tripId]) {
    return cache.tripStops[tripId];
  }

  try {
    const response = await fetch(`${API_BASE_URL}/trips/${tripId}/stops`);
    const tripStopsResponse = await response.json();
    tripStops = tripStopsResponse.data;

    cache.tripStops[tripId] = tripStops;

    return tripStops;
  } catch (error) {
    console.error("Erreur lors du chargement des arrêts du trajet", error);
    throw error;
  }
}

/**
 * Fetches available cities/locations
 * @returns {Promise<Array>} - Array of cities
 */
export async function fetchCities() {
  if (cache.cities.length > 0) {
    return cache.cities;
  }

  try {
    const response = await fetch(`${API_BASE_URL}/locations`);
    if (!response.ok) {
      throw new Error(`Erreur ${response.status}: ${response.statusText} lors du chargement des villes`);
    }
    const responseData = await response.json();
    cache.cities = responseData.data;
    return responseData.data;
  } catch (error) {
    console.error("Erreur:", error);
    throw error;
  }
}
