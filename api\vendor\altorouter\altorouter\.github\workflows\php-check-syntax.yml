name: Check PHP syntax

on: [ push, pull_request ]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php-versions: ['7.3', '7.4', '8.0', '8.1', '8.2', '8.3', 'highest']
    steps:
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}

      - name: checkout repo
        uses: actions/checkout@v3

      - run: composer run check-syntax
