<?php
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
require_once __DIR__ . '/../config/app.php';

function generateJWT($user_id, $user_role) {
    $config = require __DIR__ . '/../config/app.php';
    $payload = [
        'sub' => $user_id,
        'role' => $user_role,
        'iat' => time(),
        'exp' => time() + 24 * 3600, // 1 jour d’expiration
    ];
    return JWT::encode($payload, $config['jwt']['secret'], 'HS256');
}

function verifyJWT($token) {
    $config = require __DIR__ . '/../config/app.php';
    try {
        $secret = $config['jwt']['secret'];
        $decoded = JWT::decode($token, new Key($secret, 'HS256'));
        return $decoded;
    } catch (Exception $e) {
        sendResponse(401, ['message' => 'Token invalide']);
        exit;
    }
}