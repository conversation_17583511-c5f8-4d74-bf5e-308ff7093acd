<?php
require_once __DIR__ . '/../Helpers/db.php';

/**
 * Modèle pour la gestion des commodités (amenities)
 */
class AmenityModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    /**
     * Récupérer toutes les commodités avec pagination et filtres
     */
    public function getAllAmenities(int $limit = 50, int $offset = 0, array $filters = []): array { //NOSONAR
        $sql = "SELECT amenity_id, amenity_name, description, created_by, updated_by
                FROM amenity WHERE 1=1";
        
        $params = [];
        
        // Appliquer les filtres
        if (!empty($filters['search'])) {
            $sql .= " AND (amenity_name LIKE :search OR description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        $sql .= " ORDER BY amenity_name ASC LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        
        // Bind des paramètres
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        $stmt->bindValue(':limit', (int)$limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', (int)$offset, PDO::PARAM_INT);
        
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Récupérer une commodité par ID
     */
    public function getAmenityById(int $amenityId): ?array {
        $sql = "SELECT amenity_id, amenity_name, description, created_at, updated_at
                FROM amenity WHERE amenity_id = :amenity_id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':amenity_id', $amenityId, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Créer une nouvelle commodité
     */
    public function createAmenity(array $data): int {
        $sql = "INSERT INTO amenity (amenity_name, description, created_by) 
                VALUES (:amenity_name, :description, :created_by)";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':amenity_name', $data['amenity_name']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':created_by', $data['created_by']);
        
        $stmt->execute();
        return $this->db->lastInsertId();
    }

    /**
     * Mettre à jour une commodité
     */
    public function updateAmenity(int $amenityId, array $data): bool {
        $sql = "UPDATE amenity SET 
                amenity_name = :amenity_name,
                description = :description,
                updated_by = :updated_by
                WHERE amenity_id = :amenity_id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':amenity_id', $amenityId, PDO::PARAM_INT);
        $stmt->bindParam(':amenity_name', $data['amenity_name']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':updated_by', $data['updated_by']);
        
        return $stmt->execute();
    }

    /**
     * Supprimer une commodité
     */
    public function deleteAmenity(int $amenityId): bool {
        // Vérifier s'il y a des bus liés
        $checkSql = "SELECT COUNT(*) as count FROM bus_amenity WHERE amenity_id = :amenity_id";
        $checkStmt = $this->db->prepare($checkSql);
        $checkStmt->bindParam(':amenity_id', $amenityId, PDO::PARAM_INT);
        $checkStmt->execute();
        $result = $checkStmt->fetch();
        
        if ($result['count'] > 0) {
            throw new Exception('Impossible de supprimer cette commodité car elle est utilisée par des bus');
        }
        
        $sql = "DELETE FROM amenity WHERE amenity_id = :amenity_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':amenity_id', $amenityId, PDO::PARAM_INT);
        
        return $stmt->execute();
    }

    /**
     * Compter le nombre total de commodités
     */
    public function countAmenities(array $filters = []): int {
        $sql = "SELECT COUNT(*) as total FROM amenity WHERE 1=1";
        $params = [];
        
        if (!empty($filters['search'])) {
            $sql .= " AND (amenity_name LIKE :search OR description LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        $stmt = $this->db->prepare($sql);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        
        $stmt->execute();
        $result = $stmt->fetch();
        
        return (int)$result['total'];
    }

    /**
     * Vérifier si un nom de commodité existe déjà
     */
    public function amenityNameExists(string $amenityName, ?int $excludeId = null): bool {
        $sql = "SELECT COUNT(*) as count FROM amenity WHERE amenity_name = :amenity_name";
        $params = ['amenity_name' => $amenityName];
        
        if ($excludeId) {
            $sql .= " AND amenity_id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        $stmt->execute();
        
        $result = $stmt->fetch();
        return $result['count'] > 0;
    }

    /**
     * Récupérer les commodités d'un bus
     */
    public function getBusAmenities(int $busId): array {
        $sql = "SELECT a.amenity_id, a.amenity_name, a.description
                FROM amenity a
                INNER JOIN bus_amenity ba ON a.amenity_id = ba.amenity_id
                WHERE ba.bus_id = :bus_id
                ORDER BY a.amenity_name";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':bus_id', $busId, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Ajouter une commodité à un bus
     */
    public function addBusAmenity(int $busId, int $amenityId, int $createdBy): bool {
        // Vérifier si l'association existe déjà
        $checkSql = "SELECT COUNT(*) as count FROM bus_amenity WHERE bus_id = :bus_id AND amenity_id = :amenity_id";
        $checkStmt = $this->db->prepare($checkSql);
        $checkStmt->bindParam(':bus_id', $busId, PDO::PARAM_INT);
        $checkStmt->bindParam(':amenity_id', $amenityId, PDO::PARAM_INT);
        $checkStmt->execute();
        $result = $checkStmt->fetch();
        
        if ($result['count'] > 0) {
            return false; // Association déjà existante
        }
        
        $sql = "INSERT INTO bus_amenity (bus_id, amenity_id, created_by) 
                VALUES (:bus_id, :amenity_id, :created_by)";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':bus_id', $busId, PDO::PARAM_INT);
        $stmt->bindParam(':amenity_id', $amenityId, PDO::PARAM_INT);
        $stmt->bindParam(':created_by', $createdBy, PDO::PARAM_INT);
        
        return $stmt->execute();
    }

    /**
     * Supprimer une commodité d'un bus
     */
    public function removeBusAmenity(int $busId, int $amenityId): bool {
        $sql = "DELETE FROM bus_amenity WHERE bus_id = :bus_id AND amenity_id = :amenity_id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':bus_id', $busId, PDO::PARAM_INT);
        $stmt->bindParam(':amenity_id', $amenityId, PDO::PARAM_INT);
        
        return $stmt->execute();
    }

    /**
     * Mettre à jour toutes les commodités d'un bus
     */
    public function updateBusAmenities(int $busId, array $amenityIds, int $updatedBy): bool {
        try {
            $this->db->beginTransaction();
            
            // Supprimer toutes les commodités existantes du bus
            $deleteSql = "DELETE FROM bus_amenity WHERE bus_id = :bus_id";
            $deleteStmt = $this->db->prepare($deleteSql);
            $deleteStmt->bindParam(':bus_id', $busId, PDO::PARAM_INT);
            $deleteStmt->execute();
            
            // Ajouter les nouvelles commodités
            if (!empty($amenityIds)) {
                $insertSql = "INSERT INTO bus_amenity (bus_id, amenity_id, created_by) VALUES (:bus_id, :amenity_id, :created_by)";
                $insertStmt = $this->db->prepare($insertSql);
                
                foreach ($amenityIds as $amenityId) {
                    $insertStmt->bindParam(':bus_id', $busId, PDO::PARAM_INT);
                    $insertStmt->bindParam(':amenity_id', $amenityId, PDO::PARAM_INT);
                    $insertStmt->bindParam(':created_by', $updatedBy, PDO::PARAM_INT);
                    $insertStmt->execute();
                }
            }
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }
}
?>
