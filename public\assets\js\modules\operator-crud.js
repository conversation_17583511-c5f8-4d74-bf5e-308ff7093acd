/**
 * Module CRUD pour le tableau de bord opérateur
 * Gestion des entités : Lieux, Arrêts, Commodités, Plans de sièges, etc.
 */

// Variables globales pour les données
let locationsData = [];
let stopsData = [];
let amenitiesData = [];
let seatPlansData = [];
let routesData = [];
let pricingData = [];
let paymentsData = [];
let usersData = [];
let busesData = [];
let tripsData = [];
let seatsData = [];

// Exposer les variables globalement pour le débogage et les tests
window.locationsData = locationsData;
window.stopsData = stopsData;
window.amenitiesData = amenitiesData;
window.seatPlansData = seatPlansData;
window.routesData = routesData;
window.pricingData = pricingData;
window.paymentsData = paymentsData;
window.usersData = usersData;
window.busesData = busesData;
window.tripsData = tripsData;
window.seatsData = seatsData;

// ==================== GESTION DES LIEUX ====================

// Charger les lieux
async function loadLocations() {
    try {
        const response = await apiRequest('operator/locations');
        locationsData = response.locations || [];
        window.locationsData = locationsData; // Mettre à jour la référence globale
        displayLocations(locationsData);
        populateLocationSelects();
    } catch (error) {
        console.error('Erreur lors du chargement des lieux:', error);
        showAlert('Erreur lors du chargement des lieux', 'danger');
    }
}

// Afficher les lieux
function displayLocations(locations) {
    const container = document.getElementById('locationsTableContainer');

    if (!locations || locations.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-map-marker-alt fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun lieu trouvé</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>Région</th>
                        <th>Pays</th>
                        <th>Fuseau horaire</th>
                        <th>Coordonnées</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    locations.forEach(location => {
        const statusClass = location.status === 'active' ? 'success' : 'secondary';
        const statusText = location.status === 'active' ? 'Actif' : 'Inactif';

        html += `
            <tr>
                <td>#${location.location_id}</td>
                <td><strong>${location.location_name}</strong></td>
                <td>${location.region}</td>
                <td>${location.country}</td>
                <td>${location.time_zone}</td>
                <td>
                    <small class="text-muted">
                        ${location.latitude ? location.latitude.toFixed(4) : 'N/A'}, 
                        ${location.longitude ? location.longitude.toFixed(4) : 'N/A'}
                    </small>
                </td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editLocation(${location.location_id})" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteLocation(${location.location_id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Afficher le modal de création de lieu
function showCreateLocationModal() {
    document.getElementById('locationModalTitle').textContent = 'Nouveau Lieu';
    document.getElementById('locationForm').reset();
    document.getElementById('locationId').value = '';
    document.getElementById('locationStatus').value = 'active';
    
    const modal = new bootstrap.Modal(document.getElementById('locationModal'));
    modal.show();
}

// Modifier un lieu
function editLocation(locationId) {
    const location = locationsData.find(l => l.location_id == locationId);
    if (!location) {
        showAlert('Lieu non trouvé', 'danger');
        return;
    }

    document.getElementById('locationModalTitle').textContent = 'Modifier le Lieu';
    document.getElementById('locationId').value = location.location_id;
    document.getElementById('locationName').value = location.location_name;
    document.getElementById('locationRegion').value = location.region;
    document.getElementById('locationCountry').value = location.country;
    document.getElementById('locationTimeZone').value = location.time_zone;
    document.getElementById('locationLatitude').value = location.latitude || '';
    document.getElementById('locationLongitude').value = location.longitude || '';
    document.getElementById('locationStatus').value = location.status;

    const modal = new bootstrap.Modal(document.getElementById('locationModal'));
    modal.show();
}

// Sauvegarder un lieu
async function saveLocation() {
    const form = document.getElementById('locationForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const locationId = document.getElementById('locationId').value;
    const isEdit = !!locationId;

    const locationData = {
        location_name: document.getElementById('locationName').value,
        region: document.getElementById('locationRegion').value,
        country: document.getElementById('locationCountry').value,
        time_zone: document.getElementById('locationTimeZone').value,
        latitude: parseFloat(document.getElementById('locationLatitude').value),
        longitude: parseFloat(document.getElementById('locationLongitude').value),
        status: document.getElementById('locationStatus').value
    };

    try {
        let response;
        if (isEdit) {
            response = await apiRequest(`operator/locations/${locationId}`, {
                method: 'PUT',
                body: JSON.stringify(locationData)
            });
        } else {
            response = await apiRequest('operator/locations', {
                method: 'POST',
                body: JSON.stringify(locationData)
            });
        }

        showAlert(isEdit ? 'Lieu modifié avec succès' : 'Lieu créé avec succès', 'success');
        
        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('locationModal'));
        modal.hide();
        
        // Recharger les données
        await loadLocations();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        showAlert(error.message || 'Erreur lors de la sauvegarde', 'danger');
    }
}

// Supprimer un lieu
async function deleteLocation(locationId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce lieu ? Cette action est irréversible.')) {
        return;
    }

    try {
        await apiRequest(`operator/locations/${locationId}`, {
            method: 'DELETE'
        });

        showAlert('Lieu supprimé avec succès', 'success');
        await loadLocations();

    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        showAlert(error.message || 'Erreur lors de la suppression', 'danger');
    }
}

// Filtrer les lieux
function filterLocations() {
    const statusFilter = document.getElementById('locationStatusFilter').value;
    const countryFilter = document.getElementById('locationCountryFilter').value;
    const searchFilter = document.getElementById('locationSearchFilter').value.toLowerCase();

    let filteredLocations = locationsData.filter(location => {
        const matchesStatus = !statusFilter || location.status === statusFilter;
        const matchesCountry = !countryFilter || location.country === countryFilter;
        const matchesSearch = !searchFilter || 
            location.location_name.toLowerCase().includes(searchFilter) ||
            location.region.toLowerCase().includes(searchFilter);

        return matchesStatus && matchesCountry && matchesSearch;
    });

    displayLocations(filteredLocations);
}

// Effacer les filtres de lieux
function clearLocationFilters() {
    document.getElementById('locationStatusFilter').value = '';
    document.getElementById('locationCountryFilter').value = '';
    document.getElementById('locationSearchFilter').value = '';
    displayLocations(locationsData);
}

// Peupler les selects de lieux
function populateLocationSelects() {
    // Tous les selects qui utilisent les lieux
    const selects = [
        'stopLocationId',
        'routeDepartureFilter',
        'stopLocationFilter',
        'routeDepartureLocationId',
        'routeDestinationLocationId'
    ];

    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // Garder la première option
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            locationsData.forEach(location => {
                if (location.status === 'active') {
                    const option = document.createElement('option');
                    option.value = location.location_id;
                    option.textContent = location.location_name;
                    select.appendChild(option);
                }
            });
        }
    });

    // Peupler le filtre pays
    const countryFilter = document.getElementById('locationCountryFilter');
    if (countryFilter) {
        const countries = [...new Set(locationsData.map(l => l.country))];
        const firstOption = countryFilter.querySelector('option');
        countryFilter.innerHTML = '';
        if (firstOption) {
            countryFilter.appendChild(firstOption);
        }

        countries.forEach(country => {
            const option = document.createElement('option');
            option.value = country;
            option.textContent = country;
            countryFilter.appendChild(option);
        });
    }
}

// ==================== GESTION DES ARRÊTS ====================

// Charger les arrêts
async function loadStops() {
    try {
        const response = await apiRequest('operator/stops');
        stopsData = response.stops || [];
        displayStops(stopsData);
    } catch (error) {
        console.error('Erreur lors du chargement des arrêts:', error);
        showAlert('Erreur lors du chargement des arrêts', 'danger');
    }
}

// Afficher les arrêts
function displayStops(stops) {
    const container = document.getElementById('stopsTableContainer');

    if (!stops || stops.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-map-pin fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun arrêt trouvé</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom de l'arrêt</th>
                        <th>Lieu</th>
                        <th>Adresse</th>
                        <th>Coordonnées</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    stops.forEach(stop => {
        html += `
            <tr>
                <td>#${stop.stop_id}</td>
                <td><strong>${stop.stop_name}</strong></td>
                <td>${stop.location_name || 'N/A'}</td>
                <td>
                    <small class="text-muted">${stop.address || 'Non spécifiée'}</small>
                </td>
                <td>
                    <small class="text-muted">
                        ${stop.latitude ? stop.latitude.toFixed(4) : 'N/A'}, 
                        ${stop.longitude ? stop.longitude.toFixed(4) : 'N/A'}
                    </small>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editStop(${stop.stop_id})" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteStop(${stop.stop_id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Afficher le modal de création d'arrêt
async function showCreateStopModal() {
    try {
        // Afficher un indicateur de chargement
        const modalElement = document.getElementById('stopModal');
        const modalBody = modalElement.querySelector('.modal-body');
        const originalContent = modalBody.innerHTML;

        modalBody.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div><p class="mt-2">Chargement des données...</p></div>';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Charger les données nécessaires (lieux)
        await ensureLocationsLoaded();

        // Restaurer le contenu original
        modalBody.innerHTML = originalContent;

        // Configurer le formulaire
        document.getElementById('stopModalTitle').textContent = 'Nouvel Arrêt';
        document.getElementById('stopForm').reset();
        document.getElementById('stopId').value = '';

        // Peupler les selects avec les données chargées
        populateLocationSelects();

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal arrêt:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Modifier un arrêt
async function editStop(stopId) {
    const stop = stopsData.find(s => s.stop_id == stopId);
    if (!stop) {
        showAlert('Arrêt non trouvé', 'danger');
        return;
    }

    try {
        // Afficher un indicateur de chargement
        const modalElement = document.getElementById('stopModal');
        const modalBody = modalElement.querySelector('.modal-body');
        const originalContent = modalBody.innerHTML;

        modalBody.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div><p class="mt-2">Chargement des données...</p></div>';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Charger les données nécessaires (lieux)
        await ensureLocationsLoaded();

        // Restaurer le contenu original
        modalBody.innerHTML = originalContent;

        // Peupler les selects avec les données chargées
        populateLocationSelects();

        // Configurer le formulaire avec les données de l'arrêt
        document.getElementById('stopModalTitle').textContent = 'Modifier l\'Arrêt';
        document.getElementById('stopId').value = stop.stop_id;
        document.getElementById('stopName').value = stop.stop_name;
        document.getElementById('stopLocationId').value = stop.location_id;
        document.getElementById('stopAddress').value = stop.address || '';
        document.getElementById('stopLatitude').value = stop.latitude || '';
        document.getElementById('stopLongitude').value = stop.longitude || '';

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal arrêt:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Sauvegarder un arrêt
async function saveStop() {
    const form = document.getElementById('stopForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const stopId = document.getElementById('stopId').value;
    const isEdit = !!stopId;

    const stopData = {
        stop_name: document.getElementById('stopName').value,
        location_id: parseInt(document.getElementById('stopLocationId').value),
        address: document.getElementById('stopAddress').value,
        latitude: parseFloat(document.getElementById('stopLatitude').value),
        longitude: parseFloat(document.getElementById('stopLongitude').value)
    };

    try {
        let response;
        if (isEdit) {
            response = await apiRequest(`operator/stops/${stopId}`, {
                method: 'PUT',
                body: JSON.stringify(stopData)
            });
        } else {
            response = await apiRequest('operator/stops', {
                method: 'POST',
                body: JSON.stringify(stopData)
            });
        }

        showAlert(isEdit ? 'Arrêt modifié avec succès' : 'Arrêt créé avec succès', 'success');

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('stopModal'));
        modal.hide();

        // Recharger les données
        await loadStops();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        showAlert(error.message || 'Erreur lors de la sauvegarde', 'danger');
    }
}

// Supprimer un arrêt
async function deleteStop(stopId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet arrêt ? Cette action est irréversible.')) {
        return;
    }

    try {
        await apiRequest(`operator/stops/${stopId}`, {
            method: 'DELETE'
        });

        showAlert('Arrêt supprimé avec succès', 'success');
        await loadStops();

    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        showAlert(error.message || 'Erreur lors de la suppression', 'danger');
    }
}

// Filtrer les arrêts
function filterStops() {
    const locationFilter = document.getElementById('stopLocationFilter').value;
    const searchFilter = document.getElementById('stopSearchFilter').value.toLowerCase();

    let filteredStops = stopsData.filter(stop => {
        const matchesLocation = !locationFilter || stop.location_id == locationFilter;
        const matchesSearch = !searchFilter ||
            stop.stop_name.toLowerCase().includes(searchFilter) ||
            (stop.address && stop.address.toLowerCase().includes(searchFilter));

        return matchesLocation && matchesSearch;
    });

    displayStops(filteredStops);
}

// Effacer les filtres d'arrêts
function clearStopFilters() {
    document.getElementById('stopLocationFilter').value = '';
    document.getElementById('stopSearchFilter').value = '';
    displayStops(stopsData);
}

// ==================== GESTION DES COMMODITÉS ====================

// Charger les commodités
async function loadAmenities() {
    try {
        const response = await apiRequest('operator/amenities');
        amenitiesData = response.amenities || [];
        displayAmenities(amenitiesData);
    } catch (error) {
        console.error('Erreur lors du chargement des commodités:', error);
        showAlert('Erreur lors du chargement des commodités', 'danger');
    }
}

// Afficher les commodités
function displayAmenities(amenities) {
    const container = document.getElementById('amenitiesGridContainer');

    if (!amenities || amenities.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-star fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucune commodité trouvée</p>
            </div>
        `;
        return;
    }

    let html = '<div class="row">';

    amenities.forEach(amenity => {
        html += `
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-star text-warning me-2"></i>
                            ${amenity.amenity_name}
                        </h5>
                        <p class="card-text text-muted">
                            ${amenity.description || 'Aucune description'}
                        </p>
                    </div>
                    <div class="card-footer bg-transparent">
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="editAmenity(${amenity.amenity_id})">
                            <i class="fas fa-edit"></i> Modifier
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteAmenity(${amenity.amenity_id})">
                            <i class="fas fa-trash"></i> Supprimer
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

// Afficher le modal de création de commodité
function showCreateAmenityModal() {
    document.getElementById('amenityModalTitle').textContent = 'Nouvelle Commodité';
    document.getElementById('amenityForm').reset();
    document.getElementById('amenityId').value = '';

    const modal = new bootstrap.Modal(document.getElementById('amenityModal'));
    modal.show();
}

// Modifier une commodité
function editAmenity(amenityId) {
    const amenity = amenitiesData.find(a => a.amenity_id == amenityId);
    if (!amenity) {
        showAlert('Commodité non trouvée', 'danger');
        return;
    }

    document.getElementById('amenityModalTitle').textContent = 'Modifier la Commodité';
    document.getElementById('amenityId').value = amenity.amenity_id;
    document.getElementById('amenityName').value = amenity.amenity_name;
    document.getElementById('amenityDescription').value = amenity.description || '';

    const modal = new bootstrap.Modal(document.getElementById('amenityModal'));
    modal.show();
}

// Sauvegarder une commodité
async function saveAmenity() {
    const form = document.getElementById('amenityForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const amenityId = document.getElementById('amenityId').value;
    const isEdit = !!amenityId;

    const amenityData = {
        amenity_name: document.getElementById('amenityName').value,
        description: document.getElementById('amenityDescription').value
    };

    try {
        let response;
        if (isEdit) {
            response = await apiRequest(`operator/amenities/${amenityId}`, {
                method: 'PUT',
                body: JSON.stringify(amenityData)
            });
        } else {
            response = await apiRequest('operator/amenities', {
                method: 'POST',
                body: JSON.stringify(amenityData)
            });
        }

        showAlert(isEdit ? 'Commodité modifiée avec succès' : 'Commodité créée avec succès', 'success');

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('amenityModal'));
        modal.hide();

        // Recharger les données
        await loadAmenities();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        showAlert(error.message || 'Erreur lors de la sauvegarde', 'danger');
    }
}

// Supprimer une commodité
async function deleteAmenity(amenityId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette commodité ? Cette action est irréversible.')) {
        return;
    }

    try {
        await apiRequest(`operator/amenities/${amenityId}`, {
            method: 'DELETE'
        });

        showAlert('Commodité supprimée avec succès', 'success');
        await loadAmenities();

    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        showAlert(error.message || 'Erreur lors de la suppression', 'danger');
    }
}

// ==================== FONCTIONS UTILITAIRES ====================

// Mettre à jour les titres de section dans le dashboard principal
function updateSectionTitles() {
    const titles = {
        'dashboard': 'Tableau de bord',
        'locations': 'Gestion des Lieux/Villes',
        'stops': 'Gestion des Arrêts',
        'amenities': 'Gestion des Commodités',
        'seatPlans': 'Gestion des Plans de Sièges',
        'buses': 'Gestion des Bus',
        'routes': 'Gestion des Itinéraires',
        'trips': 'Gestion des Voyages',
        'pricing': 'Gestion de la Tarification',
        'bookings': 'Gestion des Réservations',
        'payments': 'Suivi des Paiements',
        'users': 'Gestion des Utilisateurs',
        'reports': 'Rapports et analyses',
        'validation': 'Validation des tickets'
    };

    return titles;
}

// Charger les données spécifiques à chaque section (extension de la fonction existante)
async function loadSectionData(sectionName) {
    try {
        switch (sectionName) {
            case 'locations':
                await loadLocations();
                break;
            case 'stops':
                await loadStops();
                break;
            case 'amenities':
                await loadAmenities();
                break;
            case 'seatPlans':
                await loadSeatPlans();
                break;
            case 'routes':
                await loadRoutes();
                break;
            case 'pricing':
                await loadPricing();
                break;
            case 'payments':
                await loadPayments();
                break;
            case 'users':
                await loadUsers();
                break;
            case 'bookings':
                await loadBookings();
                break;
            case 'trips':
                await loadTrips();
                break;
            case 'buses':
                await loadBuses();
                break;
            case 'seats':
                await loadSeats();
                break;
            case 'reports':
                // Les rapports sont générés à la demande
                break;
            case 'dashboard':
                // Le dashboard est géré séparément
                break;
            default:
                console.log(`Section ${sectionName} non reconnue`);
        }

        // Mettre à jour les selects après chargement des données
        updateAllSelects();

    } catch (error) {
        console.error(`Erreur lors du chargement de la section ${sectionName}:`, error);
        showAlert(`Erreur lors du chargement de la section ${sectionName}`, 'danger');
    }
}

// ==================== GESTION DES ITINÉRAIRES ====================

// Charger les itinéraires
async function loadRoutes() {
    try {
        const response = await apiRequest('operator/routes');
        routesData = response.routes || [];
        window.routesData = routesData; // Mettre à jour la référence globale
        displayRoutes(routesData);
        populateRouteSelects();
    } catch (error) {
        console.error('Erreur lors du chargement des itinéraires:', error);
        showAlert('Erreur lors du chargement des itinéraires', 'danger');
    }
}

// Afficher les itinéraires
function displayRoutes(routes) {
    const container = document.getElementById('routesTableContainer');

    if (!routes || routes.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-route fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun itinéraire trouvé</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom de l'itinéraire</th>
                        <th>Départ</th>
                        <th>Destination</th>
                        <th>Distance</th>
                        <th>Durée</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    routes.forEach(route => {
        const statusClass = route.status === 'active' ? 'success' : 'secondary';
        const statusText = route.status === 'active' ? 'Actif' : 'Inactif';

        html += `
            <tr>
                <td>#${route.route_id}</td>
                <td><strong>${route.route_name}</strong></td>
                <td>${route.departure_location_name || 'N/A'}</td>
                <td>${route.arrival_location_name || 'N/A'}</td>
                <td>${route.distance ? route.distance + ' km' : 'N/A'}</td>
                <td>${route.duration || 'N/A'}</td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editRoute(${route.route_id})" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="manageRouteStops(${route.route_id})" title="Gérer les arrêts">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteRoute(${route.route_id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Afficher le modal de création d'itinéraire
async function showCreateRouteModal() {
    try {
        // Afficher un indicateur de chargement
        const modalElement = document.getElementById('routeModal');
        const modalBody = modalElement.querySelector('.modal-body');
        const originalContent = modalBody.innerHTML;

        modalBody.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div><p class="mt-2">Chargement des données...</p></div>';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Charger les données nécessaires (lieux)
        await ensureLocationsLoaded();

        // Restaurer le contenu original
        modalBody.innerHTML = originalContent;

        // Configurer le formulaire
        document.getElementById('routeModalTitle').textContent = 'Nouvel Itinéraire';
        document.getElementById('routeForm').reset();
        document.getElementById('routeId').value = '';
        document.getElementById('routeStatus').value = 'active';

        // Peupler les selects avec les données chargées
        populateLocationSelects();

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal itinéraire:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Modifier un itinéraire
async function editRoute(routeId) {
    const route = routesData.find(r => r.route_id == routeId);
    if (!route) {
        showAlert('Itinéraire non trouvé', 'danger');
        return;
    }

    try {
        // Afficher un indicateur de chargement
        const modalElement = document.getElementById('routeModal');
        const modalBody = modalElement.querySelector('.modal-body');
        const originalContent = modalBody.innerHTML;

        modalBody.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div><p class="mt-2">Chargement des données...</p></div>';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Charger les données nécessaires (lieux)
        await ensureLocationsLoaded();

        // Restaurer le contenu original
        modalBody.innerHTML = originalContent;

        // Peupler les selects avec les données chargées
        populateLocationSelects();

        // Configurer le formulaire avec les données de l'itinéraire
        document.getElementById('routeModalTitle').textContent = 'Modifier l\'Itinéraire';
        document.getElementById('routeId').value = route.route_id;
        document.getElementById('routeName').value = route.route_name;
        document.getElementById('routeDescription').value = route.description || '';
        document.getElementById('routeDepartureLocationId').value = route.departure_location_id;
        document.getElementById('routeDestinationLocationId').value = route.destination_location_id;
        document.getElementById('routeDistance').value = route.distance || '';
        document.getElementById('routeDuration').value = route.duration || '';
        document.getElementById('routeStatus').value = route.status;

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal itinéraire:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Sauvegarder un itinéraire
async function saveRoute() {
    const form = document.getElementById('routeForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const routeId = document.getElementById('routeId').value;
    const isEdit = !!routeId;

    const routeData = {
        route_name: document.getElementById('routeName').value,
        description: document.getElementById('routeDescription').value,
        departure_location_id: parseInt(document.getElementById('routeDepartureLocationId').value),
        destination_location_id: parseInt(document.getElementById('routeDestinationLocationId').value),
        distance: parseFloat(document.getElementById('routeDistance').value),
        duration: document.getElementById('routeDuration').value,
        status: document.getElementById('routeStatus').value
    };

    try {
        if (isEdit) {
            await apiRequest(`operator/routes/${routeId}`, {
                method: 'PUT',
                body: JSON.stringify(routeData)
            });
        } else {
            await apiRequest('operator/routes', {
                method: 'POST',
                body: JSON.stringify(routeData)
            });
        }

        showAlert(isEdit ? 'Itinéraire modifié avec succès' : 'Itinéraire créé avec succès', 'success');

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('routeModal'));
        modal.hide();

        // Recharger les données
        await loadRoutes();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        showAlert(error.message || 'Erreur lors de la sauvegarde', 'danger');
    }
}

// Supprimer un itinéraire
async function deleteRoute(routeId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet itinéraire ? Cette action est irréversible.')) {
        return;
    }

    try {
        await apiRequest(`operator/routes/${routeId}`, {
            method: 'DELETE'
        });

        showAlert('Itinéraire supprimé avec succès', 'success');
        await loadRoutes();

    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        showAlert(error.message || 'Erreur lors de la suppression', 'danger');
    }
}

// Filtrer les itinéraires
function filterRoutes() {
    const statusFilter = document.getElementById('routeStatusFilter').value;
    const departureFilter = document.getElementById('routeDepartureFilter').value;
    const searchFilter = document.getElementById('routeSearchFilter').value.toLowerCase();

    let filteredRoutes = routesData.filter(route => {
        const matchesStatus = !statusFilter || route.status === statusFilter;
        const matchesDeparture = !departureFilter || route.departure_location_id == departureFilter;
        const matchesSearch = !searchFilter ||
            route.route_name.toLowerCase().includes(searchFilter) ||
            (route.description && route.description.toLowerCase().includes(searchFilter));

        return matchesStatus && matchesDeparture && matchesSearch;
    });

    displayRoutes(filteredRoutes);
}

// Effacer les filtres d'itinéraires
function clearRouteFilters() {
    document.getElementById('routeStatusFilter').value = '';
    document.getElementById('routeDepartureFilter').value = '';
    document.getElementById('routeSearchFilter').value = '';
    displayRoutes(routesData);
}

// Peupler les selects d'itinéraires
function populateRouteSelects() {
    // Tous les selects qui utilisent les itinéraires
    const selects = ['tripRouteId', 'pricingRouteId', 'tripRouteFilter', 'pricingRouteFilter'];

    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // Garder la première option
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            routesData.forEach(route => {
                if (route.status === 'active') {
                    const option = document.createElement('option');
                    option.value = route.route_id;
                    option.textContent = route.route_name;
                    select.appendChild(option);
                }
            });
        }
    });
}

// ==================== GESTION DES ARRÊTS D'ITINÉRAIRE ====================

// Gérer les arrêts d'un itinéraire
async function manageRouteStops(routeId) {
    const route = routesData.find(r => r.route_id == routeId);
    if (!route) {
        showAlert('Itinéraire non trouvé', 'danger');
        return;
    }

    try {
        // Afficher le modal avec un indicateur de chargement
        const modal = new bootstrap.Modal(document.getElementById('routeStopsModal'));
        document.getElementById('routeStopsModalTitle').textContent = `Gestion des Arrêts - ${route.route_name}`;
        document.getElementById('routeStopsRouteId').value = routeId;

        // Réinitialiser le formulaire
        document.getElementById('routeStopForm').reset();

        modal.show();

        // Charger les données nécessaires
        await Promise.all([
            ensureStopsLoaded(),
            loadRouteStops(routeId)
        ]);

        // Peupler le select des arrêts
        populateStopSelects();

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal arrêts:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Charger les arrêts d'un itinéraire
async function loadRouteStops(routeId) {
    try {
        const response = await apiRequest(`operator/routes/${routeId}/stops`);
        const routeStops = response.stops || [];
        displayRouteStops(routeStops);

        // Calculer le prochain ordre
        const nextOrder = routeStops.length > 0 ? Math.max(...routeStops.map(s => s.stop_order)) + 1 : 1;
        document.getElementById('routeStopOrder').value = nextOrder;

        return routeStops;
    } catch (error) {
        console.error('Erreur lors du chargement des arrêts de l\'itinéraire:', error);
        document.getElementById('routeStopsContainer').innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Erreur lors du chargement des arrêts
            </div>
        `;
        return [];
    }
}

// Afficher les arrêts d'un itinéraire
function displayRouteStops(routeStops) {
    const container = document.getElementById('routeStopsContainer');

    if (!routeStops || routeStops.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-map-marker-alt fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun arrêt configuré pour cet itinéraire</p>
            </div>
        `;
        return;
    }

    // Trier par ordre
    routeStops.sort((a, b) => a.stop_order - b.stop_order);

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Ordre</th>
                        <th>Arrêt</th>
                        <th>Type</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    routeStops.forEach(stop => {
        const typeClass = stop.stop_type === 'boarding' ? 'success' :
                         stop.stop_type === 'dropping' ? 'danger' : 'warning';
        const typeText = stop.stop_type === 'boarding' ? 'Embarquement' :
                        stop.stop_type === 'dropping' ? 'Débarquement' : 'Intermédiaire';

        html += `
            <tr>
                <td><span class="badge bg-primary">${stop.stop_order}</span></td>
                <td>
                    <strong>${stop.stop_name}</strong>
                    ${stop.address ? `<br><small class="text-muted">${stop.address}</small>` : ''}
                </td>
                <td><span class="badge bg-${typeClass}">${typeText}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editRouteStop(${stop.route_stop_id})" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteRouteStop(${stop.route_stop_id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Peupler les selects d'arrêts
function populateStopSelects() {
    const select = document.getElementById('routeStopStopId');
    if (select && stopsData) {
        // Garder la première option
        const firstOption = select.querySelector('option');
        select.innerHTML = '';
        if (firstOption) {
            select.appendChild(firstOption);
        }

        stopsData.forEach(stop => {
            const option = document.createElement('option');
            option.value = stop.stop_id;
            option.textContent = stop.stop_name;
            select.appendChild(option);
        });
    }
}

// Ajouter un arrêt à un itinéraire
async function addRouteStop() {
    const form = document.getElementById('routeStopForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const routeId = document.getElementById('routeStopsRouteId').value;
    const routeStopData = {
        route_id: parseInt(routeId),
        stop_id: parseInt(document.getElementById('routeStopStopId').value),
        stop_order: parseInt(document.getElementById('routeStopOrder').value),
        stop_type: document.getElementById('routeStopType').value
    };

    try {
        await apiRequest('operator/route-stops', {
            method: 'POST',
            body: JSON.stringify(routeStopData)
        });

        showAlert('Arrêt ajouté avec succès', 'success');

        // Recharger les arrêts et réinitialiser le formulaire
        await loadRouteStops(routeId);
        form.reset();

    } catch (error) {
        console.error('Erreur lors de l\'ajout de l\'arrêt:', error);
        showAlert(error.message || 'Erreur lors de l\'ajout de l\'arrêt', 'danger');
    }
}

// Modifier un arrêt d'itinéraire
async function editRouteStop(routeStopId) {
    // Pour simplifier, on peut implémenter une modification inline ou un modal séparé
    // Ici, on va demander les nouvelles valeurs via des prompts
    const newOrder = prompt('Nouvel ordre:');
    const newType = prompt('Nouveau type (boarding/intermediate/dropping):');

    if (newOrder && newType) {
        try {
            await apiRequest(`operator/route-stops/${routeStopId}`, {
                method: 'PUT',
                body: JSON.stringify({
                    stop_order: parseInt(newOrder),
                    stop_type: newType
                })
            });

            showAlert('Arrêt modifié avec succès', 'success');

            // Recharger les arrêts
            const routeId = document.getElementById('routeStopsRouteId').value;
            await loadRouteStops(routeId);

        } catch (error) {
            console.error('Erreur lors de la modification de l\'arrêt:', error);
            showAlert(error.message || 'Erreur lors de la modification de l\'arrêt', 'danger');
        }
    }
}

// Supprimer un arrêt d'itinéraire
async function deleteRouteStop(routeStopId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet arrêt de l\'itinéraire ?')) {
        return;
    }

    try {
        await apiRequest(`operator/route-stops/${routeStopId}`, {
            method: 'DELETE'
        });

        showAlert('Arrêt supprimé avec succès', 'success');

        // Recharger les arrêts
        const routeId = document.getElementById('routeStopsRouteId').value;
        await loadRouteStops(routeId);

    } catch (error) {
        console.error('Erreur lors de la suppression de l\'arrêt:', error);
        showAlert(error.message || 'Erreur lors de la suppression de l\'arrêt', 'danger');
    }
}

// S'assurer que les arrêts sont chargés
async function ensureStopsLoaded() {
    if (!stopsData || stopsData.length === 0) {
        await loadStops();
    }
}

// ==================== GESTION DE LA TARIFICATION ====================

// Charger la tarification
async function loadPricing() {
    try {
        const response = await apiRequest('operator/pricing');
        pricingData = response.pricing || [];
        displayPricing(pricingData);
    } catch (error) {
        console.error('Erreur lors du chargement de la tarification:', error);
        showAlert('Erreur lors du chargement de la tarification', 'danger');

        // Fallback vers des données mockées en cas d'erreur
        pricingData = [
            {
                pricing_id: 1,
                route_id: 1,
                route_name: "Cotonou - Porto-Novo",
                bus_type: "standard",
                seat_type: "standard",
                price: 1500,
                start_date: "2024-01-01",
                end_date: "2024-12-31"
            },
            {
                pricing_id: 2,
                route_id: 1,
                route_name: "Cotonou - Porto-Novo",
                bus_type: "vip",
                seat_type: "premium",
                price: 2500,
                start_date: "2024-01-01",
                end_date: "2024-12-31"
            }
        ];
        displayPricing(pricingData);
    }
}

// Afficher la tarification
function displayPricing(pricing) {
    const container = document.getElementById('pricingTableContainer');

    if (!pricing || pricing.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-tags fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucune tarification trouvée</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Itinéraire</th>
                        <th>Type de bus</th>
                        <th>Type de siège</th>
                        <th>Prix</th>
                        <th>Période</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    pricing.forEach(price => {
        const busTypeText = price.bus_type === 'vip' ? 'VIP' : 'Standard';
        const seatTypeText = price.seat_type === 'premium' ? 'Premium' : 'Standard';
        const period = price.start_date && price.end_date ?
            `${formatDate(price.start_date)} - ${formatDate(price.end_date)}` : 'Non définie';

        html += `
            <tr>
                <td>#${price.pricing_id}</td>
                <td>${price.route_name || 'Tous les itinéraires'}</td>
                <td><span class="badge bg-${price.bus_type === 'vip' ? 'warning' : 'secondary'}">${busTypeText}</span></td>
                <td><span class="badge bg-${price.seat_type === 'premium' ? 'success' : 'info'}">${seatTypeText}</span></td>
                <td><strong>${formatCurrency(price.price)}</strong></td>
                <td><small class="text-muted">${period}</small></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editPricing(${price.pricing_id})" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deletePricing(${price.pricing_id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Afficher le modal de création de tarification
async function showCreatePricingModal() {
    try {
        // Afficher un indicateur de chargement
        const modalElement = document.getElementById('pricingModal');
        const modalBody = modalElement.querySelector('.modal-body');
        const originalContent = modalBody.innerHTML;

        modalBody.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div><p class="mt-2">Chargement des données...</p></div>';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Charger les données nécessaires (itinéraires)
        await ensureRoutesLoaded();

        // Restaurer le contenu original
        modalBody.innerHTML = originalContent;

        // Configurer le formulaire
        document.getElementById('pricingModalTitle').textContent = 'Nouvelle Tarification';
        document.getElementById('pricingForm').reset();
        document.getElementById('pricingId').value = '';

        // Peupler les selects avec les données chargées
        populateRouteSelects();

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal tarification:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Modifier une tarification
async function editPricing(pricingId) {
    const pricing = pricingData.find(p => p.pricing_id == pricingId);
    if (!pricing) {
        showAlert('Tarification non trouvée', 'danger');
        return;
    }

    try {
        // Afficher un indicateur de chargement
        const modalElement = document.getElementById('pricingModal');
        const modalBody = modalElement.querySelector('.modal-body');
        const originalContent = modalBody.innerHTML;

        modalBody.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div><p class="mt-2">Chargement des données...</p></div>';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Charger les données nécessaires (itinéraires)
        await ensureRoutesLoaded();

        // Restaurer le contenu original
        modalBody.innerHTML = originalContent;

        // Peupler les selects avec les données chargées
        populateRouteSelects();

        // Configurer le formulaire avec les données de la tarification
        document.getElementById('pricingModalTitle').textContent = 'Modifier la Tarification';
        document.getElementById('pricingId').value = pricing.pricing_id;
        document.getElementById('pricingRouteId').value = pricing.route_id || '';
        document.getElementById('pricingBusType').value = pricing.bus_type;
        document.getElementById('pricingSeatType').value = pricing.seat_type;
        document.getElementById('pricingPrice').value = pricing.price;
        document.getElementById('pricingStartDate').value = pricing.start_date || '';
        document.getElementById('pricingEndDate').value = pricing.end_date || '';

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal tarification:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Sauvegarder une tarification
async function savePricing() {
    const form = document.getElementById('pricingForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const pricingId = document.getElementById('pricingId').value;
    const isEdit = !!pricingId;

    const pricingData = {
        route_id: document.getElementById('pricingRouteId').value || null,
        bus_type: document.getElementById('pricingBusType').value,
        seat_type: document.getElementById('pricingSeatType').value,
        price: parseFloat(document.getElementById('pricingPrice').value),
        start_date: document.getElementById('pricingStartDate').value || null,
        end_date: document.getElementById('pricingEndDate').value || null
    };

    try {
        if (isEdit) {
            await apiRequest(`operator/pricing/${pricingId}`, {
                method: 'PUT',
                body: JSON.stringify(pricingData)
            });
        } else {
            await apiRequest('operator/pricing', {
                method: 'POST',
                body: JSON.stringify(pricingData)
            });
        }

        showAlert(isEdit ? 'Tarification modifiée avec succès' : 'Tarification créée avec succès', 'success');

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('pricingModal'));
        modal.hide();

        // Recharger les données
        await loadPricing();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        showAlert(error.message || 'Erreur lors de la sauvegarde', 'danger');
    }
}

// Supprimer une tarification
async function deletePricing(pricingId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette tarification ? Cette action est irréversible.')) {
        return;
    }

    try {
        await apiRequest(`operator/pricing/${pricingId}`, {
            method: 'DELETE'
        });

        showAlert('Tarification supprimée avec succès', 'success');
        await loadPricing();

    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        showAlert(error.message || 'Erreur lors de la suppression', 'danger');
    }
}

// Filtrer la tarification
function filterPricing() {
    const routeFilter = document.getElementById('pricingRouteFilter').value;
    const busTypeFilter = document.getElementById('pricingBusTypeFilter').value;
    const seatTypeFilter = document.getElementById('pricingSeatTypeFilter').value;

    let filteredPricing = pricingData.filter(pricing => {
        const matchesRoute = !routeFilter || pricing.route_id == routeFilter;
        const matchesBusType = !busTypeFilter || pricing.bus_type === busTypeFilter;
        const matchesSeatType = !seatTypeFilter || pricing.seat_type === seatTypeFilter;

        return matchesRoute && matchesBusType && matchesSeatType;
    });

    displayPricing(filteredPricing);
}

// Effacer les filtres de tarification
function clearPricingFilters() {
    document.getElementById('pricingRouteFilter').value = '';
    document.getElementById('pricingBusTypeFilter').value = '';
    document.getElementById('pricingSeatTypeFilter').value = '';
    displayPricing(pricingData);
}

// Filtrer les utilisateurs
function filterUsers() {
    const statusFilter = document.getElementById('userStatusFilter').value;
    const verificationFilter = document.getElementById('userVerificationFilter').value;
    const roleFilter = document.getElementById('userRoleFilter').value;
    const searchFilter = document.getElementById('userSearchFilter').value.toLowerCase();

    let filteredUsers = usersData.filter(user => {
        const matchesStatus = !statusFilter || user.status === statusFilter;
        const matchesVerification = !verificationFilter || user.verification_status === verificationFilter;
        const matchesRole = !roleFilter || (user.roles && user.roles.includes(roleFilter));
        const matchesSearch = !searchFilter ||
            user.first_name.toLowerCase().includes(searchFilter) ||
            user.last_name.toLowerCase().includes(searchFilter) ||
            user.email.toLowerCase().includes(searchFilter) ||
            user.phone.includes(searchFilter);

        return matchesStatus && matchesVerification && matchesRole && matchesSearch;
    });

    displayUsers(filteredUsers);
}

// Effacer les filtres d'utilisateurs
function clearUserFilters() {
    document.getElementById('userStatusFilter').value = '';
    document.getElementById('userVerificationFilter').value = '';
    document.getElementById('userRoleFilter').value = '';
    document.getElementById('userSearchFilter').value = '';
    displayUsers(usersData);
}

// ==================== GESTION DES PAIEMENTS ====================

// Charger les paiements
async function loadPayments() {
    try {
        const response = await apiRequest('operator/payments');
        paymentsData = response.payments || [];
        displayPayments(paymentsData);
    } catch (error) {
        console.error('Erreur lors du chargement des paiements:', error);
        showAlert('Erreur lors du chargement des paiements', 'danger');

        // Fallback vers des données mockées en cas d'erreur
        paymentsData = [
            {
                payment_id: 1,
                booking_id: 1,
                amount: 1500,
                currency: "XOF",
                payment_method: "mobile_money",
                payment_provider: "fedapay",
                payment_reference: "FP123456",
                payment_status: "successful",
                payment_date: "2024-01-15T10:30:00"
            },
            {
                payment_id: 2,
                booking_id: 2,
                amount: 2500,
                currency: "XOF",
                payment_method: "credit_card",
                payment_provider: "fedapay",
                payment_reference: "FP789012",
                payment_status: "pending",
                payment_date: "2024-01-15T14:45:00"
            }
        ];
        displayPayments(paymentsData);
    }
}

// Afficher les paiements
function displayPayments(payments) {
    const container = document.getElementById('paymentsTableContainer');

    if (!payments || payments.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-credit-card fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun paiement trouvé</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Réservation</th>
                        <th>Montant</th>
                        <th>Méthode</th>
                        <th>Fournisseur</th>
                        <th>Référence</th>
                        <th>Statut</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    payments.forEach(payment => {
        const statusClass = payment.payment_status === 'successful' ? 'success' :
                           payment.payment_status === 'failed' ? 'danger' :
                           payment.payment_status === 'refunded' ? 'warning' : 'secondary';
        const statusText = payment.payment_status === 'successful' ? 'Réussi' :
                          payment.payment_status === 'failed' ? 'Échoué' :
                          payment.payment_status === 'refunded' ? 'Remboursé' : 'En attente';

        html += `
            <tr>
                <td>#${payment.payment_id}</td>
                <td>#${payment.booking_id}</td>
                <td><strong>${formatCurrency(payment.amount)} ${payment.currency}</strong></td>
                <td>${payment.payment_method}</td>
                <td>${payment.payment_provider || 'N/A'}</td>
                <td><small class="text-muted">${payment.payment_reference || 'N/A'}</small></td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td>${formatDateTime(payment.payment_date)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewPaymentDetails(${payment.payment_id})" title="Voir détails">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${payment.payment_status === 'successful' ?
                        `<button class="btn btn-sm btn-outline-warning" onclick="refundPayment(${payment.payment_id})" title="Rembourser">
                            <i class="fas fa-undo"></i>
                        </button>` : ''}
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Voir les détails d'un paiement
function viewPaymentDetails(paymentId) {
    const payment = paymentsData.find(p => p.payment_id == paymentId);
    if (!payment) {
        showAlert('Paiement non trouvé', 'danger');
        return;
    }

    // Afficher les détails dans un modal ou une section dédiée
    showAlert(`Détails du paiement #${paymentId} - Montant: ${formatCurrency(payment.amount)} ${payment.currency}`, 'info');
}

// Rembourser un paiement
async function refundPayment(paymentId) {
    if (!confirm('Êtes-vous sûr de vouloir rembourser ce paiement ?')) {
        return;
    }

    try {
        await apiRequest(`operator/payments/${paymentId}/refund`, {
            method: 'POST'
        });

        showAlert('Paiement remboursé avec succès', 'success');
        await loadPayments();

    } catch (error) {
        console.error('Erreur lors du remboursement:', error);
        showAlert(error.message || 'Erreur lors du remboursement', 'danger');
    }
}

// Filtrer les paiements
function filterPayments() {
    const statusFilter = document.getElementById('paymentStatusFilter').value;
    const methodFilter = document.getElementById('paymentMethodFilter').value;
    const dateFromFilter = document.getElementById('paymentDateFromFilter').value;
    const dateToFilter = document.getElementById('paymentDateToFilter').value;

    let filteredPayments = paymentsData.filter(payment => {
        const matchesStatus = !statusFilter || payment.payment_status === statusFilter;
        const matchesMethod = !methodFilter || payment.payment_method === methodFilter;

        let matchesDateRange = true;
        if (dateFromFilter || dateToFilter) {
            const paymentDate = new Date(payment.payment_date);
            if (dateFromFilter) {
                matchesDateRange = matchesDateRange && paymentDate >= new Date(dateFromFilter);
            }
            if (dateToFilter) {
                matchesDateRange = matchesDateRange && paymentDate <= new Date(dateToFilter);
            }
        }

        return matchesStatus && matchesMethod && matchesDateRange;
    });

    displayPayments(filteredPayments);
}

// Effacer les filtres de paiements
function clearPaymentFilters() {
    document.getElementById('paymentStatusFilter').value = '';
    document.getElementById('paymentMethodFilter').value = '';
    document.getElementById('paymentDateFromFilter').value = '';
    document.getElementById('paymentDateToFilter').value = '';
    displayPayments(paymentsData);
}

// Actualiser les paiements
function refreshPayments() {
    loadPayments();
}

// Exporter les paiements
function exportPayments() {
    // Créer un CSV des paiements
    const csvContent = "data:text/csv;charset=utf-8," +
        "ID,Réservation,Montant,Devise,Méthode,Fournisseur,Référence,Statut,Date\n" +
        paymentsData.map(payment =>
            `${payment.payment_id},${payment.booking_id},${payment.amount},${payment.currency},${payment.payment_method},${payment.payment_provider || ''},${payment.payment_reference || ''},${payment.payment_status},${payment.payment_date}`
        ).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `paiements_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// ==================== GESTION DES UTILISATEURS ====================

// Charger les utilisateurs
async function loadUsers() {
    try {
        const response = await apiRequest('operator/users');
        usersData = response.users || [];
        window.usersData = usersData; // Mettre à jour la référence globale
        displayUsers(usersData);
        populateUserSelects();
    } catch (error) {
        console.error('Erreur lors du chargement des utilisateurs:', error);
        showAlert('Erreur lors du chargement des utilisateurs', 'danger');

        // Fallback vers des données mockées en cas d'erreur
        usersData = [
            {
                user_id: 1,
                first_name: "Jean",
                last_name: "Dupont",
                email: "<EMAIL>",
                phone: "+229 12345678",
                roles: "driver,traveler",
                status: "active",
                verification_status: "verified",
                date_of_birth: "1985-05-15"
            },
            {
                user_id: 2,
                first_name: "Marie",
                last_name: "Martin",
                email: "<EMAIL>",
                phone: "+229 87654321",
                roles: "controller",
                status: "active",
                verification_status: "pending",
                date_of_birth: "1990-08-22"
            }
        ];
        displayUsers(usersData);
        populateUserSelects();
    }
}

// Afficher les utilisateurs
function displayUsers(users) {
    const container = document.getElementById('usersTableContainer');

    if (!users || users.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-users fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun utilisateur trouvé</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom complet</th>
                        <th>Email</th>
                        <th>Téléphone</th>
                        <th>Rôles</th>
                        <th>Statut</th>
                        <th>Vérification</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    users.forEach(user => {
        const statusClass = user.status === 'active' ? 'success' :
                           user.status === 'suspended' ? 'danger' : 'secondary';
        const statusText = user.status === 'active' ? 'Actif' :
                          user.status === 'suspended' ? 'Suspendu' : 'Inactif';

        const verificationClass = user.verification_status === 'verified' ? 'success' :
                                 user.verification_status === 'rejected' ? 'danger' : 'warning';
        const verificationText = user.verification_status === 'verified' ? 'Vérifié' :
                                user.verification_status === 'rejected' ? 'Rejeté' : 'En attente';

        const roles = user.roles ? user.roles.split(',').map(role =>
            `<span class="badge bg-info me-1">${role}</span>`
        ).join('') : '<span class="badge bg-secondary">Aucun</span>';

        html += `
            <tr>
                <td>#${user.user_id}</td>
                <td><strong>${user.first_name} ${user.last_name}</strong></td>
                <td>${user.email}</td>
                <td>${user.phone}</td>
                <td>${roles}</td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td><span class="badge bg-${verificationClass}">${verificationText}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editUser(${user.user_id})" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.user_id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Afficher le modal de création d'utilisateur
function showCreateUserModal() {
    document.getElementById('userModalTitle').textContent = 'Nouvel Utilisateur';
    document.getElementById('userForm').reset();
    document.getElementById('userId').value = '';
    document.getElementById('userStatus').value = 'active';
    document.getElementById('userVerificationStatus').value = 'pending';

    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

// Modifier un utilisateur
function editUser(userId) {
    const user = usersData.find(u => u.user_id == userId);
    if (!user) {
        showAlert('Utilisateur non trouvé', 'danger');
        return;
    }

    document.getElementById('userModalTitle').textContent = 'Modifier l\'Utilisateur';
    document.getElementById('userId').value = user.user_id;
    document.getElementById('userFirstName').value = user.first_name;
    document.getElementById('userLastName').value = user.last_name;
    document.getElementById('userEmail').value = user.email;
    document.getElementById('userPhone').value = user.phone;
    document.getElementById('userDateOfBirth').value = user.date_of_birth || '';
    document.getElementById('userStatus').value = user.status;
    document.getElementById('userVerificationStatus').value = user.verification_status;

    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

// Sauvegarder un utilisateur
async function saveUser() {
    const form = document.getElementById('userForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const userId = document.getElementById('userId').value;
    const isEdit = !!userId;

    const userData = {
        first_name: document.getElementById('userFirstName').value,
        last_name: document.getElementById('userLastName').value,
        email: document.getElementById('userEmail').value,
        phone: document.getElementById('userPhone').value,
        date_of_birth: document.getElementById('userDateOfBirth').value || null,
        status: document.getElementById('userStatus').value,
        verification_status: document.getElementById('userVerificationStatus').value
    };

    // Ajouter le mot de passe seulement pour la création
    if (!isEdit) {
        userData.password = document.getElementById('userPassword').value;
    }

    try {
        if (isEdit) {
            await apiRequest(`operator/users/${userId}`, {
                method: 'PUT',
                body: JSON.stringify(userData)
            });
        } else {
            await apiRequest('operator/users', {
                method: 'POST',
                body: JSON.stringify(userData)
            });
        }

        showAlert(isEdit ? 'Utilisateur modifié avec succès' : 'Utilisateur créé avec succès', 'success');

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
        modal.hide();

        // Recharger les données
        await loadUsers();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        showAlert(error.message || 'Erreur lors de la sauvegarde', 'danger');
    }
}

// Supprimer un utilisateur
async function deleteUser(userId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ? Cette action est irréversible.')) {
        return;
    }

    try {
        await apiRequest(`operator/users/${userId}`, {
            method: 'DELETE'
        });

        showAlert('Utilisateur supprimé avec succès', 'success');
        await loadUsers();

    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        showAlert(error.message || 'Erreur lors de la suppression', 'danger');
    }
}

// ==================== GESTION DES BUS ====================

// Charger les bus
async function loadBuses() {
    try {
        const response = await apiRequest('operator/buses');
        busesData = response.buses || [];
        window.busesData = busesData; // Mettre à jour la référence globale
        displayBuses(busesData);
        populateBusSelects();
    } catch (error) {
        console.error('Erreur lors du chargement des bus:', error);
        showAlert('Erreur lors du chargement des bus', 'danger');
    }
}

// Afficher les bus
function displayBuses(buses) {
    const container = document.getElementById('busesGridContainer');

    if (!buses || buses.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-bus fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun bus trouvé</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Immatriculation</th>
                        <th>Marque/Modèle</th>
                        <th>Capacité</th>
                        <th>Type</th>
                        <th>Année</th>
                        <th>Plan de sièges</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    buses.forEach(bus => {
        const statusClass = bus.status === 'active' ? 'success' :
                           bus.status === 'under_maintenance' ? 'warning' : 'secondary';
        const statusText = bus.status === 'active' ? 'Actif' :
                          bus.status === 'under_maintenance' ? 'En maintenance' : 'Inactif';

        const typeClass = bus.bus_type === 'vip' ? 'warning' : 'secondary';
        const typeText = bus.bus_type === 'vip' ? 'VIP' : 'Standard';

        html += `
            <tr>
                <td>#${bus.bus_id}</td>
                <td><strong>${bus.registration_number}</strong></td>
                <td>${bus.brand} ${bus.model}</td>
                <td>${bus.capacity} places</td>
                <td><span class="badge bg-${typeClass}">${typeText}</span></td>
                <td>${bus.year_manufactured || 'N/A'}</td>
                <td>${bus.seat_plan_config || 'N/A'}</td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editBus(${bus.bus_id})" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="manageBusSeats(${bus.bus_id})" title="Gérer les sièges">
                        <i class="fas fa-chair"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteBus(${bus.bus_id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Afficher le modal de création de bus
async function showCreateBusModal() {
    try {
        // Afficher un indicateur de chargement
        const modalElement = document.getElementById('busModal');
        const modalBody = modalElement.querySelector('.modal-body');
        const originalContent = modalBody.innerHTML;

        modalBody.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div><p class="mt-2">Chargement des données...</p></div>';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Charger les données nécessaires (plans de sièges)
        await Promise.all([
            ensureSeatPlansLoaded(),
            ensureAmenitiesLoaded()
        ]);

        // Restaurer le contenu original
        modalBody.innerHTML = originalContent;

        // Configurer le formulaire
        document.getElementById('busModalTitle').textContent = 'Nouveau Bus';
        document.getElementById('busForm').reset();
        document.getElementById('busId').value = '';
        document.getElementById('busStatus').value = 'active';
        document.getElementById('busType').value = 'standard';

        // Peupler les selects avec les données chargées
        populateSeatPlanSelects();
        populateBusAmenities();

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal bus:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Modifier un bus
async function editBus(busId) {
    const bus = busesData.find(b => b.bus_id == busId);
    if (!bus) {
        showAlert('Bus non trouvé', 'danger');
        return;
    }

    try {
        // Afficher un indicateur de chargement
        const modalElement = document.getElementById('busModal');
        const modalBody = modalElement.querySelector('.modal-body');
        const originalContent = modalBody.innerHTML;

        modalBody.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div><p class="mt-2">Chargement des données...</p></div>';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Charger les données nécessaires (plans de sièges et commodités)
        await Promise.all([
            ensureSeatPlansLoaded(),
            ensureAmenitiesLoaded()
        ]);

        // Charger les commodités du bus
        const busAmenities = await loadBusAmenities(busId);

        // Restaurer le contenu original
        modalBody.innerHTML = originalContent;

        // Peupler les selects avec les données chargées
        populateSeatPlanSelects();
        populateBusAmenities(busAmenities);

        // Configurer le formulaire avec les données du bus
        document.getElementById('busModalTitle').textContent = 'Modifier le Bus';
        document.getElementById('busId').value = bus.bus_id;
        document.getElementById('busRegistrationNumber').value = bus.registration_number;
        document.getElementById('busBrand').value = bus.brand;
        document.getElementById('busModel').value = bus.model;
        document.getElementById('busCapacity').value = bus.capacity;
        document.getElementById('busType').value = bus.bus_type;
        document.getElementById('busSeatPlanId').value = bus.seat_plan_id || '';
        document.getElementById('busYearManufactured').value = bus.year_manufactured || '';
        document.getElementById('busStatus').value = bus.status;

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal bus:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Sauvegarder un bus
async function saveBus() {
    const form = document.getElementById('busForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const busId = document.getElementById('busId').value;
    const isEdit = !!busId;

    const busData = {
        registration_number: document.getElementById('busRegistrationNumber').value,
        brand: document.getElementById('busBrand').value,
        model: document.getElementById('busModel').value,
        capacity: parseInt(document.getElementById('busCapacity').value),
        bus_type: document.getElementById('busType').value,
        seat_plan_id: document.getElementById('busSeatPlanId').value || null,
        year_manufactured: document.getElementById('busYearManufactured').value || null,
        status: document.getElementById('busStatus').value
    };

    // Récupérer les commodités sélectionnées
    const selectedAmenities = getSelectedBusAmenities();

    try {
        let savedBusId = busId;

        if (isEdit) {
            await apiRequest(`operator/buses/${busId}`, {
                method: 'PUT',
                body: JSON.stringify(busData)
            });
        } else {
            const response = await apiRequest('operator/buses', {
                method: 'POST',
                body: JSON.stringify(busData)
            });
            savedBusId = response.bus_id;
        }

        // Sauvegarder les commodités
        if (selectedAmenities.length > 0 || isEdit) {
            await saveBusAmenities(savedBusId, selectedAmenities);
        }

        showAlert(isEdit ? 'Bus modifié avec succès' : 'Bus créé avec succès', 'success');

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('busModal'));
        modal.hide();

        // Recharger les données
        await loadBuses();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        showAlert(error.message || 'Erreur lors de la sauvegarde', 'danger');
    }
}

// Supprimer un bus
async function deleteBus(busId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce bus ? Cette action est irréversible.')) {
        return;
    }

    try {
        await apiRequest(`operator/buses/${busId}`, {
            method: 'DELETE'
        });

        showAlert('Bus supprimé avec succès', 'success');
        await loadBuses();

    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        showAlert(error.message || 'Erreur lors de la suppression', 'danger');
    }
}

// Peupler les selects de bus
function populateBusSelects() {
    // Tous les selects qui utilisent les bus
    const selects = ['tripBusId', 'seatBusFilter'];

    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // Garder la première option
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            busesData.forEach(bus => {
                const option = document.createElement('option');
                option.value = bus.bus_id;
                option.textContent = `${bus.registration_number} - ${bus.brand} ${bus.model}`;
                // Afficher le statut si le bus n'est pas actif, pour information
                if (bus.status !== 'active') {
                    option.textContent += ` (${getStatusText(bus.status)})`;
                    // Optionnellement, désactiver les bus non actifs ou ajouter un style distinct
                    // option.disabled = true;
                    // option.style.color = 'gray';
                }
                select.appendChild(option);
            });
        }
    });
}

// Filtrer les bus
function filterBuses() {
    const statusFilter = document.getElementById('busStatusFilter').value;
    const typeFilter = document.getElementById('busTypeFilter').value;
    const searchFilter = document.getElementById('busSearchFilter').value.toLowerCase();

    let filteredBuses = busesData.filter(bus => {
        const matchesStatus = !statusFilter || bus.status === statusFilter;
        const matchesType = !typeFilter || bus.bus_type === typeFilter;
        const matchesSearch = !searchFilter ||
            bus.registration_number.toLowerCase().includes(searchFilter) ||
            bus.brand.toLowerCase().includes(searchFilter) ||
            bus.model.toLowerCase().includes(searchFilter);

        return matchesStatus && matchesType && matchesSearch;
    });

    displayBuses(filteredBuses);
}

// Effacer les filtres de bus
function clearBusFilters() {
    document.getElementById('busStatusFilter').value = '';
    document.getElementById('busTypeFilter').value = '';
    document.getElementById('busSearchFilter').value = '';
    displayBuses(busesData);
}

// ==================== GESTION DES COMMODITÉS DE BUS ====================

// S'assurer que les commodités sont chargées
async function ensureAmenitiesLoaded() {
    if (!amenitiesData || amenitiesData.length === 0) {
        await loadAmenities();
    }
}

// Peupler les commodités dans le formulaire de bus
function populateBusAmenities(selectedAmenities = []) {
    const container = document.getElementById('busAmenitiesContainer');

    if (!amenitiesData || amenitiesData.length === 0) {
        container.innerHTML = '<div class="text-muted">Aucune commodité disponible</div>';
        return;
    }

    let html = '<div class="row">';

    amenitiesData.forEach(amenity => {
        const isSelected = selectedAmenities.some(selected => selected.amenity_id == amenity.amenity_id);

        html += `
            <div class="col-md-6 mb-2">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox"
                           id="amenity_${amenity.amenity_id}"
                           value="${amenity.amenity_id}"
                           ${isSelected ? 'checked' : ''}>
                    <label class="form-check-label" for="amenity_${amenity.amenity_id}">
                        <i class="fas fa-star text-warning me-1"></i>
                        ${amenity.amenity_name}
                    </label>
                    ${amenity.description ? `<small class="text-muted d-block">${amenity.description}</small>` : ''}
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

// Récupérer les commodités sélectionnées
function getSelectedBusAmenities() {
    const checkboxes = document.querySelectorAll('#busAmenitiesContainer input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(checkbox => parseInt(checkbox.value));
}

// Charger les commodités d'un bus spécifique
async function loadBusAmenities(busId) {
    try {
        const response = await apiRequest(`operator/buses/${busId}/amenities`);
        return response.amenities || [];
    } catch (error) {
        console.error('Erreur lors du chargement des commodités du bus:', error);
        return [];
    }
}

// Sauvegarder les commodités d'un bus
async function saveBusAmenities(busId, amenityIds) {
    try {
        await apiRequest(`operator/buses/${busId}/amenities`, {
            method: 'PUT',
            body: JSON.stringify({ amenity_ids: amenityIds })
        });
    } catch (error) {
        console.error('Erreur lors de la sauvegarde des commodités:', error);
        throw error;
    }
}

// ==================== GESTION DES PLANS DE SIÈGES ====================

// Charger les plans de sièges
async function loadSeatPlans() {
    try {
        const response = await apiRequest('operator/seat-plans');
        seatPlansData = response.seatPlans || [];
        window.seatPlansData = seatPlansData; // Mettre à jour la référence globale
        displaySeatPlans(seatPlansData);
        populateSeatPlanSelects();
    } catch (error) {
        console.error('Erreur lors du chargement des plans de sièges:', error);
        showAlert('Erreur lors du chargement des plans de sièges', 'danger');

        // Fallback vers des données mockées en cas d'erreur
        seatPlansData = [
            {
                seat_plan_id: 1,
                plan_config: "2x2",
                layout_details: JSON.stringify({rows: 10, columns: 4})
            },
            {
                seat_plan_id: 2,
                plan_config: "2x3",
                layout_details: JSON.stringify({rows: 12, columns: 5})
            }
        ];
        displaySeatPlans(seatPlansData);
        populateSeatPlanSelects();
    }
}

// Afficher les plans de sièges
function displaySeatPlans(seatPlans) {
    const container = document.getElementById('seatPlansGridContainer');

    if (!seatPlans || seatPlans.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-th fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun plan de sièges trouvé</p>
            </div>
        `;
        return;
    }

    let html = '<div class="row">';

    seatPlans.forEach(plan => {
        const layoutDetails = plan.layout_details ? JSON.parse(plan.layout_details) : {};
        const rows = layoutDetails.rows || 0;
        const columns = layoutDetails.columns || 0;

        html += `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-th me-2"></i>
                            Plan ${plan.plan_config}
                        </h5>
                        <p class="card-text">
                            <strong>Configuration:</strong> ${plan.plan_config}<br>
                            <strong>Dimensions:</strong> ${rows} rangées × ${columns} colonnes<br>
                            <strong>Capacité estimée:</strong> ${rows * columns} places
                        </p>
                        <div class="seat-plan-preview mb-3">
                            ${generateSeatPlanPreview(layoutDetails)}
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="editSeatPlan(${plan.seat_plan_id})">
                            <i class="fas fa-edit"></i> Modifier
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteSeatPlan(${plan.seat_plan_id})">
                            <i class="fas fa-trash"></i> Supprimer
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

// Générer un aperçu du plan de sièges
function generateSeatPlanPreview(layoutDetails) {
    const rows = layoutDetails.rows || 0;
    const columns = layoutDetails.columns || 0;

    if (rows === 0 || columns === 0) {
        return '<small class="text-muted">Aperçu non disponible</small>';
    }

    let preview = '<div class="seat-preview-grid" style="display: grid; grid-template-columns: repeat(' + columns + ', 1fr); gap: 2px; max-width: 150px;">';

    for (let i = 0; i < rows * columns; i++) {
        preview += '<div class="seat-preview-cell" style="width: 12px; height: 12px; background: #e9ecef; border-radius: 2px;"></div>';
    }

    preview += '</div>';
    return preview;
}

// Afficher le modal de création de plan de sièges
function showCreateSeatPlanModal() {
    document.getElementById('seatPlanModalTitle').textContent = 'Nouveau Plan de Sièges';
    document.getElementById('seatPlanForm').reset();
    document.getElementById('seatPlanId').value = '';

    const modal = new bootstrap.Modal(document.getElementById('seatPlanModal'));
    modal.show();
}

// Modifier un plan de sièges
function editSeatPlan(seatPlanId) {
    const seatPlan = seatPlansData.find(sp => sp.seat_plan_id == seatPlanId);
    if (!seatPlan) {
        showAlert('Plan de sièges non trouvé', 'danger');
        return;
    }

    const layoutDetails = seatPlan.layout_details ? JSON.parse(seatPlan.layout_details) : {};

    document.getElementById('seatPlanModalTitle').textContent = 'Modifier le Plan de Sièges';
    document.getElementById('seatPlanId').value = seatPlan.seat_plan_id;
    document.getElementById('seatPlanConfig').value = seatPlan.plan_config;
    document.getElementById('seatPlanRows').value = layoutDetails.rows || '';
    document.getElementById('seatPlanColumns').value = layoutDetails.columns || '';

    const modal = new bootstrap.Modal(document.getElementById('seatPlanModal'));
    modal.show();
}

// Sauvegarder un plan de sièges
async function saveSeatPlan() {
    const form = document.getElementById('seatPlanForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const seatPlanId = document.getElementById('seatPlanId').value;
    const isEdit = !!seatPlanId;

    const rows = parseInt(document.getElementById('seatPlanRows').value);
    const columns = parseInt(document.getElementById('seatPlanColumns').value);

    const seatPlanData = {
        plan_config: document.getElementById('seatPlanConfig').value,
        layout_details: JSON.stringify({
            rows: rows,
            columns: columns
        })
    };

    try {
        if (isEdit) {
            await apiRequest(`operator/seat-plans/${seatPlanId}`, {
                method: 'PUT',
                body: JSON.stringify(seatPlanData)
            });
        } else {
            await apiRequest('operator/seat-plans', {
                method: 'POST',
                body: JSON.stringify(seatPlanData)
            });
        }

        showAlert(isEdit ? 'Plan de sièges modifié avec succès' : 'Plan de sièges créé avec succès', 'success');

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('seatPlanModal'));
        modal.hide();

        // Recharger les données
        await loadSeatPlans();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        showAlert(error.message || 'Erreur lors de la sauvegarde', 'danger');
    }
}

// Supprimer un plan de sièges
async function deleteSeatPlan(seatPlanId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce plan de sièges ? Cette action est irréversible.')) {
        return;
    }

    try {
        await apiRequest(`operator/seat-plans/${seatPlanId}`, {
            method: 'DELETE'
        });

        showAlert('Plan de sièges supprimé avec succès', 'success');
        await loadSeatPlans();

    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        showAlert(error.message || 'Erreur lors de la suppression', 'danger');
    }
}

// Peupler les selects de plans de sièges
function populateSeatPlanSelects() {
    const selects = ['busSeatPlanId'];

    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // Garder la première option
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            seatPlansData.forEach(seatPlan => {
                const option = document.createElement('option');
                option.value = seatPlan.seat_plan_id;
                option.textContent = `${seatPlan.plan_config}`;
                select.appendChild(option);
            });
        }
    });
}

// ==================== GESTION DES SIÈGES ====================

// Charger les sièges
async function loadSeats() {
    try {
        const response = await apiRequest('operator/seats');
        seatsData = response.seats || [];
        displaySeats(seatsData);
    } catch (error) {
        console.error('Erreur lors du chargement des sièges:', error);
        showAlert('Erreur lors du chargement des sièges', 'danger');

        // Fallback vers des données mockées en cas d'erreur
        seatsData = [
            {
                seat_id: 1,
                bus_id: 1,
                bus_registration: "AB-123-CD",
                seat_number: "A1",
                seat_type: "standard",
                status: "active"
            },
            {
                seat_id: 2,
                bus_id: 1,
                bus_registration: "AB-123-CD",
                seat_number: "A2",
                seat_type: "standard",
                status: "active"
            },
            {
                seat_id: 3,
                bus_id: 2,
                bus_registration: "EF-456-GH",
                seat_number: "B1",
                seat_type: "premium",
                status: "active"
            }
        ];
        displaySeats(seatsData);
    }
}

// Afficher les sièges
function displaySeats(seats) {
    const container = document.getElementById('seatsTableContainer');

    if (!seats || seats.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-chair fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun siège trouvé</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Bus</th>
                        <th>Numéro de siège</th>
                        <th>Type</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    seats.forEach(seat => {
        const statusClass = seat.status === 'active' ? 'success' : 'secondary';
        const statusText = seat.status === 'active' ? 'Actif' : 'Inactif';
        const typeClass = seat.seat_type === 'premium' ? 'warning' : 'info';
        const typeText = seat.seat_type === 'premium' ? 'Premium' : 'Standard';

        html += `
            <tr>
                <td>#${seat.seat_id}</td>
                <td>${seat.bus_registration || `Bus #${seat.bus_id}`}</td>
                <td><strong>${seat.seat_number}</strong></td>
                <td><span class="badge bg-${typeClass}">${typeText}</span></td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editSeat(${seat.seat_id})" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteSeat(${seat.seat_id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Filtrer les sièges
function filterSeats() {
    const busFilter = document.getElementById('seatBusFilter').value;
    const statusFilter = document.getElementById('seatStatusFilter').value;
    const typeFilter = document.getElementById('seatTypeFilter').value;

    let filteredSeats = seatsData.filter(seat => {
        const matchesBus = !busFilter || seat.bus_id == busFilter;
        const matchesStatus = !statusFilter || seat.status === statusFilter;
        const matchesType = !typeFilter || seat.seat_type === typeFilter;

        return matchesBus && matchesStatus && matchesType;
    });

    displaySeats(filteredSeats);
}

// Effacer les filtres de sièges
function clearSeatFilters() {
    document.getElementById('seatBusFilter').value = '';
    document.getElementById('seatStatusFilter').value = '';
    document.getElementById('seatTypeFilter').value = '';
    displaySeats(seatsData);
}

// Gérer les sièges d'un bus (interface grille)
async function manageBusSeats(busId) {
    try {
        // Charger les informations du bus et son plan de sièges
        const busResponse = await apiRequest(`operator/buses/${busId}`);
        const bus = busResponse.bus;

        if (!bus.seat_plan_id) {
            showAlert('Ce bus n\'a pas de plan de sièges défini', 'warning');
            return;
        }

        // Charger le plan de sièges
        const seatPlanResponse = await apiRequest(`operator/seat-plans/${bus.seat_plan_id}`);
        const seatPlan = seatPlanResponse.seatPlan;

        // Charger les sièges existants pour ce bus
        const seatsResponse = await apiRequest(`operator/buses/${busId}/seats`);
        const existingSeats = seatsResponse.seats || [];

        // Afficher l'interface de gestion des sièges
        showSeatManagementModal(bus, seatPlan, existingSeats);

    } catch (error) {
        console.error('Erreur lors du chargement des données du bus:', error);
        showAlert('Erreur lors du chargement des données du bus', 'danger');
    }
}

// Afficher le modal de gestion des sièges avec grille
function showSeatManagementModal(bus, seatPlan, existingSeats) {
    const layoutDetails = JSON.parse(seatPlan.layout_details);
    const rows = layoutDetails.rows;
    const columns = layoutDetails.columns;

    // Créer un map des sièges existants
    const seatMap = {};
    existingSeats.forEach(seat => {
        seatMap[seat.seat_number] = seat;
    });

    document.getElementById('seatManagementModalTitle').textContent = `Gestion des sièges - ${bus.registration_number}`;
    document.getElementById('seatManagementBusId').value = bus.bus_id;

    // Générer la grille de sièges
    const gridContainer = document.getElementById('seatGrid');
    gridContainer.style.display = 'grid';
    gridContainer.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
    gridContainer.style.gap = '5px';
    gridContainer.style.maxWidth = '400px';
    gridContainer.innerHTML = '';

    const selectedSeats = [];

    // Générer les cellules de la grille
    for (let row = 1; row <= rows; row++) {
        for (let col = 1; col <= columns; col++) {
            const seatNumber = String.fromCharCode(64 + col) + row; // A1, B1, C1, etc.
            const existingSeat = seatMap[seatNumber];

            const cell = document.createElement('div');
            cell.className = 'seat-cell';
            cell.style.cssText = `
                width: 40px;
                height: 40px;
                border: 2px solid #ddd;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-size: 12px;
                font-weight: bold;
                transition: all 0.2s;
            `;

            if (existingSeat) {
                // Siège existant
                cell.style.backgroundColor = existingSeat.status === 'active' ? '#28a745' : '#6c757d';
                cell.style.color = 'white';
                cell.style.borderColor = existingSeat.status === 'active' ? '#28a745' : '#6c757d';
                cell.textContent = seatNumber;
                cell.title = `Siège ${seatNumber} (${existingSeat.seat_type}) - ${existingSeat.status}`;
            } else {
                // Cellule vide - peut être sélectionnée
                cell.style.backgroundColor = '#f8f9fa';
                cell.style.color = '#6c757d';
                cell.textContent = seatNumber;
                cell.title = `Cliquer pour ajouter le siège ${seatNumber}`;

                cell.addEventListener('click', function() {
                    if (cell.classList.contains('selected')) {
                        // Désélectionner
                        cell.classList.remove('selected');
                        cell.style.backgroundColor = '#f8f9fa';
                        cell.style.borderColor = '#ddd';
                        const index = selectedSeats.indexOf(seatNumber);
                        if (index > -1) selectedSeats.splice(index, 1);
                    } else {
                        // Sélectionner
                        cell.classList.add('selected');
                        cell.style.backgroundColor = '#007bff';
                        cell.style.color = 'white';
                        cell.style.borderColor = '#007bff';
                        selectedSeats.push(seatNumber);
                    }
                    updateSelectedSeatsDisplay(selectedSeats);
                });
            }

            gridContainer.appendChild(cell);
        }
    }

    // Afficher les sièges sélectionnés
    updateSelectedSeatsDisplay(selectedSeats);

    const modal = new bootstrap.Modal(document.getElementById('seatManagementModal'));
    modal.show();
}

// Mettre à jour l'affichage des sièges sélectionnés
function updateSelectedSeatsDisplay(selectedSeats) {
    const container = document.getElementById('selectedSeatsDisplay');
    if (selectedSeats.length === 0) {
        container.innerHTML = '<p class="text-muted">Aucun siège sélectionné</p>';
    } else {
        const seatsJson = selectedSeats.map(seatNumber => ({
            bus_id: parseInt(document.getElementById('seatManagementBusId').value),
            seat_number: seatNumber
        }));

        container.innerHTML = `
            <p><strong>Sièges sélectionnés (${selectedSeats.length}):</strong></p>
            <div class="mb-2">${selectedSeats.join(', ')}</div>
            <p><strong>JSON à envoyer à l'API:</strong></p>
            <pre class="bg-light p-2 rounded"><code>${JSON.stringify(seatsJson, null, 2)}</code></pre>
        `;
    }
}

// Sauvegarder les sièges sélectionnés
async function saveSelectedSeats() {
    const busId = document.getElementById('seatManagementBusId').value;
    const seatType = document.getElementById('newSeatType').value;

    // Récupérer les sièges sélectionnés
    const selectedCells = document.querySelectorAll('.seat-cell.selected');
    if (selectedCells.length === 0) {
        showAlert('Veuillez sélectionner au moins un siège', 'warning');
        return;
    }

    const seatsData = Array.from(selectedCells).map(cell => ({
        bus_id: parseInt(busId),
        seat_number: cell.textContent,
        seat_type: seatType
    }));

    try {
        await apiRequest(`operator/buses/${busId}/seats`, {
            method: 'POST',
            body: JSON.stringify({ seats: seatsData })
        });

        showAlert(`${seatsData.length} siège(s) ajouté(s) avec succès`, 'success');

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('seatManagementModal'));
        modal.hide();

        // Recharger les données
        await loadSeats();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde des sièges:', error);
        showAlert(error.message || 'Erreur lors de la sauvegarde des sièges', 'danger');
    }
}

// Modifier un siège
function editSeat(seatId) {
    const seat = seatsData.find(s => s.seat_id == seatId);
    if (!seat) {
        showAlert('Siège non trouvé', 'danger');
        return;
    }

    document.getElementById('seatModalTitle').textContent = 'Modifier le Siège';
    document.getElementById('seatId').value = seat.seat_id;
    document.getElementById('seatBusId').value = seat.bus_id;
    document.getElementById('seatNumber').value = seat.seat_number;
    document.getElementById('seatType').value = seat.seat_type;
    document.getElementById('seatStatus').value = seat.status;

    const modal = new bootstrap.Modal(document.getElementById('seatModal'));
    modal.show();
}

// Sauvegarder un siège
async function saveSeat() {
    const form = document.getElementById('seatForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const seatId = document.getElementById('seatId').value;

    const seatData = {
        seat_type: document.getElementById('seatType').value,
        status: document.getElementById('seatStatus').value
    };

    try {
        await apiRequest(`operator/seats/${seatId}`, {
            method: 'PUT',
            body: JSON.stringify(seatData)
        });

        showAlert('Siège modifié avec succès', 'success');

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('seatModal'));
        modal.hide();

        // Recharger les données
        await loadSeats();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        showAlert(error.message || 'Erreur lors de la sauvegarde', 'danger');
    }
}

// Supprimer un siège
async function deleteSeat(seatId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce siège ? Cette action est irréversible.')) {
        return;
    }

    try {
        await apiRequest(`operator/seats/${seatId}`, {
            method: 'DELETE'
        });

        showAlert('Siège supprimé avec succès', 'success');
        await loadSeats();

    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        showAlert(error.message || 'Erreur lors de la suppression', 'danger');
    }
}

// ==================== GESTION DES VOYAGES ====================

// Charger les voyages
async function loadTrips() {
    try {
        const response = await apiRequest('operator/trips');
        tripsData = response.trips || [];
        displayTrips(tripsData);
    } catch (error) {
        console.error('Erreur lors du chargement des voyages:', error);
        // Utiliser des données mockées en cas d'erreur
        tripsData = [
            {
                trip_id: 1,
                route_id: 1,
                route_name: "Cotonou - Porto-Novo",
                bus_id: 1,
                bus_registration: "AB-123-CD",
                driver_id: 1,
                driver_name: "Jean Dupont",
                controller_id: 2,
                estimated_departure_time: "2024-01-15T08:00:00",
                estimated_arrival_time: "2024-01-15T10:30:00",
                status: "planned",
                departure_location: "Cotonou",
                destination_location: "Porto-Novo"
            },
            {
                trip_id: 2,
                route_id: 1,
                route_name: "Porto-Novo - Cotonou",
                bus_id: 2,
                bus_registration: "EF-456-GH",
                driver_id: 1,
                driver_name: "Jean Dupont",
                controller_id: null,
                estimated_departure_time: "2024-01-15T14:00:00",
                estimated_arrival_time: "2024-01-15T16:30:00",
                status: "ongoing",
                departure_location: "Porto-Novo",
                destination_location: "Cotonou"
            }
        ];
        displayTrips(tripsData);
        showAlert('Données de démonstration chargées pour les voyages', 'info');
    }
}

// Afficher les voyages
function displayTrips(trips) {
    const container = document.getElementById('tripsTableContainer');

    if (!trips || trips.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-route fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun voyage trouvé</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Itinéraire</th>
                        <th>Bus</th>
                        <th>Chauffeur</th>
                        <th>Départ prévu</th>
                        <th>Arrivée prévue</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    trips.forEach(trip => {
        const statusClass = trip.status === 'completed' ? 'success' :
                           trip.status === 'ongoing' ? 'primary' :
                           trip.status === 'cancelled' ? 'danger' :
                           trip.status === 'delayed' ? 'warning' : 'secondary';

        const statusText = trip.status === 'completed' ? 'Terminé' :
                          trip.status === 'ongoing' ? 'En cours' :
                          trip.status === 'cancelled' ? 'Annulé' :
                          trip.status === 'delayed' ? 'Retardé' : 'Planifié';

        html += `
            <tr>
                <td>#${trip.trip_id}</td>
                <td>
                    <strong>${trip.route_name || 'N/A'}</strong><br>
                    <small class="text-muted">${trip.departure_location || 'N/A'} → ${trip.destination_location || 'N/A'}</small>
                </td>
                <td>${trip.bus_registration || `Bus #${trip.bus_id}`}</td>
                <td>${trip.driver_name || `Chauffeur #${trip.driver_id}`}</td>
                <td>${formatDateTime(trip.estimated_departure_time)}</td>
                <td>${formatDateTime(trip.estimated_arrival_time)}</td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editTrip(${trip.trip_id})" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="viewTripDetails(${trip.trip_id})" title="Voir détails">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="manageTripStops(${trip.trip_id})" title="Gérer les arrêts">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteTrip(${trip.trip_id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Afficher le modal de création de voyage
async function showCreateTripModal() {
    try {
        // Afficher un indicateur de chargement
        const modalElement = document.getElementById('tripModal');
        const modalBody = modalElement.querySelector('.modal-body');
        const originalContent = modalBody.innerHTML;

        modalBody.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div><p class="mt-2">Chargement des données...</p></div>';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Charger les données nécessaires
        await Promise.all([
            ensureRoutesLoaded(),
            ensureBusesLoaded(),
            ensureUsersLoaded()
        ]);

        // Restaurer le contenu original
        modalBody.innerHTML = originalContent;

        // Configurer le formulaire
        document.getElementById('tripModalTitle').textContent = 'Nouveau Voyage';
        document.getElementById('tripForm').reset();
        document.getElementById('tripId').value = '';
        document.getElementById('tripStatus').value = 'planned';

        // Peupler les selects avec les données chargées
        populateRouteSelects();
        populateBusSelects();
        populateUserSelects();

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal voyage:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Modifier un voyage
async function editTrip(tripId) {
    const trip = tripsData.find(t => t.trip_id == tripId);
    if (!trip) {
        showAlert('Voyage non trouvé', 'danger');
        return;
    }

    try {
        // Afficher un indicateur de chargement
        const modalElement = document.getElementById('tripModal');
        const modalBody = modalElement.querySelector('.modal-body');
        const originalContent = modalBody.innerHTML;

        modalBody.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div><p class="mt-2">Chargement des données...</p></div>';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Charger les données nécessaires
        await Promise.all([
            ensureRoutesLoaded(),
            ensureBusesLoaded(),
            ensureUsersLoaded()
        ]);

        // Restaurer le contenu original
        modalBody.innerHTML = originalContent;

        // Peupler les selects avec les données chargées
        populateRouteSelects();
        populateBusSelects();
        populateUserSelects();

        // Configurer le formulaire avec les données du voyage
        document.getElementById('tripModalTitle').textContent = 'Modifier le Voyage';
        document.getElementById('tripId').value = trip.trip_id;
        document.getElementById('tripRouteId').value = trip.route_id;
        document.getElementById('tripBusId').value = trip.bus_id;
        document.getElementById('tripDriverId').value = trip.driver_id;
        document.getElementById('tripControllerId').value = trip.controller_id || '';
        document.getElementById('tripEstimatedDepartureTime').value = formatDateTimeForInput(trip.estimated_departure_time);
        document.getElementById('tripEstimatedArrivalTime').value = formatDateTimeForInput(trip.estimated_arrival_time);
        document.getElementById('tripActualDepartureTime').value = formatDateTimeForInput(trip.actual_departure_time);
        document.getElementById('tripActualArrivalTime').value = formatDateTimeForInput(trip.actual_arrival_time);
        document.getElementById('tripTrackingLink').value = trip.tracking_link || '';
        document.getElementById('tripStatus').value = trip.status;
        document.getElementById('tripDelayReason').value = trip.delay_reason || '';
        document.getElementById('tripCancellationReason').value = trip.cancellation_reason || '';

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal voyage:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Sauvegarder un voyage
async function saveTrip() {
    const form = document.getElementById('tripForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const tripId = document.getElementById('tripId').value;
    const isEdit = !!tripId;

    const tripData = {
        route_id: parseInt(document.getElementById('tripRouteId').value),
        bus_id: parseInt(document.getElementById('tripBusId').value),
        driver_id: parseInt(document.getElementById('tripDriverId').value),
        controller_id: document.getElementById('tripControllerId').value || null,
        estimated_departure_time: document.getElementById('tripEstimatedDepartureTime').value,
        estimated_arrival_time: document.getElementById('tripEstimatedArrivalTime').value,
        actual_departure_time: document.getElementById('tripActualDepartureTime').value || null,
        actual_arrival_time: document.getElementById('tripActualArrivalTime').value || null,
        tracking_link: document.getElementById('tripTrackingLink').value || null,
        status: document.getElementById('tripStatus').value,
        delay_reason: document.getElementById('tripDelayReason').value || null,
        cancellation_reason: document.getElementById('tripCancellationReason').value || null
    };

    try {
        if (isEdit) {
            await apiRequest(`operator/trips/${tripId}`, {
                method: 'PUT',
                body: JSON.stringify(tripData)
            });
        } else {
            await apiRequest('operator/trips', {
                method: 'POST',
                body: JSON.stringify(tripData)
            });
        }

        showAlert(isEdit ? 'Voyage modifié avec succès' : 'Voyage créé avec succès', 'success');

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('tripModal'));
        modal.hide();

        // Recharger les données
        await loadTrips();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        showAlert(error.message || 'Erreur lors de la sauvegarde', 'danger');
    }
}

// Supprimer un voyage
async function deleteTrip(tripId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce voyage ? Cette action est irréversible.')) {
        return;
    }

    try {
        await apiRequest(`operator/trips/${tripId}`, {
            method: 'DELETE'
        });

        showAlert('Voyage supprimé avec succès', 'success');
        await loadTrips();

    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        showAlert(error.message || 'Erreur lors de la suppression', 'danger');
    }
}

// Voir les détails d'un voyage
async function viewTripDetails(tripId) {
    const trip = tripsData.find(t => t.trip_id == tripId);
    if (!trip) {
        showAlert('Voyage non trouvé', 'danger');
        return;
    }

    try {
        // Afficher le modal avec un indicateur de chargement
        const modal = new bootstrap.Modal(document.getElementById('tripDetailsModal'));
        document.getElementById('tripDetailsModalTitle').textContent = `Détails du Voyage #${tripId}`;
        document.getElementById('tripDetailsContent').innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                <p class="mt-2 text-muted">Chargement des détails...</p>
            </div>
        `;
        modal.show();

        // Charger les détails complets du voyage
        const [tripDetails, tripStops, bookings] = await Promise.all([
            loadTripFullDetails(tripId),
            loadTripStops(tripId),
            loadTripBookings(tripId)
        ]);

        // Afficher les détails
        document.getElementById('tripDetailsContent').innerHTML = renderTripDetails(tripDetails, tripStops, bookings);

    } catch (error) {
        console.error('Erreur lors du chargement des détails:', error);
        document.getElementById('tripDetailsContent').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Erreur lors du chargement des détails du voyage
            </div>
        `;
    }
}

// Filtrer les voyages
function filterTrips() {
    const statusFilter = document.getElementById('tripStatusFilter').value;
    const routeFilter = document.getElementById('tripRouteFilter').value;
    const dateFromFilter = document.getElementById('tripDateFromFilter').value;
    const dateToFilter = document.getElementById('tripDateToFilter').value;

    let filteredTrips = tripsData.filter(trip => {
        const matchesStatus = !statusFilter || trip.status === statusFilter;
        const matchesRoute = !routeFilter || trip.route_id == routeFilter;

        let matchesDateRange = true;
        if (dateFromFilter || dateToFilter) {
            const tripDate = new Date(trip.estimated_departure_time);
            if (dateFromFilter) {
                matchesDateRange = matchesDateRange && tripDate >= new Date(dateFromFilter);
            }
            if (dateToFilter) {
                matchesDateRange = matchesDateRange && tripDate <= new Date(dateToFilter);
            }
        }

        return matchesStatus && matchesRoute && matchesDateRange;
    });

    displayTrips(filteredTrips);
}

// Effacer les filtres de voyages
function clearTripFilters() {
    document.getElementById('tripStatusFilter').value = '';
    document.getElementById('tripRouteFilter').value = '';
    document.getElementById('tripDateFromFilter').value = '';
    document.getElementById('tripDateToFilter').value = '';
    displayTrips(tripsData);
}

// ==================== GESTION DES ARRÊTS DE VOYAGE ====================

// Gérer les arrêts d'un voyage
async function manageTripStops(tripId) {
    const trip = tripsData.find(t => t.trip_id == tripId);
    if (!trip) {
        showAlert('Voyage non trouvé', 'danger');
        return;
    }

    try {
        // Afficher le modal avec un indicateur de chargement
        const modal = new bootstrap.Modal(document.getElementById('tripStopsModal'));
        document.getElementById('tripStopsModalTitle').textContent = `Gestion des Arrêts - Voyage #${tripId}`;
        document.getElementById('tripStopsTripId').value = tripId;

        // Réinitialiser le formulaire
        document.getElementById('tripStopForm').reset();

        modal.show();

        // Charger les données nécessaires
        await Promise.all([
            ensureStopsLoaded(),
            loadTripStopsForManagement(tripId)
        ]);

        // Peupler le select des arrêts
        populateTripStopSelects();

    } catch (error) {
        console.error('Erreur lors de l\'ouverture du modal arrêts de voyage:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
    }
}

// Charger les arrêts d'un voyage pour la gestion
async function loadTripStopsForManagement(tripId) {
    try {
        const response = await apiRequest(`operator/trips/${tripId}/stops`);
        const tripStops = response.stops || [];
        displayTripStops(tripStops);
        return tripStops;
    } catch (error) {
        console.error('Erreur lors du chargement des arrêts du voyage:', error);
        document.getElementById('tripStopsContainer').innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Erreur lors du chargement des arrêts
            </div>
        `;
        return [];
    }
}

// Afficher les arrêts d'un voyage
function displayTripStops(tripStops) {
    const container = document.getElementById('tripStopsContainer');

    if (!tripStops || tripStops.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-map-marker-alt fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun arrêt configuré pour ce voyage</p>
            </div>
        `;
        return;
    }

    // Trier par heure d'arrivée
    tripStops.sort((a, b) => new Date(a.arrival_time) - new Date(b.arrival_time));

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Arrêt</th>
                        <th>Heure d'arrivée prévue</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    tripStops.forEach(stop => {
        html += `
            <tr>
                <td>
                    <strong>${stop.stop_name}</strong>
                    ${stop.address ? `<br><small class="text-muted">${stop.address}</small>` : ''}
                </td>
                <td>${formatDateTime(stop.arrival_time)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editTripStop(${stop.trip_stop_id})" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteTripStop(${stop.trip_stop_id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Peupler les selects d'arrêts pour les voyages
function populateTripStopSelects() {
    const select = document.getElementById('tripStopStopId');
    if (select && stopsData) {
        // Garder la première option
        const firstOption = select.querySelector('option');
        select.innerHTML = '';
        if (firstOption) {
            select.appendChild(firstOption);
        }

        stopsData.forEach(stop => {
            const option = document.createElement('option');
            option.value = stop.stop_id;
            option.textContent = stop.stop_name;
            select.appendChild(option);
        });
    }
}

// Ajouter un arrêt à un voyage
async function addTripStop() {
    const form = document.getElementById('tripStopForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const tripId = document.getElementById('tripStopsTripId').value;
    const tripStopData = {
        trip_id: parseInt(tripId),
        stop_id: parseInt(document.getElementById('tripStopStopId').value),
        arrival_time: document.getElementById('tripStopArrivalTime').value
    };

    try {
        await apiRequest('operator/trip-stops', {
            method: 'POST',
            body: JSON.stringify(tripStopData)
        });

        showAlert('Arrêt ajouté avec succès', 'success');

        // Recharger les arrêts et réinitialiser le formulaire
        await loadTripStopsForManagement(tripId);
        form.reset();

    } catch (error) {
        console.error('Erreur lors de l\'ajout de l\'arrêt:', error);
        showAlert(error.message || 'Erreur lors de l\'ajout de l\'arrêt', 'danger');
    }
}

// Modifier un arrêt de voyage
async function editTripStop(tripStopId) {
    // Pour simplifier, on va demander la nouvelle heure via un prompt
    const newArrivalTime = prompt('Nouvelle heure d\'arrivée (format: YYYY-MM-DD HH:MM):');

    if (newArrivalTime) {
        try {
            await apiRequest(`operator/trip-stops/${tripStopId}`, {
                method: 'PUT',
                body: JSON.stringify({
                    arrival_time: newArrivalTime
                })
            });

            showAlert('Arrêt modifié avec succès', 'success');

            // Recharger les arrêts
            const tripId = document.getElementById('tripStopsTripId').value;
            await loadTripStopsForManagement(tripId);

        } catch (error) {
            console.error('Erreur lors de la modification de l\'arrêt:', error);
            showAlert(error.message || 'Erreur lors de la modification de l\'arrêt', 'danger');
        }
    }
}

// Supprimer un arrêt de voyage
async function deleteTripStop(tripStopId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet arrêt du voyage ?')) {
        return;
    }

    try {
        await apiRequest(`operator/trip-stops/${tripStopId}`, {
            method: 'DELETE'
        });

        showAlert('Arrêt supprimé avec succès', 'success');

        // Recharger les arrêts
        const tripId = document.getElementById('tripStopsTripId').value;
        await loadTripStopsForManagement(tripId);

    } catch (error) {
        console.error('Erreur lors de la suppression de l\'arrêt:', error);
        showAlert(error.message || 'Erreur lors de la suppression de l\'arrêt', 'danger');
    }
}

// ==================== FONCTIONS POUR LES DÉTAILS DE VOYAGE ====================

// Charger les détails complets d'un voyage
async function loadTripFullDetails(tripId) {
    try {
        const response = await apiRequest(`operator/trips/${tripId}`);
        return response.trip || null;
    } catch (error) {
        console.error('Erreur lors du chargement des détails du voyage:', error);
        // Retourner les données locales si l'API échoue
        return tripsData.find(t => t.trip_id == tripId) || null;
    }
}

// Charger les arrêts d'un voyage
async function loadTripStops(tripId) {
    try {
        const response = await apiRequest(`operator/trips/${tripId}/stops`);
        return response.stops || [];
    } catch (error) {
        console.error('Erreur lors du chargement des arrêts du voyage:', error);
        return [];
    }
}

// Charger les réservations d'un voyage
async function loadTripBookings(tripId) {
    try {
        const response = await apiRequest(`operator/trips/${tripId}/bookings`);
        return response.bookings || [];
    } catch (error) {
        console.error('Erreur lors du chargement des réservations du voyage:', error);
        return [];
    }
}

// Rendre les détails du voyage
function renderTripDetails(trip, stops, bookings) {
    if (!trip) {
        return '<div class="alert alert-warning">Aucun détail disponible pour ce voyage</div>';
    }

    const statusClass = getStatusClass(trip.status);
    const statusText = getStatusText(trip.status);

    let html = `
        <div class="row">
            <!-- Informations générales -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations générales</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>ID Voyage:</strong></td>
                                <td>#${trip.trip_id}</td>
                            </tr>
                            <tr>
                                <td><strong>Itinéraire:</strong></td>
                                <td>${trip.route_name || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Départ:</strong></td>
                                <td>${trip.departure_location || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Destination:</strong></td>
                                <td>${trip.destination_location || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td><strong>Statut:</strong></td>
                                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Informations du bus et équipage -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-bus me-2"></i>Bus et équipage</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Bus:</strong></td>
                                <td>${trip.bus_registration || `Bus #${trip.bus_id}`}</td>
                            </tr>
                            <tr>
                                <td><strong>Marque/Modèle:</strong></td>
                                <td>${trip.bus_brand || 'N/A'} ${trip.bus_model || ''}</td>
                            </tr>
                            <tr>
                                <td><strong>Capacité:</strong></td>
                                <td>${trip.bus_capacity || 'N/A'} places</td>
                            </tr>
                            <tr>
                                <td><strong>Chauffeur:</strong></td>
                                <td>${trip.driver_name || `Chauffeur #${trip.driver_id}`}</td>
                            </tr>
                            <tr>
                                <td><strong>Contrôleur:</strong></td>
                                <td>${trip.controller_name || (trip.controller_id ? `Contrôleur #${trip.controller_id}` : 'Aucun')}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Horaires -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Horaires</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Départ prévu:</strong></td>
                                <td>${formatDateTime(trip.estimated_departure_time)}</td>
                            </tr>
                            <tr>
                                <td><strong>Arrivée prévue:</strong></td>
                                <td>${formatDateTime(trip.estimated_arrival_time)}</td>
                            </tr>
                            <tr>
                                <td><strong>Départ réel:</strong></td>
                                <td>${formatDateTime(trip.actual_departure_time) || '<span class="text-muted">Non défini</span>'}</td>
                            </tr>
                            <tr>
                                <td><strong>Arrivée réelle:</strong></td>
                                <td>${formatDateTime(trip.actual_arrival_time) || '<span class="text-muted">Non défini</span>'}</td>
                            </tr>
                            <tr>
                                <td><strong>Lien de suivi:</strong></td>
                                <td>${trip.tracking_link ? `<a href="${trip.tracking_link}" target="_blank" class="btn btn-sm btn-outline-primary"><i class="fas fa-external-link-alt me-1"></i>Suivre</a>` : '<span class="text-muted">Non disponible</span>'}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Statistiques -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Statistiques</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Réservations:</strong></td>
                                <td>${bookings.length} réservation(s)</td>
                            </tr>
                            <tr>
                                <td><strong>Arrêts prévus:</strong></td>
                                <td>${stops.length} arrêt(s)</td>
                            </tr>
                            <tr>
                                <td><strong>Taux d'occupation:</strong></td>
                                <td>${trip.bus_capacity ? Math.round((bookings.length / trip.bus_capacity) * 100) : 0}%</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Ajouter les arrêts si disponibles
    if (stops.length > 0) {
        html += `
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Arrêts du voyage</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Arrêt</th>
                                            <th>Heure prévue</th>
                                            <th>Statut</th>
                                        </tr>
                                    </thead>
                                    <tbody>
        `;

        stops.forEach(stop => {
            html += `
                <tr>
                    <td>${stop.stop_name || 'N/A'}</td>
                    <td>${formatDateTime(stop.arrival_time)}</td>
                    <td><span class="badge bg-info">Prévu</span></td>
                </tr>
            `;
        });

        html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Ajouter les réservations si disponibles
    if (bookings.length > 0) {
        html += `
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-ticket-alt me-2"></i>Réservations (${bookings.length})</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Passager</th>
                                            <th>Embarquement</th>
                                            <th>Débarquement</th>
                                            <th>Montant</th>
                                            <th>Statut</th>
                                        </tr>
                                    </thead>
                                    <tbody>
        `;

        bookings.forEach(booking => {
            const statusClass = booking.booking_status === 'confirmed' ? 'success' :
                               booking.booking_status === 'cancelled' ? 'danger' : 'warning';
            html += `
                <tr>
                    <td>#${booking.booking_id}</td>
                    <td>${booking.user_name || 'N/A'}</td>
                    <td>${booking.boarding_stop_name || 'N/A'}</td>
                    <td>${booking.dropping_stop_name || 'N/A'}</td>
                    <td>${formatCurrency(booking.total_amount)}</td>
                    <td><span class="badge bg-${statusClass}">${getStatusText(booking.booking_status)}</span></td>
                </tr>
            `;
        });

        html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    return html;
}

// ==================== FONCTIONS UTILITAIRES SUPPLÉMENTAIRES ====================

// Formater une date pour les inputs datetime-local
function formatDateTimeForInput(dateTimeString) {
    if (!dateTimeString) return '';
    const date = new Date(dateTimeString);

    // Calcule le décalage entre l'heure locale et UTC en minutes
    const timezoneOffset = date.getTimezoneOffset(); // en minutes

    // Ajuste la date en ajoutant le décalage (converti en ms)
    const localDate = new Date(date.getTime() - timezoneOffset * 60000);

    return localDate.toISOString().slice(0, 16);
}

// Formater une date simple
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
}

// Formater une date et heure
function formatDateTime(dateTimeString) {
    if (!dateTimeString) return 'N/A';
    const date = new Date(dateTimeString);
    return date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
}

// Formater une heure
function formatTime(timeString) {
    if (!timeString) return 'N/A';
    const date = new Date(timeString);
    return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
}

// Formater une devise
function formatCurrency(amount) {
    if (amount === null || amount === undefined) return '0 FCFA';
    return new Intl.NumberFormat('fr-FR').format(amount) + ' FCFA';
}

// Obtenir le texte du statut
function getStatusText(status) {
    const statusTexts = {
        'planned': 'Planifié',
        'ongoing': 'En cours',
        'completed': 'Terminé',
        'delayed': 'Retardé',
        'cancelled': 'Annulé',
        'active': 'Actif',
        'inactive': 'Inactif',
        'pending': 'En attente',
        'confirmed': 'Confirmé',
        'successful': 'Réussi',
        'failed': 'Échoué',
        'refunded': 'Remboursé'
    };
    return statusTexts[status] || status;
}

// Obtenir la classe CSS du statut
function getStatusClass(status) {
    const statusClasses = {
        'planned': 'primary',
        'ongoing': 'warning',
        'completed': 'success',
        'delayed': 'warning',
        'cancelled': 'danger',
        'active': 'success',
        'inactive': 'secondary',
        'pending': 'warning',
        'confirmed': 'success',
        'successful': 'success',
        'failed': 'danger',
        'refunded': 'info'
    };
    return statusClasses[status] || 'secondary';
}

// Charger les réservations (fonction existante à compléter)
async function loadBookings() {
    try {
        const response = await apiRequest('operator/bookings');
        // Traitement des réservations
        console.log('Réservations chargées:', response);
    } catch (error) {
        console.error('Erreur lors du chargement des réservations:', error);
        showAlert('Erreur lors du chargement des réservations', 'danger');
    }
}

// Peupler les selects d'utilisateurs (chauffeurs, contrôleurs)
function populateUserSelects() {
    // Cette fonction sera appelée après le chargement des utilisateurs
    const driverSelects = ['tripDriverId'];
    const controllerSelects = ['tripControllerId'];

    driverSelects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            usersData.forEach(user => {
                if (user.roles && user.roles.includes('driver') && user.status === 'active') {
                    const option = document.createElement('option');
                    option.value = user.user_id;
                    option.textContent = `${user.first_name} ${user.last_name}`;
                    select.appendChild(option);
                }
            });
        }
    });

    controllerSelects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            const firstOption = select.querySelector('option');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            usersData.forEach(user => {
                if (user.roles && user.roles.includes('controller') && user.status === 'active') {
                    const option = document.createElement('option');
                    option.value = user.user_id;
                    option.textContent = `${user.first_name} ${user.last_name}`;
                    select.appendChild(option);
                }
            });
        }
    });
}

// --- Fonctions d'initialisation conditionnelle des données ---
async function ensureLocationsLoaded() {
    if (!locationsData || locationsData.length === 0) {
        await loadLocations();
    }
}

async function ensureRoutesLoaded() {
    if (!routesData || routesData.length === 0) {
        await loadRoutes();
    }
}

async function ensureBusesLoaded() {
    if (!busesData || busesData.length === 0) {
        await loadBuses();
    }
}

async function ensureUsersLoaded() {
    if (!usersData || usersData.length === 0) {
        await loadUsers();
    }
}

async function ensureSeatPlansLoaded() {
    if (!seatPlansData || seatPlansData.length === 0) {
        await loadSeatPlans();
    }
}

// Mettre à jour tous les selects après chargement des données
function updateAllSelects() {
    // Mettre à jour tous les selects avec les données actuelles
    if (locationsData && locationsData.length > 0) {
        populateLocationSelects();
    }
    if (routesData && routesData.length > 0) {
        populateRouteSelects();
    }
    if (busesData && busesData.length > 0) {
        populateBusSelects();
    }
    if (seatPlansData && seatPlansData.length > 0) {
        populateSeatPlanSelects();
    }
    if (usersData && usersData.length > 0) {
        populateUserSelects();
    }
}

// Initialiser les données de base pour tous les formulaires
async function initializeFormData() {
    try {
        await Promise.all([
            loadLocations(),
            loadRoutes(),
            loadBuses(),
            loadSeatPlans(),
            loadUsers()
        ]);
        updateAllSelects();
    } catch (error) {
        console.error('Erreur lors de l\'initialisation des données:', error);
    }
}
