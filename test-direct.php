<?php
// Test direct de l'API sans passer par le frontend

echo "<h1>Test Direct API</h1>";

// Fonction pour faire des requêtes
function makeRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data && $method !== 'GET') {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen(json_encode($data))
        ]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

// Tests
$baseUrl = 'http://localhost/bus-booking/api/v1';

$tests = [
    'Test endpoint' => $baseUrl . '/test',
    'Health check' => $baseUrl . '/health',
    'Locations' => $baseUrl . '/locations',
    'Routes' => $baseUrl . '/routes',
    'Trips' => $baseUrl . '/trips',
    'Amenities' => $baseUrl . '/amenities'
];

echo "<h2>Résultats des tests</h2>";

foreach ($tests as $name => $url) {
    echo "<h3>$name</h3>";
    echo "URL: <code>$url</code><br>";
    
    $result = makeRequest($url);
    
    $statusColor = $result['http_code'] == 200 ? 'green' : 'red';
    echo "Status: <span style='color: $statusColor'>{$result['http_code']}</span><br>";
    
    if ($result['error']) {
        echo "Erreur cURL: <span style='color: red'>{$result['error']}</span><br>";
    }
    
    if ($result['response']) {
        $decoded = json_decode($result['response'], true);
        if ($decoded) {
            echo "Réponse:<br>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
            echo htmlspecialchars(json_encode($decoded, JSON_PRETTY_PRINT));
            echo "</pre>";
        } else {
            echo "Réponse brute:<br>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
            echo htmlspecialchars($result['response']);
            echo "</pre>";
        }
    }
    
    echo "<hr>";
}

// Test de la réécriture d'URL
echo "<h2>Test de réécriture d'URL</h2>";
echo "Tentative d'accès direct à api/index.php:<br>";
$directResult = makeRequest('http://localhost/bus-booking/api/index.php');
echo "Status: <span style='color: " . ($directResult['http_code'] == 200 ? 'green' : 'red') . "'>{$directResult['http_code']}</span><br>";

if ($directResult['response']) {
    echo "Réponse:<br>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 100px; overflow-y: auto;'>";
    echo htmlspecialchars($directResult['response']);
    echo "</pre>";
}

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
</style>
