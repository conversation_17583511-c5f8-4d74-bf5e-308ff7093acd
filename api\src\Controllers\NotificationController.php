<?php
require_once __DIR__ . '/../Services/EmailService.php';
require_once __DIR__ . '/../Helpers/response.php';
require_once __DIR__ . '/../Middlewares/AuthMiddleware.php';

class NotificationController {
    private $emailService;
    
    public function __construct() {
        $this->emailService = new EmailService();
    }
    
    /**
     * Envoyer un email
     * POST /v1/notifications/email
     */
    public function sendEmail($params) {
        try {
            // Authentification requise
            $user = AuthMiddleware::authenticate();
            
            $to = $params['to'] ?? '';
            $subject = $params['subject'] ?? '';
            $template = $params['template'] ?? '';
            $data = $params['data'] ?? [];
            
            if (empty($to) || empty($subject) || empty($template)) {
                sendResponse(400, ['message' => 'Paramètres manquants (to, subject, template)']);
                return;
            }
            
            $success = $this->emailService->sendEmail($to, $subject, $template, $data);
            
            if ($success) {
                sendResponse(200, ['message' => 'Email envoyé avec succès']);
            } else {
                sendResponse(500, ['message' => 'Erreur lors de l\'envoi de l\'email']);
            }
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * Obtenir la liste des templates d'email
     * GET /v1/notifications/templates
     */
    public function getTemplates($params) {
        try {
            $templates = [
                'booking_confirmation' => [
                    'name' => 'Confirmation de réservation',
                    'description' => 'Email envoyé après une réservation réussie',
                    'variables' => ['passenger_name', 'booking_id', 'trip_details', 'tickets']
                ],
                'booking_cancellation' => [
                    'name' => 'Annulation de réservation',
                    'description' => 'Email envoyé lors de l\'annulation d\'une réservation',
                    'variables' => ['passenger_name', 'booking_id', 'refund_info']
                ],
                'payment_confirmation' => [
                    'name' => 'Confirmation de paiement',
                    'description' => 'Email envoyé après un paiement réussi',
                    'variables' => ['passenger_name', 'amount', 'payment_reference', 'booking_details']
                ],
                'trip_reminder' => [
                    'name' => 'Rappel de voyage',
                    'description' => 'Email de rappel envoyé avant le départ',
                    'variables' => ['passenger_name', 'departure_time', 'boarding_location', 'trip_details']
                ],
                'trip_delay' => [
                    'name' => 'Retard de voyage',
                    'description' => 'Email envoyé en cas de retard',
                    'variables' => ['passenger_name', 'new_departure_time', 'delay_reason', 'trip_details']
                ]
            ];
            
            sendResponse(200, ['templates' => $templates]);
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * Envoyer une confirmation de réservation
     */
    public function sendBookingConfirmation($bookingData, $tickets) {
        try {
            $emailData = [
                'passenger_name' => $bookingData['passenger_name'],
                'booking_id' => $bookingData['booking_id'],
                'trip_details' => $bookingData['trip_details'],
                'tickets' => $tickets,
                'total_amount' => $bookingData['total_amount'],
                'booking_date' => date('d/m/Y H:i')
            ];
            
            return $this->emailService->sendEmail(
                $bookingData['passenger_email'],
                'Confirmation de votre réservation EasyBus',
                'booking_confirmation',
                $emailData
            );
            
        } catch (Exception $e) {
            error_log('Erreur envoi email confirmation: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Envoyer une confirmation de paiement
     */
    public function sendPaymentConfirmation($paymentData) {
        try {
            $emailData = [
                'passenger_name' => $paymentData['passenger_name'],
                'amount' => $paymentData['amount'],
                'currency' => $paymentData['currency'],
                'payment_reference' => $paymentData['payment_reference'],
                'booking_details' => $paymentData['booking_details'],
                'payment_date' => date('d/m/Y H:i')
            ];
            
            return $this->emailService->sendEmail(
                $paymentData['passenger_email'],
                'Confirmation de paiement EasyBus',
                'payment_confirmation',
                $emailData
            );
            
        } catch (Exception $e) {
            error_log('Erreur envoi email paiement: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Envoyer un rappel de voyage
     */
    public function sendTripReminder($tripData, $passengers) {
        try {
            foreach ($passengers as $passenger) {
                $emailData = [
                    'passenger_name' => $passenger['passenger_name'],
                    'departure_time' => $tripData['estimated_departure_time'],
                    'boarding_location' => $tripData['boarding_location'],
                    'trip_details' => $tripData,
                    'tickets' => $passenger['tickets']
                ];
                
                $this->emailService->sendEmail(
                    $passenger['passenger_email'],
                    'Rappel: Votre voyage EasyBus demain',
                    'trip_reminder',
                    $emailData
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log('Erreur envoi rappel voyage: ' . $e->getMessage());
            return false;
        }
    }
}
