<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord Opérateur - EasyBus</title>
    <link rel="stylesheet" href="/./assets/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="/./assets/css/operator-dashboard.css"/>
    <link rel="stylesheet" href="/./assets/css/all.min.css"/>
    <link rel="icon" type="image/svg+xml" href="/./assets/img/favicon.svg">
    <link rel="icon" type="image/png" href="/./assets/img/favicon.png">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-bottom: 0.25rem;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s;
            border-left: 4px solid #0d6efd;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .alert-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-center mb-4">
                        <i class="fas fa-bus me-2"></i>EasyBus
                        <small class="d-block fs-6 text-muted">Opérateur</small>
                    </h4>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                            <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord
                        </a>

                        <!-- Gestion Géographique -->
                        <div class="nav-section-title text-muted small mt-3 mb-2 px-3">GÉOGRAPHIE</div>
                        <a class="nav-link" href="#" onclick="showSection('locations')">
                            <i class="fas fa-map-marker-alt me-2"></i>Lieux/Villes
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('stops')">
                            <i class="fas fa-map-pin me-2"></i>Arrêts
                        </a>

                        <!-- Gestion Flotte -->
                        <div class="nav-section-title text-muted small mt-3 mb-2 px-3">FLOTTE</div>
                        <a class="nav-link" href="#" onclick="showSection('amenities')">
                            <i class="fas fa-star me-2"></i>Commodités
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('seatPlans')">
                            <i class="fas fa-th me-2"></i>Plans de sièges
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('buses')">
                            <i class="fas fa-bus me-2"></i>Bus
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('seats')">
                            <i class="fas fa-chair me-2"></i>Sièges
                        </a>

                        <!-- Gestion Voyages -->
                        <div class="nav-section-title text-muted small mt-3 mb-2 px-3">VOYAGES</div>
                        <a class="nav-link" href="#" onclick="showSection('routes')">
                            <i class="fas fa-road me-2"></i>Itinéraires
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('trips')">
                            <i class="fas fa-route me-2"></i>Voyages
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('pricing')">
                            <i class="fas fa-tags me-2"></i>Tarification
                        </a>

                        <!-- Gestion Opérationnelle -->
                        <div class="nav-section-title text-muted small mt-3 mb-2 px-3">OPÉRATIONS</div>
                        <a class="nav-link" href="#" onclick="showSection('bookings')">
                            <i class="fas fa-ticket-alt me-2"></i>Réservations
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('payments')">
                            <i class="fas fa-credit-card me-2"></i>Paiements
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('users')">
                            <i class="fas fa-users me-2"></i>Utilisateurs
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('validation')">
                            <i class="fas fa-qrcode me-2"></i>Validation tickets
                        </a>

                        <!-- Rapports -->
                        <div class="nav-section-title text-muted small mt-3 mb-2 px-3">ANALYSES</div>
                        <a class="nav-link" href="#" onclick="showSection('reports')">
                            <i class="fas fa-chart-bar me-2"></i>Rapports
                        </a>

                        <hr class="my-3">
                        <a class="nav-link" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Contenu principal -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="bg-white shadow-sm p-3 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="mb-0" id="pageTitle">Tableau de bord</h2>
                        <div class="d-flex align-items-center">
                            <span class="me-3">Bonjour, <strong id="operatorName">Opérateur</strong></span>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user-circle"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profil</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Paramètres</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Déconnexion</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Tableau de bord -->
                <div id="dashboardSection" class="content-section">
                    <!-- Statistiques -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="stats-card">
                                <div class="stats-icon bg-primary text-white">
                                    <i class="fas fa-ticket-alt"></i>
                                </div>
                                <h3 class="mb-1" id="totalBookingsToday">0</h3>
                                <p class="text-muted mb-0">Réservations aujourd'hui</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stats-card">
                                <div class="stats-icon bg-success text-white">
                                    <i class="fas fa-bus"></i>
                                </div>
                                <h3 class="mb-1" id="activeTrips">0</h3>
                                <p class="text-muted mb-0">Voyages en cours</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stats-card">
                                <div class="stats-icon bg-warning text-white">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <h3 class="mb-1" id="revenueToday">0 FCFA</h3>
                                <p class="text-muted mb-0">Revenus aujourd'hui</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stats-card">
                                <div class="stats-icon bg-info text-white">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h3 class="mb-1" id="totalPassengers">0</h3>
                                <p class="text-muted mb-0">Passagers aujourd'hui</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Graphique des revenus -->
                        <div class="col-lg-8 mb-4">
                            <div class="chart-container">
                                <h5 class="mb-3"><i class="fas fa-chart-line me-2"></i>Évolution des revenus (7 derniers jours)</h5>
                                <canvas id="revenueChart" height="100"></canvas>
                            </div>
                        </div>

                        <!-- Alertes et notifications -->
                        <div class="col-lg-4 mb-4">
                            <div class="chart-container">
                                <h5 class="mb-3"><i class="fas fa-bell me-2"></i>Alertes</h5>
                                <div id="alertsContainer">
                                    <div class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                        <p class="mt-2 text-muted">Chargement...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Voyages du jour -->
                    <div class="row">
                        <div class="col-12">
                            <div class="chart-container">
                                <h5 class="mb-3"><i class="fas fa-calendar-day me-2"></i>Voyages d'aujourd'hui</h5>
                                <div id="todayTripsContainer">
                                    <div class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                        <p class="mt-2 text-muted">Chargement...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Validation des tickets -->
                <div id="validationSection" class="content-section" style="display: none;">
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="chart-container text-center">
                                <h3 class="mb-4"><i class="fas fa-qrcode me-2"></i>Validation des tickets</h3>
                                
                                <div class="mb-4">
                                    <div class="input-group input-group-lg">
                                        <input type="text" class="form-control" id="ticketCodeInput" 
                                               placeholder="Scanner ou saisir le code du ticket" autofocus>
                                        <button class="btn btn-primary" type="button" onclick="validateTicket()">
                                            <i class="fas fa-check me-2"></i>Valider
                                        </button>
                                    </div>
                                </div>

                                <div id="validationResult" class="mt-4"></div>

                                <div class="mt-4">
                                    <button class="btn btn-outline-secondary me-2" onclick="startQRScanner()">
                                        <i class="fas fa-camera me-2"></i>Scanner QR Code
                                    </button>
                                    <button class="btn btn-outline-info" onclick="showValidationHistory()">
                                        <i class="fas fa-history me-2"></i>Historique
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Gestion des réservations -->
                <div id="bookingsSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Gestion des réservations</h3>
                        <div>
                            <button class="btn btn-outline-primary me-2" onclick="exportBookings()">
                                <i class="fas fa-download me-2"></i>Exporter
                            </button>
                            <button class="btn btn-primary" onclick="refreshBookings()">
                                <i class="fas fa-sync-alt me-2"></i>Actualiser
                            </button>
                        </div>
                    </div>

                    <!-- Filtres -->
                    <div class="chart-container mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="bookingStatusFilter" class="form-label">Statut</label>
                                <select class="form-select" id="bookingStatusFilter" onchange="filterBookings()">
                                    <option value="">Tous les statuts</option>
                                    <option value="confirmed">Confirmé</option>
                                    <option value="pending">En attente</option>
                                    <option value="cancelled">Annulé</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="bookingDateFilter" class="form-label">Date</label>
                                <input type="date" class="form-control" id="bookingDateFilter" onchange="filterBookings()">
                            </div>
                            <div class="col-md-4">
                                <label for="bookingSearchFilter" class="form-label">Rechercher</label>
                                <input type="text" class="form-control" id="bookingSearchFilter"
                                       placeholder="Email, téléphone, nom..." onkeyup="filterBookings()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-outline-secondary w-100" onclick="clearBookingFilters()">
                                    <i class="fas fa-times"></i> Effacer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tableau des réservations -->
                    <div class="chart-container">
                        <div id="bookingsTableContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Chargement des réservations...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Gestion des voyages -->
                <div id="tripsSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Gestion des voyages</h3>
                        <button class="btn btn-primary" onclick="showCreateTripModal()">
                            <i class="fas fa-plus me-2"></i>Nouveau voyage
                        </button>
                    </div>

                    <!-- Filtres pour les voyages -->
                    <div class="chart-container mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="tripStatusFilter" class="form-label">Statut</label>
                                <select class="form-select" id="tripStatusFilter" onchange="filterTrips()">
                                    <option value="">Tous les statuts</option>
                                    <option value="scheduled">Programmé</option>
                                    <option value="ongoing">En cours</option>
                                    <option value="completed">Terminé</option>
                                    <option value="cancelled">Annulé</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="tripDateFromFilter" class="form-label">Date de début</label>
                                <input type="date" class="form-control" id="tripDateFromFilter" onchange="filterTrips()">
                            </div>
                            <div class="col-md-3">
                                <label for="tripDateToFilter" class="form-label">Date de fin</label>
                                <input type="date" class="form-control" id="tripDateToFilter" onchange="filterTrips()">
                            </div>
                            <div class="col-md-4">
                                <label for="tripRouteFilter" class="form-label">Trajet</label>
                                <select class="form-select" id="tripRouteFilter" onchange="filterTrips()">
                                    <option value="">Tous les trajets</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Tableau des voyages -->
                    <div class="chart-container">
                        <div id="tripsTableContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Chargement des voyages...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Gestion des bus -->
                <div id="busesSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Gestion des bus</h3>
                        <button class="btn btn-primary" onclick="showCreateBusModal()">
                            <i class="fas fa-plus me-2"></i>Nouveau bus
                        </button>
                    </div>

                    <!-- Filtres pour les bus -->
                    <div class="chart-container mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="busStatusFilter" class="form-label">Statut</label>
                                <select class="form-select" id="busStatusFilter" onchange="filterBuses()">
                                    <option value="">Tous les statuts</option>
                                    <option value="active">Actif</option>
                                    <option value="maintenance">En maintenance</option>
                                    <option value="inactive">Inactif</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="busTypeFilter" class="form-label">Type</label>
                                <select class="form-select" id="busTypeFilter" onchange="filterBuses()">
                                    <option value="">Tous les types</option>
                                    <option value="standard">Standard</option>
                                    <option value="vip">VIP</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="busSearchFilter" class="form-label">Rechercher</label>
                                <input type="text" class="form-control" id="busSearchFilter"
                                       placeholder="Numéro d'immatriculation, marque..." onkeyup="filterBuses()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-outline-secondary w-100" onclick="clearBusFilters()">
                                    <i class="fas fa-times"></i> Effacer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tableau des bus -->
                    <div class="chart-container">
                        <div id="busesGridContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Chargement des bus...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Gestion des Sièges -->
                <div id="seatsSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Gestion des Sièges</h3>
                        <div>
                            <button class="btn btn-outline-info me-2" onclick="loadSeats()">
                                <i class="fas fa-sync-alt me-2"></i>Actualiser
                            </button>
                        </div>
                    </div>

                    <!-- Filtres pour les sièges -->
                    <div class="chart-container mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="seatBusFilter" class="form-label">Bus</label>
                                <select class="form-select" id="seatBusFilter" onchange="filterSeats()">
                                    <option value="">Tous les bus</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="seatStatusFilter" class="form-label">Statut</label>
                                <select class="form-select" id="seatStatusFilter" onchange="filterSeats()">
                                    <option value="">Tous les statuts</option>
                                    <option value="available">Disponible</option>
                                    <option value="occupied">Occupé</option>
                                    <option value="maintenance">En maintenance</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="seatTypeFilter" class="form-label">Type</label>
                                <select class="form-select" id="seatTypeFilter" onchange="filterSeats()">
                                    <option value="">Tous les types</option>
                                    <option value="standard">Standard</option>
                                    <option value="premium">Premium</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-outline-secondary w-100" onclick="clearSeatFilters()">
                                    <i class="fas fa-times"></i> Effacer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tableau des sièges -->
                    <div class="chart-container">
                        <div id="seatsTableContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Chargement des sièges...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Rapports -->
                <div id="reportsSection" class="content-section" style="display: none;">
                    <h3 class="mb-4">Rapports et analyses</h3>

                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="chart-container text-center">
                                <h5>Rapport de revenus</h5>
                                <p class="text-muted">Analyse des revenus par période</p>
                                <button class="btn btn-outline-primary" onclick="generateRevenueReport()">
                                    <i class="fas fa-chart-line me-2"></i>Générer
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="chart-container text-center">
                                <h5>Rapport d'occupation</h5>
                                <p class="text-muted">Taux d'occupation des bus</p>
                                <button class="btn btn-outline-primary" onclick="generateOccupancyReport()">
                                    <i class="fas fa-chart-pie me-2"></i>Générer
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="chart-container text-center">
                                <h5>Rapport des trajets</h5>
                                <p class="text-muted">Performance par trajet</p>
                                <button class="btn btn-outline-primary" onclick="generateRoutesReport()">
                                    <i class="fas fa-route me-2"></i>Générer
                                </button>
                            </div>
                        </div>
                    </div>
                    <div id="reportResultContainer" class="chart-container" style="display: none;">
                        <h5 id="reportTitle">Résultats du rapport</h5>
                        <div id="reportContent"></div>
                    </div>
                </div>

                <!-- Section Gestion des Lieux -->
                <div id="locationsSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Gestion des Lieux/Villes</h3>
                        <button class="btn btn-primary" onclick="showCreateLocationModal()">
                            <i class="fas fa-plus me-2"></i>Nouveau lieu
                        </button>
                    </div>

                    <!-- Filtres pour les lieux -->
                    <div class="chart-container mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="locationStatusFilter" class="form-label">Statut</label>
                                <select class="form-select" id="locationStatusFilter" onchange="filterLocations()">
                                    <option value="">Tous les statuts</option>
                                    <option value="active">Actif</option>
                                    <option value="inactive">Inactif</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="locationCountryFilter" class="form-label">Pays</label>
                                <select class="form-select" id="locationCountryFilter" onchange="filterLocations()">
                                    <option value="">Tous les pays</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="locationSearchFilter" class="form-label">Rechercher</label>
                                <input type="text" class="form-control" id="locationSearchFilter"
                                       placeholder="Nom de lieu, région..." onkeyup="filterLocations()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-outline-secondary w-100" onclick="clearLocationFilters()">
                                    <i class="fas fa-times"></i> Effacer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tableau des lieux -->
                    <div class="chart-container">
                        <div id="locationsTableContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Chargement des lieux...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Gestion des Arrêts -->
                <div id="stopsSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Gestion des Arrêts</h3>
                        <button class="btn btn-primary" onclick="showCreateStopModal()">
                            <i class="fas fa-plus me-2"></i>Nouvel arrêt
                        </button>
                    </div>

                    <!-- Filtres pour les arrêts -->
                    <div class="chart-container mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="stopLocationFilter" class="form-label">Lieu</label>
                                <select class="form-select" id="stopLocationFilter" onchange="filterStops()">
                                    <option value="">Tous les lieux</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="stopSearchFilter" class="form-label">Rechercher</label>
                                <input type="text" class="form-control" id="stopSearchFilter"
                                       placeholder="Nom d'arrêt, adresse..." onkeyup="filterStops()">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-outline-secondary w-100" onclick="clearStopFilters()">
                                    <i class="fas fa-times"></i> Effacer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tableau des arrêts -->
                    <div class="chart-container">
                        <div id="stopsTableContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Chargement des arrêts...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Gestion des Commodités -->
                <div id="amenitiesSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Gestion des Commodités</h3>
                        <button class="btn btn-primary" onclick="showCreateAmenityModal()">
                            <i class="fas fa-plus me-2"></i>Nouvelle commodité
                        </button>
                    </div>

                    <!-- Grille des commodités -->
                    <div id="amenitiesGridContainer">
                        <div class="text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                            <p class="mt-2 text-muted">Chargement des commodités...</p>
                        </div>
                    </div>
                </div>

                <!-- Section Gestion des Plans de Sièges -->
                <div id="seatPlansSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Gestion des Plans de Sièges</h3>
                        <button class="btn btn-primary" onclick="showCreateSeatPlanModal()">
                            <i class="fas fa-plus me-2"></i>Nouveau plan
                        </button>
                    </div>

                    <!-- Grille des plans de sièges -->
                    <div id="seatPlansGridContainer">
                        <div class="text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                            <p class="mt-2 text-muted">Chargement des plans de sièges...</p>
                        </div>
                    </div>
                </div>

                <!-- Section Gestion des Itinéraires -->
                <div id="routesSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Gestion des Itinéraires</h3>
                        <button class="btn btn-primary" onclick="showCreateRouteModal()">
                            <i class="fas fa-plus me-2"></i>Nouvel itinéraire
                        </button>
                    </div>

                    <!-- Filtres pour les itinéraires -->
                    <div class="chart-container mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="routeStatusFilter" class="form-label">Statut</label>
                                <select class="form-select" id="routeStatusFilter" onchange="filterRoutes()">
                                    <option value="">Tous les statuts</option>
                                    <option value="active">Actif</option>
                                    <option value="inactive">Inactif</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="routeDepartureFilter" class="form-label">Départ</label>
                                <select class="form-select" id="routeDepartureFilter" onchange="filterRoutes()">
                                    <option value="">Tous les départs</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="routeSearchFilter" class="form-label">Rechercher</label>
                                <input type="text" class="form-control" id="routeSearchFilter"
                                       placeholder="Nom d'itinéraire..." onkeyup="filterRoutes()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-outline-secondary w-100" onclick="clearRouteFilters()">
                                    <i class="fas fa-times"></i> Effacer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tableau des itinéraires -->
                    <div class="chart-container">
                        <div id="routesTableContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Chargement des itinéraires...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Gestion de la Tarification -->
                <div id="pricingSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Gestion de la Tarification</h3>
                        <button class="btn btn-primary" onclick="showCreatePricingModal()">
                            <i class="fas fa-plus me-2"></i>Nouveau tarif
                        </button>
                    </div>

                    <!-- Filtres pour la tarification -->
                    <div class="chart-container mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="pricingRouteFilter" class="form-label">Itinéraire</label>
                                <select class="form-select" id="pricingRouteFilter" onchange="filterPricing()">
                                    <option value="">Tous les itinéraires</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="pricingBusTypeFilter" class="form-label">Type de bus</label>
                                <select class="form-select" id="pricingBusTypeFilter" onchange="filterPricing()">
                                    <option value="">Tous les types</option>
                                    <option value="standard">Standard</option>
                                    <option value="vip">VIP</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="pricingSeatTypeFilter" class="form-label">Type de siège</label>
                                <select class="form-select" id="pricingSeatTypeFilter" onchange="filterPricing()">
                                    <option value="">Tous les types</option>
                                    <option value="standard">Standard</option>
                                    <option value="premium">Premium</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-outline-secondary w-100" onclick="clearPricingFilters()">
                                    <i class="fas fa-times"></i> Effacer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tableau de la tarification -->
                    <div class="chart-container">
                        <div id="pricingTableContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Chargement de la tarification...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Gestion des Paiements -->
                <div id="paymentsSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Suivi des Paiements</h3>
                        <div>
                            <button class="btn btn-outline-primary me-2" onclick="exportPayments()">
                                <i class="fas fa-download me-2"></i>Exporter
                            </button>
                            <button class="btn btn-primary" onclick="refreshPayments()">
                                <i class="fas fa-sync-alt me-2"></i>Actualiser
                            </button>
                        </div>
                    </div>

                    <!-- Filtres pour les paiements -->
                    <div class="chart-container mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="paymentStatusFilter" class="form-label">Statut</label>
                                <select class="form-select" id="paymentStatusFilter" onchange="filterPayments()">
                                    <option value="">Tous les statuts</option>
                                    <option value="successful">Réussi</option>
                                    <option value="pending">En attente</option>
                                    <option value="failed">Échoué</option>
                                    <option value="refunded">Remboursé</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="paymentMethodFilter" class="form-label">Méthode</label>
                                <select class="form-select" id="paymentMethodFilter" onchange="filterPayments()">
                                    <option value="">Toutes les méthodes</option>
                                    <option value="mobile_money">Mobile Money</option>
                                    <option value="credit_card">Carte bancaire</option>
                                    <option value="cash">Espèces</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="paymentDateFromFilter" class="form-label">Date de début</label>
                                <input type="date" class="form-control" id="paymentDateFromFilter" onchange="filterPayments()">
                            </div>
                            <div class="col-md-3">
                                <label for="paymentDateToFilter" class="form-label">Date de fin</label>
                                <input type="date" class="form-control" id="paymentDateToFilter" onchange="filterPayments()">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-outline-secondary w-100" onclick="clearPaymentFilters()">
                                    <i class="fas fa-times"></i> Effacer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tableau des paiements -->
                    <div class="chart-container">
                        <div id="paymentsTableContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Chargement des paiements...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section Gestion des Utilisateurs -->
                <div id="usersSection" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Gestion des Utilisateurs</h3>
                        <button class="btn btn-primary" onclick="showCreateUserModal()">
                            <i class="fas fa-plus me-2"></i>Nouvel utilisateur
                        </button>
                    </div>

                    <!-- Filtres pour les utilisateurs -->
                    <div class="chart-container mb-4">
                        <div class="row">
                            <div class="col-md-2">
                                <label for="userRoleFilter" class="form-label">Rôle</label>
                                <select class="form-select" id="userRoleFilter" onchange="filterUsers()">
                                    <option value="">Tous les rôles</option>
                                    <option value="traveler">Voyageur</option>
                                    <option value="driver">Chauffeur</option>
                                    <option value="controller">Contrôleur</option>
                                    <option value="operator">Opérateur</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="userStatusFilter" class="form-label">Statut</label>
                                <select class="form-select" id="userStatusFilter" onchange="filterUsers()">
                                    <option value="">Tous les statuts</option>
                                    <option value="active">Actif</option>
                                    <option value="inactive">Inactif</option>
                                    <option value="suspended">Suspendu</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="userVerificationFilter" class="form-label">Vérification</label>
                                <select class="form-select" id="userVerificationFilter" onchange="filterUsers()">
                                    <option value="">Tous</option>
                                    <option value="pending">En attente</option>
                                    <option value="verified">Vérifié</option>
                                    <option value="rejected">Rejeté</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="userSearchFilter" class="form-label">Rechercher</label>
                                <input type="text" class="form-control" id="userSearchFilter"
                                       placeholder="Email, nom, téléphone..." onkeyup="filterUsers()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-outline-secondary w-100" onclick="clearUserFilters()">
                                    <i class="fas fa-times"></i> Effacer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tableau des utilisateurs -->
                    <div class="chart-container">
                        <div id="usersTableContainer">
                            <div class="text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                <p class="mt-2 text-muted">Chargement des utilisateurs...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages d'alerte -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        <div id="alertContainer"></div>
    </div>

    <!-- Modal pour Lieu/Ville -->
    <div class="modal fade" id="locationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="locationModalTitle">Nouveau Lieu</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="locationForm">
                        <input type="hidden" id="locationId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="locationName" class="form-label">Nom du lieu *</label>
                                <input type="text" class="form-control" id="locationName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="locationRegion" class="form-label">Région *</label>
                                <input type="text" class="form-control" id="locationRegion" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="locationCountry" class="form-label">Pays *</label>
                                <input type="text" class="form-control" id="locationCountry" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="locationTimeZone" class="form-label">Fuseau horaire *</label>
                                <select class="form-select" id="locationTimeZone" required>
                                    <option value="">Sélectionner...</option>
                                    <option value="Africa/Porto-Novo">Africa/Porto-Novo</option>
                                    <option value="Africa/Abidjan">Africa/Abidjan</option>
                                    <option value="Africa/Accra">Africa/Accra</option>
                                    <option value="Africa/Bamako">Africa/Bamako</option>
                                    <option value="Africa/Ouagadougou">Africa/Ouagadougou</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="locationLatitude" class="form-label">Latitude *</label>
                                <input type="number" step="any" class="form-control" id="locationLatitude" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="locationLongitude" class="form-label">Longitude *</label>
                                <input type="number" step="any" class="form-control" id="locationLongitude" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="locationStatus" class="form-label">Statut</label>
                            <select class="form-select" id="locationStatus">
                                <option value="active">Actif</option>
                                <option value="inactive">Inactif</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveLocation()">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Arrêt -->
    <div class="modal fade" id="stopModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="stopModalTitle">Nouvel Arrêt</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="stopForm">
                        <input type="hidden" id="stopId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="stopName" class="form-label">Nom de l'arrêt *</label>
                                <input type="text" class="form-control" id="stopName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="stopLocationId" class="form-label">Lieu *</label>
                                <select class="form-select" id="stopLocationId" required>
                                    <option value="">Sélectionner un lieu...</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="stopAddress" class="form-label">Adresse</label>
                            <textarea class="form-control" id="stopAddress" rows="2"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="stopLatitude" class="form-label">Latitude *</label>
                                <input type="number" step="any" class="form-control" id="stopLatitude" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="stopLongitude" class="form-label">Longitude *</label>
                                <input type="number" step="any" class="form-control" id="stopLongitude" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveStop()">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Commodité -->
    <div class="modal fade" id="amenityModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="amenityModalTitle">Nouvelle Commodité</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="amenityForm">
                        <input type="hidden" id="amenityId">
                        <div class="mb-3">
                            <label for="amenityName" class="form-label">Nom de la commodité *</label>
                            <input type="text" class="form-control" id="amenityName" required>
                        </div>
                        <div class="mb-3">
                            <label for="amenityDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="amenityDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveAmenity()">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Plan de Sièges -->
    <div class="modal fade" id="seatPlanModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="seatPlanModalTitle">Nouveau Plan de Sièges</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="seatPlanForm">
                        <input type="hidden" id="seatPlanId">
                        <div class="mb-3">
                            <label for="seatPlanConfig" class="form-label">Configuration *</label>
                            <input type="text" class="form-control" id="seatPlanConfig" placeholder="Ex: 2x2, 2x3" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="seatPlanRows" class="form-label">Nombre de rangées *</label>
                                <input type="number" class="form-control" id="seatPlanRows" min="1" max="20" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="seatPlanColumns" class="form-label">Nombre de colonnes *</label>
                                <input type="number" class="form-control" id="seatPlanColumns" min="1" max="10" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveSeatPlan()">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Bus -->
    <div class="modal fade" id="busModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="busModalTitle">Nouveau Bus</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="busForm">
                        <input type="hidden" id="busId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="busRegistrationNumber" class="form-label">Immatriculation *</label>
                                <input type="text" class="form-control" id="busRegistrationNumber" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="busCapacity" class="form-label">Capacité *</label>
                                <input type="number" class="form-control" id="busCapacity" min="1" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="busBrand" class="form-label">Marque *</label>
                                <input type="text" class="form-control" id="busBrand" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="busModel" class="form-label">Modèle *</label>
                                <input type="text" class="form-control" id="busModel" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="busType" class="form-label">Type de bus</label>
                                <select class="form-select" id="busType">
                                    <option value="standard">Standard</option>
                                    <option value="vip">VIP</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="busSeatPlanId" class="form-label">Plan de sièges</label>
                                <select class="form-select" id="busSeatPlanId">
                                    <option value="">Sélectionner un plan...</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="busYearManufactured" class="form-label">Année de fabrication</label>
                                <input type="number" class="form-control" id="busYearManufactured" min="1990" max="2030">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="busStatus" class="form-label">Statut</label>
                                <select class="form-select" id="busStatus">
                                    <option value="active">Actif</option>
                                    <option value="under_maintenance">En maintenance</option>
                                    <option value="inactive">Inactif</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Commodités</label>
                            <div id="busAmenitiesContainer" class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                <div class="text-muted">Chargement des commodités...</div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveBus()">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Itinéraire -->
    <div class="modal fade" id="routeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="routeModalTitle">Nouvel Itinéraire</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="routeForm">
                        <input type="hidden" id="routeId">
                        <div class="mb-3">
                            <label for="routeName" class="form-label">Nom de l'itinéraire *</label>
                            <input type="text" class="form-control" id="routeName" required>
                        </div>
                        <div class="mb-3">
                            <label for="routeDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="routeDescription" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="routeDepartureLocationId" class="form-label">Lieu de départ *</label>
                                <select class="form-select" id="routeDepartureLocationId" required>
                                    <option value="">Sélectionner...</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="routeDestinationLocationId" class="form-label">Lieu de destination *</label>
                                <select class="form-select" id="routeDestinationLocationId" required>
                                    <option value="">Sélectionner...</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="routeDistance" class="form-label">Distance (km)</label>
                                <input type="number" step="0.1" class="form-control" id="routeDistance">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="routeDuration" class="form-label">Durée estimée</label>
                                <input type="time" class="form-control" id="routeDuration" placeholder="Ex: 2h30">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="routeStatus" class="form-label">Statut</label>
                            <select class="form-select" id="routeStatus">
                                <option value="active">Actif</option>
                                <option value="inactive">Inactif</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveRoute()">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Voyage -->
    <div class="modal fade" id="tripModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tripModalTitle">Nouveau Voyage</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="tripForm">
                        <input type="hidden" id="tripId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tripRouteId" class="form-label">Itinéraire *</label>
                                <select class="form-select" id="tripRouteId" required>
                                    <option value="">Sélectionner un itinéraire...</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tripBusId" class="form-label">Bus *</label>
                                <select class="form-select" id="tripBusId" required>
                                    <option value="">Sélectionner un bus...</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tripDriverId" class="form-label">Chauffeur *</label>
                                <select class="form-select" id="tripDriverId" required>
                                    <option value="">Sélectionner un chauffeur...</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tripControllerId" class="form-label">Contrôleur</label>
                                <select class="form-select" id="tripControllerId">
                                    <option value="">Sélectionner un contrôleur...</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tripEstimatedDepartureTime" class="form-label">Heure de départ prévue *</label>
                                <input type="datetime-local" class="form-control" id="tripEstimatedDepartureTime" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tripEstimatedArrivalTime" class="form-label">Heure d'arrivée prévue *</label>
                                <input type="datetime-local" class="form-control" id="tripEstimatedArrivalTime" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tripActualDepartureTime" class="form-label">Heure de départ réel</label>
                                <input type="datetime-local" class="form-control" id="tripActualDepartureTime">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tripActualArrivalTime" class="form-label">Heure d'arrivée réel</label>
                                <input type="datetime-local" class="form-control" id="tripActualArrivalTime">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="tripTrackingLink" class="form-label">Lien de suivis (tracking)</label>
                            <input type="url" class="form-control" id="tripTrackingLink" placeholder="https://tracking.example.com/trip123">
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tripStatus" class="form-label">Statut</label>
                                <select class="form-select" id="tripStatus">
                                    <option value="planned">Planifié</option>
                                    <option value="ongoing">En cours</option>
                                    <option value="completed">Terminé</option>
                                    <option value="delayed">Retardé</option>
                                    <option value="cancelled">Annulé</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="tripDelayReason" class="form-label">Raison du retard</label>
                            <textarea class="form-control" id="tripDelayReason" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="tripCancellationReason" class="form-label">Raison de l'annulation</label>
                            <textarea class="form-control" id="tripCancellationReason" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveTrip()">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Utilisateur -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalTitle">Nouvel Utilisateur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="userId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="userFirstName" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="userFirstName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="userLastName" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="userLastName" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="userEmail" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="userEmail" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="userPhone" class="form-label">Téléphone *</label>
                                <input type="tel" class="form-control" id="userPhone" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="userDateOfBirth" class="form-label">Date de naissance</label>
                                <input type="date" class="form-control" id="userDateOfBirth">
                            </div>
                            <div class="col-md-6 mb-3" id="passwordField">
                                <label for="userPassword" class="form-label">Mot de passe *</label>
                                <input type="password" class="form-control" id="userPassword" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="userStatus" class="form-label">Statut</label>
                                <select class="form-select" id="userStatus">
                                    <option value="active">Actif</option>
                                    <option value="inactive">Inactif</option>
                                    <option value="suspended">Suspendu</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="userVerificationStatus" class="form-label">Statut de vérification</label>
                                <select class="form-select" id="userVerificationStatus">
                                    <option value="pending">En attente</option>
                                    <option value="verified">Vérifié</option>
                                    <option value="rejected">Rejeté</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Tarification -->
    <div class="modal fade" id="pricingModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="pricingModalTitle">Nouvelle Tarification</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="pricingForm">
                        <input type="hidden" id="pricingId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="pricingRouteId" class="form-label">Itinéraire</label>
                                <select class="form-select" id="pricingRouteId">
                                    <option value="">Tous les itinéraires</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="pricingPrice" class="form-label">Prix (FCFA) *</label>
                                <input type="number" class="form-control" id="pricingPrice" min="0" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="pricingBusType" class="form-label">Type de bus</label>
                                <select class="form-select" id="pricingBusType">
                                    <option value="standard">Standard</option>
                                    <option value="vip">VIP</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="pricingSeatType" class="form-label">Type de siège</label>
                                <select class="form-select" id="pricingSeatType">
                                    <option value="standard">Standard</option>
                                    <option value="premium">Premium</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="pricingStartDate" class="form-label">Date de début</label>
                                <input type="date" class="form-control" id="pricingStartDate">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="pricingEndDate" class="form-label">Date de fin</label>
                                <input type="date" class="form-control" id="pricingEndDate">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="savePricing()">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Gestion des Sièges -->
    <div class="modal fade" id="seatManagementModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="seatManagementModalTitle">Gestion des Sièges</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="seatManagementBusId">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>Grille des sièges</h6>
                            <p class="text-muted">Cliquez sur les cellules pour sélectionner les sièges à ajouter</p>
                            <div id="seatGrid" class="mb-3"></div>
                        </div>
                        <div class="col-md-4">
                            <h6>Configuration</h6>
                            <div class="mb-3">
                                <label for="newSeatType" class="form-label">Type de siège</label>
                                <select class="form-select" id="newSeatType">
                                    <option value="standard">Standard</option>
                                    <option value="premium">Premium</option>
                                </select>
                            </div>
                            <div id="selectedSeatsDisplay">
                                <p class="text-muted">Aucun siège sélectionné</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveSelectedSeats()">Ajouter les sièges</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Siège individuel -->
    <div class="modal fade" id="seatModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="seatModalTitle">Modifier le Siège</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="seatForm">
                        <input type="hidden" id="seatId">
                        <input type="hidden" id="seatBusId">
                        <div class="mb-3">
                            <label for="seatNumber" class="form-label">Numéro de siège</label>
                            <input type="text" class="form-control" id="seatNumber" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="seatType" class="form-label">Type de siège</label>
                            <select class="form-select" id="seatType">
                                <option value="standard">Standard</option>
                                <option value="premium">Premium</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="seatStatus" class="form-label">Statut</label>
                            <select class="form-select" id="seatStatus">
                                <option value="active">Actif</option>
                                <option value="inactive">Inactif</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveSeat()">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Détails du Voyage -->
    <div class="modal fade" id="tripDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tripDetailsModalTitle">Détails du Voyage</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="tripDetailsContent">
                        <div class="text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                            <p class="mt-2 text-muted">Chargement des détails...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Gestion des Arrêts d'Itinéraire -->
    <div class="modal fade" id="routeStopsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="routeStopsModalTitle">Gestion des Arrêts</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="routeStopsRouteId">

                    <!-- Formulaire d'ajout d'arrêt -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-plus me-2"></i>Ajouter un arrêt</h6>
                        </div>
                        <div class="card-body">
                            <form id="routeStopForm">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="routeStopStopId" class="form-label">Arrêt *</label>
                                        <select class="form-select" id="routeStopStopId" required>
                                            <option value="">Sélectionner un arrêt...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="routeStopOrder" class="form-label">Ordre *</label>
                                        <input type="number" class="form-control" id="routeStopOrder" min="1" required>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="routeStopType" class="form-label">Type d'arrêt *</label>
                                        <select class="form-select" id="routeStopType" required>
                                            <option value="">Sélectionner...</option>
                                            <option value="boarding">Embarquement</option>
                                            <option value="intermediate">Intermédiaire</option>
                                            <option value="dropping">Débarquement</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2 mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="button" class="btn btn-primary w-100" onclick="addRouteStop()">
                                            <i class="fas fa-plus"></i> Ajouter
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Liste des arrêts existants -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-list me-2"></i>Arrêts de l'itinéraire</h6>
                        </div>
                        <div class="card-body">
                            <div id="routeStopsContainer">
                                <div class="text-center py-4">
                                    <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                    <p class="mt-2 text-muted">Chargement des arrêts...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour Gestion des Arrêts de Voyage -->
    <div class="modal fade" id="tripStopsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tripStopsModalTitle">Gestion des Arrêts du Voyage</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="tripStopsTripId">

                    <!-- Formulaire d'ajout d'arrêt -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-plus me-2"></i>Ajouter un arrêt</h6>
                        </div>
                        <div class="card-body">
                            <form id="tripStopForm">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="tripStopStopId" class="form-label">Arrêt *</label>
                                        <select class="form-select" id="tripStopStopId" required>
                                            <option value="">Sélectionner un arrêt...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="tripStopArrivalTime" class="form-label">Heure d'arrivée prévue *</label>
                                        <input type="datetime-local" class="form-control" id="tripStopArrivalTime" required>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="button" class="btn btn-primary w-100" onclick="addTripStop()">
                                            <i class="fas fa-plus"></i> Ajouter
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Liste des arrêts existants -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-list me-2"></i>Arrêts du voyage</h6>
                        </div>
                        <div class="card-body">
                            <div id="tripStopsContainer">
                                <div class="text-center py-4">
                                    <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                    <p class="mt-2 text-muted">Chargement des arrêts...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/./assets/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.9/dist/chart.umd.min.js"></script>
    <script src="/./assets/js/operator-dashboard.js" defer></script>
    <script src="/./assets/js/modules/operator-crud.js" defer></script>
    <script src="/./assets/js/auth.js" defer></script>
</body>
</html>

