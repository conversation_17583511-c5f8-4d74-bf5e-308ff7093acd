<?php

require_once __DIR__ . '/../Models/TripModel.php'; // Importer le modèle TripModel
require_once __DIR__ . '/../Models/RouteModel.php'; // Importer le modèle RouteModel
require_once __DIR__ . '/../Models/BusModel.php'; // Importer le modèle BusModel
require_once __DIR__ . '/../Models/UserModel.php'; // Importer le modèle UserModel
require_once __DIR__ . '/../Helpers/response.php'; // Importer la fonction sendResponse
require_once __DIR__ . '/../Helpers/validate.php'; // Importer la fonction validateFields
require_once __DIR__ . '/../Middlewares/AuthMiddleware.php'; // Importer la classe AuthMiddleware
require_once __DIR__ . '/../Middlewares/RoleMiddleware.php'; // Importer la classe RoleMiddleware

class TripController
{
    private $tripModel;
    private $routeModel;
    private $busModel;
    private $userModel;

    public function __construct()
    {
        $this->tripModel = new TripModel();
        $this->routeModel = new RouteModel();
        $this->busModel = new BusModel();
        $this->userModel = new UserModel();
    }

    // --------------------------
    // Gestion des Voyages
    // --------------------------

    /** GET /v1/trips/search */
    /** Rechercher des voyages avec filtre */
    public function searchTrips(array $params = []): void
    {
        $validationRules = [
            'from' => 'required',
            'to' => 'required',
            'date' => 'required',
            'date' => 'futureDate',
        ];

        validateFields($params, $validationRules);

        $filters = [];
        if (isset($params['date'])) {
            $filters['date'] = $params['date'];
        }
        if (isset($params['from'])) {
            $filters['from_location_id'] = $params['from'];
        }
        if (isset($params['to'])) {
            $filters['to_location_id'] = $params['to'];
        }

        $responses = $this->tripModel->searchTrips($filters);

        if (!$responses) {
            sendResponse(404, ['error' => 'Aucun Voyage ne correspond à ces critères de recherche']);
        }

        $trips = [];

        foreach ($responses as $response) {
            $trips[] = $this->buildTripResults($response);
        }

        sendResponse(200, [
            'success' => true,
            'data' => $trips,
            'total' => count($trips),
            'message' => 'Voyages récupérés avec succès'
        ]);
    }

    /**
     * Récupère les détails d'un voyage spécifique (GET /v1/trips/{id})
     * @param array $params Paramètres incluant l'ID du voyage
     * @return void
     */
    public function getTripDetails(array $params = []): void
    {
        $trip_id = isset($params['id']) ? (int)$params['id'] : 0;

        $response = $this->tripModel->getTripById($trip_id);

        if (!$response) {
            sendResponse(404, ['error' => 'Aucun Voyage ne correspond à cet ID']);
        }

        $trip = $this->buildTripDetails($response);

        sendResponse(200, [
            'success' => true,
            'data' => $trip,
            'message' => 'Voyage récupéré avec succès'
        ]);
    }

    /** GET /v1/trips */
    public function getAllTrips(): void
    {
        AuthMiddleware::authenticate('operator');
        $limit = $_GET['limit'] ?? 10;
        $page = $_GET['page'] ?? 1;
        $offset = ($page - 1) * $limit;

        $trips = $this->tripModel->getAllTrips($limit, $offset);
        sendResponse(200, ['trips' => $trips]);
    }

    /** GET /v1/trips/{id} */

    public function getTripById($id)
    {
        $trip = $this->tripModel->getTripById($id);
        if ($trip) {
            sendResponse(200, $trip);
        } else {
            sendResponse(404, ['message' => 'Voyage non trouvé']);
        }
    }

    /** POST /v1/trips */
    public function createTrip(array $request): array
    {
        $user_id = AuthMiddleware::authenticate('operator')->sub;

        // Définir les règles de validation
        $validationRules = [
            'route_id' => 'required',
            'bus_id' => 'required',
            'driver_id' => 'required',
            'estimated_departure_time' => 'required',
            'estimated_arrival_time' => 'required',
        ];

        // Validation des données avec validateFields
        validateFields($request, $validationRules);

        // Vérification des références
        if (!$this->routeModel->getRouteById($request['route_id'])) {
            http_response_code(404);
            return ['error' => 'Trajet invalide'];
        }

        if (!$this->busModel->getBusById($request['bus_id'])) {
            http_response_code(404);
            return ['error' => 'Bus invalide'];
        }

        if (!$this->userModel->getUserById($request['driver_id'])) {
            http_response_code(404);
            return ['error' => 'Chauffeur invalide'];
        }

        try {
            $request['created_by'] = $user_id;
            $tripId = $this->tripModel->createTrip($request);
            http_response_code(201);
            return ['trip_id' => $tripId];
        } catch (PDOException $e) {
            http_response_code(500);
            return ['error' => 'Erreur serveur'];
        }
    }

    /** PUT /v1/trips/{id} */
    public function updateTripStatus(int $tripId, array $request): array
    {
        AuthMiddleware::authenticate('operator');

        $allowedStatus = ['planned', 'ongoing', 'completed', 'delayed'];
        if (!in_array($request['status'], $allowedStatus)) {
            http_response_code(400);
            return ['error' => 'Statut invalide'];
        }

        if ($this->tripModel->updateTrip($tripId, $request)) {
            http_response_code(200);
            return ['message' => 'Statut mis à jour'];
        }

        http_response_code(400);
        return ['error' => 'Échec de la mise à jour'];
    }

    // --------------------------
    // Gestion des Tarifications
    // --------------------------

    /** POST /v1/pricings */
    public function createPricing(array $request): array
    {
        AuthMiddleware::authenticate('admin');

        if (empty($request['bus_type']) || empty($request['seat_type']) || empty($request['price'])) {
            http_response_code(400);
            return ['error' => 'Données tarifaires incomplètes'];
        }

        if ($request['start_date'] > $request['end_date']) {
            http_response_code(400);
            return ['error' => 'Dates invalides'];
        }

        try {
            $pricingId = $this->tripModel->createPricing([
                ...$request,
                'created_by' => $_SERVER['AUTH_USER_ID']
            ]);
            http_response_code(201);
            return ['pricing_id' => $pricingId];
        } catch (PDOException $e) {
            http_response_code(500);
            return ['error' => 'Échec de la création'];
        }
    }

    /** GET /v1/trips/{id}/stops */
    public function getTripStops(array $params = []): void
    {
        $trip_id = isset($params['id']) ? (int)$params['id'] : 0;

        $stops = $this->tripModel->getTripStops($trip_id);
        sendResponse(200, [
            'success' => true,
            'data' => $stops,
            'total' => count($stops),
            'message' => 'Arrêts récupérés avec succès'
        ]);
    }

    private function tripStops(int $tripId): array
    {
        $stops = $this->tripModel->getTripStops($tripId);
        http_response_code(200);
        return $stops ?: [];
    }

    /** DELETE /v1/trips/{id}/stops/{stopId} */
    public function deleteTripStop(int $tripId, int $stopId): array
    {
        AuthMiddleware::authenticate('admin');

        if (!$this->tripModel->deleteTripStop($tripId, $stopId)) {
            http_response_code(404);
            return ['error' => 'Association introuvable'];
        }

        http_response_code(200);
        return ['message' => 'Point d\'arrêt retiré'];
    }

    // --------------------------
    // Gestion de la disponibilité des sièges
    // --------------------------

    /**
     * Calcule la disponibilité des sièges dans un bus pour un voyage donné
     * @param array $params Paramètres incluant l'ID du voyage
     * @return void
     *
     **/
    public function getTripSeats(array $params = []): void
    {
        $trip = $this->tripModel->getTripById($params['id']);
        $seats = $this->seatsAvailability($trip);
        sendResponse(200, [
            'success' => true,
            'data' => $seats,
            'total' => count($seats),
            'message' => 'Sièges récupérés avec succès'
        ]);
    }

    private function seatsAvailability(array $trip): array
    {
        $seats = $this->busModel->getBusSeats($trip['bus_id']);

        $booked_seats = $this->tripModel->getTripBookedSeats($trip['bus_id'], $trip['estimated_departure_time'], $trip['estimated_arrival_time']);

        $booked_seats = array_flip(array_column($booked_seats, 'seat_id'));

        $pricing = $this->tripModel->getTripPricing($trip['route_id'], $trip['bus_id']);

        foreach ($seats as &$seat) {
            $seat['is_available'] = !isset($booked_seats[$seat['seat_id']]);
            $seat['price'] = $pricing[$seat['seat_type']] ?? null;
        }

        return $seats;
    }

    private function getAvailableSeatsCount($seats)
    {
        $available = ['standard' => 0, 'premium' => 0];
        foreach ($seats as $seat) {
            if ($seat['is_available']) {
                $available[$seat['seat_type']]++;
            }
        }
        return $available;
    }

    // --------------------------
    // Fonctions auxiliaires 
    // --------------------------

    /**
     * Construire le tableau de données structurées, détaillées et formatées d'un voyage à partir des données brutes
     * @param array $data Données brutes du voyage
     * @return array Tableau de données structurées et formatées
     */
    private function buildTripResults($data)
    {

        $pricing = $this->tripModel->getTripPricing($data['route_id'], $data['bus_id']);
        $amenities = $this->busModel->getBusAmenities($data['bus_id']);
        $seats = $this->seatsAvailability($data);
        $available_seats_count = $this->getAvailableSeatsCount($seats);

        return [
            'trip_id' => $data['trip_id'],
            'route_id' => $data['route_id'],
            'bus_id' => $data['bus_id'],
            'route_name' => $data['route_name'],
            'distance' => $data['distance'],
            'duration' => $data['duration'],
            'departure_time' => $data['estimated_departure_time'],
            'arrival_time' => $data['estimated_arrival_time'],
            'departure_location' => $data['departure_location_name'],
            'arrival_location' => $data['arrival_location_name'],
            'bus_brand' => $data['brand'],
            'bus_model' => $data['model'],
            'bus_registration_number' => $data['registration_number'],
            'bus_type' => $data['bus_type'],
            'layout_details' => json_decode($data['layout_details'], true),
            'bus_amenities' => $amenities,
            'available_seats_count' => $available_seats_count,
            'pricing' => $pricing,
        ];
    }

    /**
     * Construire le tableau de données structurées, détaillées et formatées d'un voyage à partir des données brutes
     * @param array $data Données brutes du voyage
     * @return array Tableau de données structurées et formatées
     **/
    private function buildTripDetails($data)
    {

        $pricing = $this->tripModel->getTripPricing($data['route_id'], $data['bus_id']);
        $stops = $this->tripStops($data['trip_id']);
        $amenities = $this->busModel->getBusAmenities($data['bus_id']);
        $seats = $this->seatsAvailability($data);
        $available_seats_count = $this->getAvailableSeatsCount($seats);

        $optionalFields = [
            'delay_reason' => $data['delay_reason'],
            'cancellation_reason' => $data['cancellation_reason'],
            'actual_departure_time' => $data['actual_departure_time'],
            'actual_arrival_time' => $data['actual_arrival_time'],
        ];

        $optionalFields = array_filter($optionalFields, function ($value) {
            return !empty($value);
        });

        return [
            'trip' => array_merge([
                'trip_id' => $data['trip_id'],
                'estimated_departure_time' => $data['estimated_departure_time'],
                'estimated_arrival_time' => $data['estimated_arrival_time'],
                'tracking_link' => $data['tracking_link'],
                'status' => $data['status'],
                'pricing' => $pricing,
            ], $optionalFields),
            'route' => [
                'route_id' => $data['route_id'],
                'route_name' => $data['route_name'],
                'description' => $data['description'],
                'distance' => $data['distance'],
                'duration' => $data['duration'],
                'departure_location' => [
                    'name' => $data['departure_location_name'],
                    'region' => $data['departure_location_region'],
                    'country' => $data['departure_location_country'],
                    'time_zone' => $data['departure_location_time_zone'],
                    'longitude' => $data['departure_location_longitude'],
                    'latitude' => $data['departure_location_latitude']
                ],
                'arrival_location' => [
                    'name' => $data['arrival_location_name'],
                    'region' => $data['arrival_location_region'],
                    'country' => $data['arrival_location_country'],
                    'time_zone' => $data['arrival_location_time_zone'],
                    'longitude' => $data['arrival_location_longitude'],
                    'latitude' => $data['arrival_location_latitude']
                ],
                'stops' => $stops,
            ],
            'bus' => [
                'bus_id' => $data['bus_id'],
                'registration_number' => $data['registration_number'],
                'brand' => $data['brand'],
                'model' => $data['model'],
                'capacity' => $data['capacity'],
                'bus_type' => $data['bus_type'],
                'year_manufactured' => $data['year_manufactured'],
                'bus_photos' => json_decode($data['bus_photos'], true),
                'amenities' => $amenities,
                'layout_details' => json_decode($data['layout_details'], true),
                'available_seats_count' => $available_seats_count,
            ]
        ];
    }
}
