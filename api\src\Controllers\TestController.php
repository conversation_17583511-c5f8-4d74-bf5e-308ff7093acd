<?php

require_once __DIR__ . '/../Helpers/response.php';

class TestController {
    
    public function test($params = []) {
        $response = [
            'message' => 'API EasyBus fonctionne correctement !',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0.0',
            'environment' => $_ENV['APP_ENV'] ?? 'development',
            'params_received' => $params,
            'request_method' => $_SERVER['REQUEST_METHOD'],
            'request_uri' => $_SERVER['REQUEST_URI']
        ];
        
        sendResponse(200, $response);
    }
    
    public function health($params = []) {
        // Vérifier la connexion à la base de données
        $dbStatus = $this->checkDatabaseConnection();
        
        // Vérifier les services externes
        $servicesStatus = $this->checkExternalServices();
        
        $overallStatus = $dbStatus['status'] === 'ok' && $servicesStatus['status'] === 'ok' ? 'healthy' : 'unhealthy';
        
        $response = [
            'status' => $overallStatus,
            'timestamp' => date('Y-m-d H:i:s'),
            'checks' => [
                'database' => $dbStatus,
                'services' => $servicesStatus
            ],
            'uptime' => $this->getUptime(),
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true)
            ]
        ];
        
        $httpCode = $overallStatus === 'healthy' ? 200 : 503;
        sendResponse($httpCode, $response);
    }
    
    private function checkDatabaseConnection() {
        try {
            require_once __DIR__ . '/../config/database.php';
            
            $pdo = new PDO(
                "mysql:host=" . $_ENV['DB_HOST'] . ";dbname=" . $_ENV['DB_NAME'],
                $_ENV['DB_USER'],
                $_ENV['DB_PASS'],
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            
            // Test simple de requête
            $stmt = $pdo->query("SELECT 1");
            $result = $stmt->fetch();
            
            return [
                'status' => 'ok',
                'message' => 'Connexion à la base de données réussie',
                'response_time' => microtime(true)
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Erreur de connexion à la base de données',
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function checkExternalServices() {
        $services = [];
        
        // Vérifier FedaPay (si configuré)
        if (!empty($_ENV['FEDAPAY_PUBLIC_KEY'])) {
            $services['fedapay'] = [
                'status' => 'configured',
                'message' => 'FedaPay configuré'
            ];
        } else {
            $services['fedapay'] = [
                'status' => 'not_configured',
                'message' => 'FedaPay non configuré'
            ];
        }
        
        // Vérifier SMTP (si configuré)
        if (!empty($_ENV['SMTP_HOST'])) {
            $services['smtp'] = [
                'status' => 'configured',
                'message' => 'SMTP configuré'
            ];
        } else {
            $services['smtp'] = [
                'status' => 'not_configured',
                'message' => 'SMTP non configuré'
            ];
        }
        
        $overallStatus = 'ok';
        foreach ($services as $service) {
            if ($service['status'] === 'error') {
                $overallStatus = 'error';
                break;
            }
        }
        
        return [
            'status' => $overallStatus,
            'services' => $services
        ];
    }
    
    private function getUptime() {
        // Approximation simple de l'uptime
        $loadavg = sys_getloadavg();
        return [
            'load_average' => $loadavg,
            'server_time' => date('Y-m-d H:i:s')
        ];
    }
}
