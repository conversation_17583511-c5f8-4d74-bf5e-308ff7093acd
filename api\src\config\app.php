<?php
require_once __DIR__ . '/../../vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../../');
$dotenv->load();

// Récupérer les variables d’environnement
$config = [
    'db' => [
        'host' => $_ENV['DB_HOST'],
        'name' => $_ENV['DB_NAME'],
        'user' => $_ENV['DB_USER'],
        'pass' => $_ENV['DB_PASS'],
    ],
    'jwt' => [
        'secret' => $_ENV['JWT_SECRET'],
        'algo' => $_ENV['JWT_ALGORITHM'],
    ],
    'api_version' => $_ENV['API_VERSION'],
];

return $config;