<?php
use <PERSON><PERSON><PERSON><PERSON>er\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../config/app.php';

class EmailService {
    private $mailer;
    private $config;
    
    public function __construct() {
        $this->config = require __DIR__ . '/../config/app.php';
        $this->setupMailer();
    }
    
    private function setupMailer() {
        $this->mailer = new PHPMailer(true);
        
        try {
            // Configuration SMTP
            $this->mailer->isSMTP();
            $this->mailer->Host = $_ENV['SMTP_HOST'] ?? 'smtp.gmail.com';
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = $_ENV['SMTP_USERNAME'] ?? '';
            $this->mailer->Password = $_ENV['SMTP_PASSWORD'] ?? '';
            $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $this->mailer->Port = $_ENV['SMTP_PORT'] ?? 587;
            
            // Configuration de l'expéditeur
            $this->mailer->setFrom(
                $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
                $_ENV['MAIL_FROM_NAME'] ?? 'EasyBus'
            );
            
            // Configuration de l'encodage
            $this->mailer->CharSet = 'UTF-8';
            $this->mailer->isHTML(true);
            
        } catch (Exception $e) {
            error_log('Erreur configuration PHPMailer: ' . $e->getMessage());
        }
    }
    
    /**
     * Envoyer un email avec template
     */
    public function sendEmail($to, $subject, $template, $data = []) {
        try {
            // Réinitialiser les destinataires
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();
            
            // Ajouter le destinataire
            $this->mailer->addAddress($to);
            
            // Définir le sujet
            $this->mailer->Subject = $subject;
            
            // Générer le contenu HTML
            $htmlContent = $this->renderTemplate($template, $data);
            $this->mailer->Body = $htmlContent;
            
            // Générer le contenu texte alternatif
            $this->mailer->AltBody = strip_tags($htmlContent);
            
            // Envoyer l'email
            return $this->mailer->send();
            
        } catch (Exception $e) {
            error_log('Erreur envoi email: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Rendre un template d'email
     */
    private function renderTemplate($template, $data) {
        $templatePath = __DIR__ . "/../Templates/emails/{$template}.php";
        
        if (!file_exists($templatePath)) {
            throw new Exception("Template email non trouvé: {$template}");
        }
        
        // Extraire les variables pour le template
        extract($data);
        
        // Capturer le contenu du template
        ob_start();
        include $templatePath;
        $content = ob_get_clean();
        
        // Envelopper dans le layout principal
        return $this->wrapInLayout($content, $data);
    }
    
    /**
     * Envelopper le contenu dans le layout principal
     */
    private function wrapInLayout($content, $data) {
        $layoutPath = __DIR__ . '/../Templates/emails/layout.php';
        
        if (file_exists($layoutPath)) {
            extract($data);
            $emailContent = $content;
            
            ob_start();
            include $layoutPath;
            return ob_get_clean();
        }
        
        // Layout par défaut si le fichier n'existe pas
        return $this->getDefaultLayout($content);
    }
    
    /**
     * Layout par défaut
     */
    private function getDefaultLayout($content) {
        return "
        <!DOCTYPE html>
        <html lang='fr'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>EasyBus</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #0d6efd; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f8f9fa; }
                .footer { background-color: #212529; color: white; padding: 15px; text-align: center; font-size: 12px; }
                .btn { display: inline-block; padding: 10px 20px; background-color: #0d6efd; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>EasyBus</h1>
                    <p>Votre partenaire de voyage en Afrique de l'Ouest</p>
                </div>
                <div class='content'>
                    {$content}
                </div>
                <div class='footer'>
                    <p>&copy; " . date('Y') . " EasyBus. Tous droits réservés.</p>
                    <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
                </div>
            </div>
        </body>
        </html>";
    }
    
    /**
     * Envoyer un email simple (sans template)
     */
    public function sendSimpleEmail($to, $subject, $message) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($to);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $this->getDefaultLayout($message);
            $this->mailer->AltBody = strip_tags($message);
            
            return $this->mailer->send();
            
        } catch (Exception $e) {
            error_log('Erreur envoi email simple: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Envoyer un email avec pièce jointe
     */
    public function sendEmailWithAttachment($to, $subject, $template, $data, $attachments = []) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();
            
            $this->mailer->addAddress($to);
            $this->mailer->Subject = $subject;
            
            $htmlContent = $this->renderTemplate($template, $data);
            $this->mailer->Body = $htmlContent;
            $this->mailer->AltBody = strip_tags($htmlContent);
            
            // Ajouter les pièces jointes
            foreach ($attachments as $attachment) {
                if (isset($attachment['path']) && file_exists($attachment['path'])) {
                    $this->mailer->addAttachment(
                        $attachment['path'],
                        $attachment['name'] ?? basename($attachment['path'])
                    );
                }
            }
            
            return $this->mailer->send();
            
        } catch (Exception $e) {
            error_log('Erreur envoi email avec pièce jointe: ' . $e->getMessage());
            return false;
        }
    }
}
