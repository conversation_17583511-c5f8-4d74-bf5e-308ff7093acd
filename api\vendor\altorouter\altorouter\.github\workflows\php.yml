name: PHP

on: [ push, pull_request ]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php-versions: [ '7.3', 'highest' ]

    steps:
      - uses: actions/checkout@v2

      - uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          tools: composer

      - name: Validate composer.json and composer.lock
        run: composer validate

      - name: Get composer cache directory
        id: composer-cache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Cache dependencies
        uses: actions/cache@v2
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        if: steps.composer-cache.outputs.cache-hit != 'true'
        run: composer install --no-progress

      - name: Run test suite
        run: composer run-script test
