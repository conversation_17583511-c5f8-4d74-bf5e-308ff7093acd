// Configuration de l'API
const API_BASE_URL = 'api/v1';

// Variables globales
let bookingData = {};
let ticketsData = [];

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    initializeConfirmation();
});

// Initialiser la page de confirmation
async function initializeConfirmation() {
    try {
        // Récupérer les paramètres de l'URL
        const urlParams = new URLSearchParams(window.location.search);
        const bookingId = urlParams.get('booking');
        const email = urlParams.get('email');
        const phone = urlParams.get('phone');

        if (!bookingId) {
            showAlert('Numéro de réservation manquant', 'danger');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 3000);
            return;
        }

        // Charger les détails de la réservation
        await loadBookingDetails(bookingId, email, phone);
        
    } catch (error) {
        console.error('Erreur lors de l\'initialisation:', error);
        showAlert('Erreur lors du chargement des détails', 'danger');
    }
}

// Charger les détails de la réservation
async function loadBookingDetails(bookingId, email, phone) {
    try {
        let response;
        
        // Si nous avons l'email ou le téléphone, utiliser la recherche publique
        if (email || phone) {
            const searchParams = email ? { email } : { phone };
            response = await apiRequest('bookings/search?' + new URLSearchParams(searchParams));
            
            // Trouver la réservation spécifique
            const booking = response.bookings?.find(b => b.booking_id == bookingId);
            if (!booking) {
                throw new Error('Réservation non trouvée');
            }
            bookingData = booking;
        } else {
            // Utiliser l'API authentifiée
            response = await apiRequest(`bookings/${bookingId}`);
            bookingData = response.booking;
        }

        // Charger les tickets
        await loadTickets(bookingId);
        
        // Afficher les détails
        displayBookingDetails();
        displayTickets();
        
    } catch (error) {
        console.error('Erreur lors du chargement:', error);
        showAlert('Impossible de charger les détails de la réservation', 'danger');
    }
}

// Charger les tickets
async function loadTickets(bookingId) {
    try {
        const response = await apiRequest(`bookings/${bookingId}/tickets`);
        ticketsData = response.tickets || [];
    } catch (error) {
        console.error('Erreur lors du chargement des tickets:', error);
        ticketsData = [];
    }
}

// Afficher les détails de la réservation
function displayBookingDetails() {
    // Informations de base
    document.getElementById('bookingId').textContent = `#${bookingData.booking_id}`;
    document.getElementById('bookingDate').textContent = formatDate(bookingData.created_at || new Date());
    document.getElementById('contactEmail').textContent = bookingData.contact_email || 'N/A';
    document.getElementById('contactPhone').textContent = bookingData.contact_phone || 'N/A';

    // Détails du voyage
    const tripDetailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <p><strong>Trajet:</strong> ${bookingData.route_name || 'N/A'}</p>
                <p><strong>Départ:</strong> ${bookingData.departure_location || 'N/A'}</p>
                <p><strong>Destination:</strong> ${bookingData.destination_location || 'N/A'}</p>
            </div>
            <div class="col-md-6">
                <p><strong>Date de départ:</strong> ${formatDateTime(bookingData.estimated_departure_time)}</p>
                <p><strong>Heure d'arrivée:</strong> ${formatDateTime(bookingData.estimated_arrival_time)}</p>
                <p><strong>Bus:</strong> ${bookingData.bus_registration || 'N/A'}</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <p><strong>Nombre de passagers:</strong> ${ticketsData.length}</p>
                <p><strong>Montant total:</strong> <span class="text-success fw-bold">${formatCurrency(bookingData.total_amount)}</span></p>
            </div>
            <div class="col-md-6">
                <p><strong>Statut:</strong> <span class="badge bg-success">Confirmé</span></p>
                <p><strong>Mode de paiement:</strong> ${getPaymentMethodText(bookingData.payment_method)}</p>
            </div>
        </div>
    `;
    
    document.getElementById('tripDetails').innerHTML = tripDetailsHtml;
}

// Afficher les tickets
function displayTickets() {
    const container = document.getElementById('ticketsContainer');
    
    if (!ticketsData.length) {
        container.innerHTML = `
            <div class="text-center py-3">
                <i class="fas fa-exclamation-triangle text-warning"></i>
                <p class="text-muted mb-0">Aucun ticket trouvé</p>
            </div>
        `;
        return;
    }

    let html = '';
    
    ticketsData.forEach((ticket, index) => {
        html += `
            <div class="ticket-item">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-2">
                            <i class="fas fa-ticket-alt me-2 text-primary"></i>
                            Ticket ${index + 1} - ${ticket.passenger_name}
                        </h6>
                        <div class="row">
                            <div class="col-sm-6">
                                <small class="text-muted">Siège:</small> <strong>${ticket.seat_number}</strong><br>
                                <small class="text-muted">Code:</small> <strong class="text-primary">${ticket.ticket_code}</strong>
                            </div>
                            <div class="col-sm-6">
                                <small class="text-muted">Statut:</small> 
                                <span class="badge bg-${ticket.status === 'valid' ? 'success' : 'warning'}">${getTicketStatusText(ticket.status)}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="qr-code">
                            <div id="qr-${ticket.ticket_id}" class="mb-2"></div>
                            <small class="text-muted">Code QR</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
    
    // Générer les codes QR (simulation)
    ticketsData.forEach(ticket => {
        generateQRCode(ticket.ticket_code, `qr-${ticket.ticket_id}`);
    });
}

// Générer un code QR (simulation)
function generateQRCode(text, containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        // Simulation d'un QR code avec du texte
        container.innerHTML = `
            <div style="width: 80px; height: 80px; background: #f8f9fa; border: 1px solid #dee2e6; 
                        display: flex; align-items: center; justify-content: center; margin: 0 auto; font-size: 10px;">
                QR<br>${text.slice(-4)}
            </div>
        `;
    }
}

// Télécharger les tickets
function downloadTickets() {
    showAlert('Téléchargement en cours de développement', 'info');
}

// Envoyer les tickets par email
async function sendTicketsByEmail() {
    try {
        showAlert('Envoi des tickets par email...', 'info');
        
        // Simulation de l'envoi d'email
        setTimeout(() => {
            showAlert('Tickets envoyés par email avec succès !', 'success');
        }, 2000);
        
    } catch (error) {
        console.error('Erreur lors de l\'envoi:', error);
        showAlert('Erreur lors de l\'envoi des tickets', 'danger');
    }
}

// Imprimer les tickets
function printTickets() {
    window.print();
}

// Fonctions utilitaires
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 0
    }).format(amount) + ' FCFA';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

function formatDateTime(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getPaymentMethodText(method) {
    const methods = {
        'fedapay': 'Carte bancaire',
        'mobile': 'Mobile Money',
        'cash': 'Espèces'
    };
    return methods[method] || method || 'N/A';
}

function getTicketStatusText(status) {
    const statuses = {
        'valid': 'Valide',
        'used': 'Utilisé',
        'cancelled': 'Annulé'
    };
    return statuses[status] || status || 'Inconnu';
}

// Fonction pour faire des requêtes API
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}/${endpoint}`;
    const token = getAuthToken ? getAuthToken() : null;
    
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            ...(token && { 'Authorization': `Bearer ${token}` })
        }
    };
    
    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erreur de requête');
        }
        
        return data;
    } catch (error) {
        console.error('Erreur API:', error);
        throw error;
    }
}

// Fonction pour afficher les alertes
function showAlert(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" id="${alertId}">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHTML);
    
    // Auto-suppression après 5 secondes
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
