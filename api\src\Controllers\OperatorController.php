<?php
require_once __DIR__ . '/../Models/BookingModel.php';
require_once __DIR__ . '/../Models/TripModel.php';
require_once __DIR__ . '/../Models/BusModel.php';
require_once __DIR__ . '/../Models/UserModel.php';
require_once __DIR__ . '/../Models/PaymentModel.php';
require_once __DIR__ . '/../Models/LocationModel.php';
require_once __DIR__ . '/../Models/RouteModel.php';
require_once __DIR__ . '/../Models/AmenityModel.php';
require_once __DIR__ . '/../Helpers/response.php';
require_once __DIR__ . '/../Helpers/validate.php';
require_once __DIR__ . '/../Middlewares/AuthMiddleware.php';

class OperatorController {
    private $bookingModel;
    private $tripModel;
    private $busModel;
    private $userModel;
    private $paymentModel;
    private $locationModel;
    private $routeModel;
    private $amenityModel;

    public function __construct() {
        $this->bookingModel = new BookingModel();
        $this->tripModel = new TripModel();
        $this->busModel = new BusModel();
        $this->userModel = new UserModel();
        $this->paymentModel = new PaymentModel();
        $this->locationModel = new LocationModel();
        $this->routeModel = new RouteModel();
        $this->amenityModel = new AmenityModel();
    }
    
    /**
     * Tableau de bord principal de l'opérateur
     * GET /v1/operator/dashboard
     */
    public function getDashboard($params = []) {
        try {
            // Statistiques générales
            $stats = [
                'bookings' => $this->getBookingStats(),
                'trips' => $this->getTripStats(),
                'revenue' => $this->getRevenueStats(),
                'buses' => $this->getBusStats(),
                'users' => $this->userModel->getUserStats()
            ];
            
            // Réservations récentes
            $recentBookings = $this->getRecentBookings(10);
            
            // Voyages du jour
            $todayTrips = $this->getTodayTrips();
            
            // Alertes et notifications
            $alerts = $this->getSystemAlerts();
            
            sendResponse(200, [
                'stats' => $stats,
                'recent_bookings' => $recentBookings,
                'today_trips' => $todayTrips,
                'alerts' => $alerts
            ]);
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * Gestion des réservations
     * GET /v1/operator/bookings
     */
    public function getBookings($params = []) {
        try {
            $page = $params['page'] ?? $_GET['page'] ?? 1;
            $limit = $params['limit'] ?? $_GET['limit'] ?? 20;
            $status = $params['status'] ?? $_GET['status'] ?? '';
            $search = $params['search'] ?? $_GET['search'] ?? '';
            
            $bookings = $this->bookingModel->getBookingsForOperator($page, $limit, $status, $search);
            $total = $this->bookingModel->getBookingsCountForOperator($status, $search);
            
            sendResponse(200, [
                'bookings' => $bookings,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * Rapports et analyses
     * GET /v1/operator/reports
     */
    public function getReports($params = []) {
        try {
            $type = $params['type'] ?? $_GET['type'] ?? 'revenue';
            $period = $params['period'] ?? $_GET['period'] ?? 'month';
            $startDate = $params['start_date'] ?? $_GET['start_date'] ?? '';
            $endDate = $params['end_date'] ?? $_GET['end_date'] ?? '';
            
            switch ($type) {
                case 'revenue':
                    $data = $this->getRevenueReport($period, $startDate, $endDate);
                    break;
                case 'bookings':
                    $data = $this->getBookingsReport($period, $startDate, $endDate);
                    break;
                case 'routes':
                    $data = $this->getRoutesReport($period, $startDate, $endDate);
                    break;
                case 'occupancy':
                    $data = $this->getOccupancyReport($period, $startDate, $endDate);
                    break;
                default:
                    sendResponse(400, ['message' => 'Type de rapport non valide']);
                    return;
            }
            
            sendResponse(200, [
                'type' => $type,
                'period' => $period,
                'data' => $data
            ]);
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
    
    /**
     * Analyses et métriques
     * GET /v1/operator/analytics
     */
    public function getAnalytics($params = []) {
        try {
            $analytics = [
                'revenue_trends' => $this->getRevenueTrends(),
                'popular_routes' => $this->getPopularRoutes(),
                'peak_times' => $this->getPeakTimes(),
                'customer_segments' => $this->getCustomerSegments(),
                'bus_performance' => $this->getBusPerformance()
            ];
            
            sendResponse(200, ['analytics' => $analytics]);
            
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
    
    // Méthodes privées pour les statistiques
    
    private function getBookingStats() {
        return [
            'total' => $this->bookingModel->getTotalBookings(),
            'today' => $this->bookingModel->getTodayBookings(),
            'pending' => $this->bookingModel->getPendingBookings(),
            'confirmed' => $this->bookingModel->getConfirmedBookings(),
            'cancelled' => $this->bookingModel->getCancelledBookings()
        ];
    }
    
    private function getTripStats() {
        return [
            'total' => $this->tripModel->getTotalTrips(),
            'today' => $this->tripModel->getTodayTrips(),
            'ongoing' => $this->tripModel->getOngoingTrips(),
            'completed' => $this->tripModel->getCompletedTrips(),
            'cancelled' => $this->tripModel->getCancelledTrips()
        ];
    }
    
    private function getRevenueStats() {
        return [
            'total' => $this->paymentModel->getTotalRevenue(),
            'today' => $this->paymentModel->getTodayRevenue(),
            'this_month' => $this->paymentModel->getMonthRevenue(),
            'last_month' => $this->paymentModel->getLastMonthRevenue()
        ];
    }
    
    private function getBusStats() {
        return [
            'total' => $this->busModel->getTotalBuses(),
            'active' => $this->busModel->getActiveBuses(),
            'maintenance' => $this->busModel->getBusesInMaintenance(),
            'utilization_rate' => $this->busModel->getBusUtilizationRate()
        ];
    }
    
    private function getRecentBookings($limit = 10) {
        return $this->bookingModel->getRecentBookingsForOperator($limit);
    }
    
    private function getTodayTrips() {
        return $this->tripModel->getTodayTripsForOperator();
    }
    
    private function getSystemAlerts() {
        $alerts = [];
        
        // Vérifier les bus en maintenance
        $maintenanceBuses = $this->busModel->getBusesInMaintenance();
        if (count($maintenanceBuses) > 0) {
            $alerts[] = [
                'type' => 'warning',
                'message' => count($maintenanceBuses) . ' bus(es) en maintenance',
                'action' => 'Vérifier les bus en maintenance'
            ];
        }
        
        // Vérifier les voyages en retard
        $delayedTrips = $this->tripModel->getDelayedTrips();
        if (count($delayedTrips) > 0) {
            $alerts[] = [
                'type' => 'danger',
                'message' => count($delayedTrips) . ' voyage(s) en retard',
                'action' => 'Gérer les retards'
            ];
        }
        
        // Vérifier les paiements en attente
        $pendingPayments = $this->paymentModel->getPendingPayments();
        if (count($pendingPayments) > 0) {
            $alerts[] = [
                'type' => 'info',
                'message' => count($pendingPayments) . ' paiement(s) en attente',
                'action' => 'Vérifier les paiements'
            ];
        }
        
        return $alerts;
    }
    
    private function getRevenueTrends() {
        return $this->paymentModel->getRevenueTrends(30); // 30 derniers jours
    }
    
    private function getPopularRoutes() {
        return $this->bookingModel->getPopularRoutes(10);
    }
    
    private function getPeakTimes() {
        return $this->bookingModel->getPeakBookingTimes();
    }
    
    private function getCustomerSegments() {
        return $this->userModel->getCustomerSegments();
    }
    
    private function getBusPerformance() {
        return $this->busModel->getBusPerformanceMetrics();
    }
    
    private function getRevenueReport($period, $startDate, $endDate) {
        return $this->paymentModel->getRevenueReport($period, $startDate, $endDate);
    }
    
    private function getBookingsReport($period, $startDate, $endDate) {
        return $this->bookingModel->getBookingsReport($period, $startDate, $endDate);
    }
    
    private function getRoutesReport($period, $startDate, $endDate) {
        return $this->tripModel->getRoutesReport($period, $startDate, $endDate);
    }
    
    private function getOccupancyReport($period, $startDate, $endDate) {
        return $this->tripModel->getOccupancyReport($period, $startDate, $endDate);
    }

    // ==================== MÉTHODES CRUD POUR L'OPÉRATEUR ====================

    /**
     * CRUD Lieux - Récupérer tous les lieux
     * GET /v1/operator/locations
     */
    public function getLocations($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $locations = $this->locationModel->getAllLocations();
            sendResponse(200, ['locations' => $locations]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Lieux - Créer un nouveau lieu
     * POST /v1/operator/locations
     */
    public function createLocation($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $data = json_decode(file_get_contents('php://input'), true) ?? $params;

            validateFields($data, [
                'location_name' => 'required',
                'region' => 'required',
                'country' => 'required',
                'time_zone' => 'required',
                'latitude' => 'required|numeric',
                'longitude' => 'required|numeric',
            ]);

            // Exemple d'utilisation de l'id opérateur pour created_by
            $data['created_by'] = $user->sub ?? null;

            $locationId = $this->locationModel->createLocation($data);

            sendResponse(201, [
                'location_id' => $locationId
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Lieux - Mettre à jour un lieu
     * PUT /v1/operator/locations/{id}
     */
    public function updateLocation($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $locationId = $params['id'] ?? 0;
            if (!$locationId) {
                sendResponse(400, ['message' => 'ID lieu requis']);
                return;
            }

            $data = json_decode(file_get_contents('php://input'), true);
            $data['updated_by'] = $user->sub ?? null;

            $success = $this->locationModel->updateLocation($locationId, $data);

            if ($success) {
                sendResponse(200, ['message' => 'Lieu mis à jour avec succès']);
            } else {
                sendResponse(404, ['message' => 'Lieu non trouvé']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Lieux - Supprimer un lieu
     * DELETE /v1/operator/locations/{id}
     */
    public function deleteLocation($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $locationId = $params['id'] ?? 0;
            if (!$locationId) {
                sendResponse(400, ['message' => 'ID lieu requis']);
                return;
            }

            $success = $this->locationModel->deleteLocation($locationId);

            if ($success) {
                sendResponse(200, ['message' => 'Lieu supprimé avec succès']);
            } else {
                sendResponse(404, ['message' => 'Lieu non trouvé']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Arrêts - Récupérer tous les arrêts
     * GET /v1/operator/stops
     */
    public function getStops($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            // Utiliser la méthode existante du LocationController pour les arrêts
            $stops = $this->locationModel->getAllStops();
            sendResponse(200, ['stops' => $stops]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Arrêts - Créer un nouvel arrêt
     * POST /v1/operator/stops
     */
    public function createStop($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $data = json_decode(file_get_contents('php://input'), true) ?? $params;

            validateFields($data, [
                'location_id' => 'required|positiveInt'
            ]);

            $data['created_by'] = $user->sub ?? null;

            $stopId = $this->locationModel->createStop($data);

            sendResponse(201, [
                'stop_id' => $stopId
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Arrêts - Mettre à jour un arrêt
     * PUT /v1/operator/stops/{id}
     */
    public function updateStop($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $stopId = $params['id'] ?? 0;
            if (!$stopId) {
                sendResponse(400, ['message' => 'ID arrêt requis']);
                return;
            }

            $data = json_decode(file_get_contents('php://input'), true);
            $data['updated_by'] = $user->sub ?? null;

            $success = $this->locationModel->updateStop($stopId, $data);

            if ($success) {
                sendResponse(200, ['message' => 'Arrêt mis à jour avec succès']);
            } else {
                sendResponse(404, ['message' => 'Arrêt non trouvé']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Arrêts - Supprimer un arrêt
     * DELETE /v1/operator/stops/{id}
     */
    public function deleteStop($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $stopId = $params['id'] ?? 0;
            if (!$stopId) {
                sendResponse(400, ['message' => 'ID arrêt requis']);
                return;
            }

            $success = $this->locationModel->deleteStop($stopId);

            if ($success) {
                sendResponse(200, ['message' => 'Arrêt supprimé avec succès']);
            } else {
                sendResponse(404, ['message' => 'Arrêt non trouvé']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Bus - Récupérer tous les bus
     * GET /v1/operator/buses
     */
    public function getBuses($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $buses = $this->busModel->getBuses();
            sendResponse(200, ['buses' => $buses]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Bus - Créer un nouveau bus
     * POST /v1/operator/buses
     */
    public function createBus($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $data = json_decode(file_get_contents('php://input'), true) ?? $params;

            validateFields($data, [
                'bus_type' => 'required'
            ]);

            $data['created_by'] = $user->sub ?? null;

            $busId = $this->busModel->createBus($data);

            sendResponse(201, [
                'bus_id' => $busId
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Bus - Mettre à jour un bus
     * PUT /v1/operator/buses/{id}
     */
    public function updateBus($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $busId = $params['id'] ?? 0;
            if (!$busId) {
                sendResponse(400, ['message' => 'ID bus requis']);
                return;
            }

            $success = $this->busModel->updateBus($busId, $params);

            if ($success) {
                sendResponse(200, ['message' => 'Bus mis à jour avec succès']);
            } else {
                sendResponse(404, ['message' => 'Bus non trouvé']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Bus - Supprimer un bus
     * DELETE /v1/operator/buses/{id}
     */
    public function deleteBus($params = []) {
        try {

            $busId = $params['id'] ?? 0;
            if (!$busId) {
                sendResponse(400, ['message' => 'ID bus requis']);
                return;
            }

            $success = $this->busModel->deleteBus($busId);

            if ($success) {
                sendResponse(200, ['message' => 'Bus supprimé avec succès']);
            } else {
                sendResponse(404, ['message' => 'Bus non trouvé']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Sièges - Récupérer les sièges d'un bus
     * GET /v1/operator/buses/{bus_id}/seats
     */
    public function getBusSeats($params = []) {
        try {

            $busId = $params['bus_id'] ?? 0;
            if (!$busId) {
                sendResponse(400, ['message' => 'ID bus requis']);
                return;
            }

            $seats = $this->busModel->getBusSeats($busId);
            sendResponse(200, ['seats' => $seats]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Sièges - Créer un nouveau siège
     * POST /v1/operator/buses/{bus_id}/seats
     */
    public function createSeat($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $userId = $user->sub ?? null;

            $busId = $params['bus_id'] ?? 0;
            if (!$busId) {
                sendResponse(400, ['message' => 'ID bus requis']);
                return;
            }

            // Le tableau 'seats' est attendu dans le corps JSON de la requête
            $seatsDataArray = $params['seats'] ?? null;

            if (!is_array($seatsDataArray) || empty($seatsDataArray)) {
                sendResponse(400, ['message' => 'Le champ "seats" doit être un tableau non vide contenant les données des sièges.']);
                return;
            }

            $processedSeats = [];
            foreach ($seatsDataArray as $seatData) {
                // Valider chaque siège. Assurez-vous que 'price' est inclus si nécessaire.
                // L'exemple de payload n'inclut pas 'price', mais la validation originale l'exigeait.
                validateFields($seatData, [
                    'seat_number' => 'required',
                    'seat_type' => 'required'
                ]);

                $processedSeats[] = [
                    'bus_id' => (int)$busId,
                    'seat_number' => $seatData['seat_number'],
                    'seat_type' => $seatData['seat_type'],
                    'status' => $seatData['status'] ?? 'active',
                    'created_by' => $userId
                ];
            }

            $createdSeatIds = $this->busModel->createMultipleSeats($processedSeats);

            sendResponse(201, [
                'message' => count($createdSeatIds) . ' siège(s) créé(s) avec succès.',
                'seat_ids' => $createdSeatIds
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Sièges - Mettre à jour un siège
     * PUT /v1/operator/seats/{id}
     */
    public function updateSeat($params = []) {
        try {
            AuthMiddleware::authenticate('operator');

            $seatId = $params['id'] ?? 0;
            if (!$seatId) {
                sendResponse(400, ['message' => 'ID siège requis']);
                return;
            }

            $data = json_decode(file_get_contents('php://input'), true);

            $success = $this->busModel->updateSeat($seatId, $data);

            if ($success) {
                sendResponse(200, ['message' => 'Siège mis à jour avec succès']);
            } else {
                sendResponse(404, ['message' => 'Siège non trouvé']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Sièges - Supprimer un siège
     * DELETE /v1/operator/seats/{id}
     */
    public function deleteSeat($params = []) {
        try {
            AuthMiddleware::authenticate('operator');

            $seatId = $params['id'] ?? 0;
            if (!$seatId) {
                sendResponse(400, ['message' => 'ID siège requis']);
                return;
            }

            $success = $this->busModel->deleteSeat($seatId);

            if ($success) {
                sendResponse(200, ['message' => 'Siège supprimé avec succès']);
            } else {
                sendResponse(404, ['message' => 'Siège non trouvé']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    // ==================== MÉTHODES CRUD POUR LES COMMODITÉS ====================

    /**
     * CRUD Commodités - Récupérer toutes les commodités
     * GET /v1/operator/amenities
     */
    public function getAmenities($params = []) {
        try {
            $page = $params['page'] ?? $_GET['page'] ?? 1;
            $limit = $params['limit'] ?? $_GET['limit'] ?? 50;
            $search = $params['search'] ?? $_GET['search'] ?? '';

            $filters = [];
            if ($search) {
                $filters['search'] = $search;
            }

            $offset = ($page - 1) * $limit;
            $amenities = $this->amenityModel->getAllAmenities($limit, $offset, $filters);
            $total = $this->amenityModel->countAmenities($filters);

            sendResponse(200, [
                'amenities' => $amenities,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Commodités - Créer une nouvelle commodité
     * POST /v1/operator/amenities
     */
    public function createAmenity($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            validateFields($params, [
                'amenity_name' => 'required'
            ]);

            // Vérifier si le nom existe déjà
            if ($this->amenityModel->amenityNameExists($params['amenity_name'])) {
                sendResponse(400, ['message' => 'Une commodité avec ce nom existe déjà']);
                return;
            }

            $params['created_by'] = $user->sub ?? null;
            $amenityId = $this->amenityModel->createAmenity($params);

            sendResponse(201, [
                'message' => 'Commodité créée avec succès',
                'amenity_id' => $amenityId
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Commodités - Mettre à jour une commodité
     * PUT /v1/operator/amenities/{id}
     */
    public function updateAmenity($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $amenityId = $params['id'] ?? 0;
            if (!$amenityId) {
                sendResponse(400, ['message' => 'ID commodité requis']);
                return;
            }

            validateFields($params, [
                'amenity_name' => 'required'
            ]);

            // Vérifier si le nom existe déjà (excluant l'ID actuel)
            if ($this->amenityModel->amenityNameExists($params['amenity_name'], $amenityId)) {
                sendResponse(400, ['message' => 'Une commodité avec ce nom existe déjà']);
                return;
            }

            $params['updated_by'] = $user->sub ?? null;
            $success = $this->amenityModel->updateAmenity($amenityId, $params);

            if ($success) {
                sendResponse(200, ['message' => 'Commodité mise à jour avec succès']);
            } else {
                sendResponse(404, ['message' => 'Commodité non trouvée']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Commodités - Supprimer une commodité
     * DELETE /v1/operator/amenities/{id}
     */
    public function deleteAmenity($params = []) {
        try {
            $amenityId = $params['id'] ?? 0;
            if (!$amenityId) {
                sendResponse(400, ['message' => 'ID commodité requis']);
                return;
            }

            $success = $this->amenityModel->deleteAmenity($amenityId);

            if ($success) {
                sendResponse(200, ['message' => 'Commodité supprimée avec succès']);
            } else {
                sendResponse(404, ['message' => 'Commodité non trouvée']);
            }

        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'utilisée par des bus') !== false) {
                sendResponse(400, ['message' => $e->getMessage()]);
            } else {
                sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
            }
        }
    }

    /**
     * Récupérer les commodités d'un bus spécifique
     * GET /v1/operator/buses/{id}/amenities
     */
    public function getBusAmenities($params = []) {
        try {
            $busId = $params['id'] ?? 0;
            if (!$busId) {
                sendResponse(400, ['message' => 'ID bus requis']);
                return;
            }

            $amenities = $this->amenityModel->getBusAmenities($busId);
            sendResponse(200, ['amenities' => $amenities]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Mettre à jour les commodités d'un bus
     * PUT /v1/operator/buses/{id}/amenities
     */
    public function updateBusAmenities($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $busId = $params['id'] ?? 0;
            if (!$busId) {
                sendResponse(400, ['message' => 'ID bus requis']);
                return;
            }

            $amenityIds = $params['amenity_ids'] ?? [];

            $success = $this->amenityModel->updateBusAmenities($busId, $amenityIds, $user->sub ?? null);

            if ($success) {
                sendResponse(200, ['message' => 'Commodités du bus mises à jour avec succès']);
            } else {
                sendResponse(400, ['message' => 'Erreur lors de la mise à jour']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Trajets (Routes) - Récupérer tous les trajets
     * GET /v1/operator/routes
     */
    public function getRoutes($params = []) {
        try {
            $page = $params['page'] ?? $_GET['page'] ?? 1;
            $limit = $params['limit'] ?? $_GET['limit'] ?? 20;
            $search = $params['search'] ?? $_GET['search'] ?? '';
            $offset = ($page - 1) * $limit;
            $filters = [];
            if ($search) {
                $filters['search'] = $search;
            }
            $routes = $this->routeModel->getAllRoutes($limit, $offset, $filters);
            $total = $this->routeModel->countRoutes($filters);
            sendResponse(200, [
                'routes' => $routes,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Trajets (Routes) - Créer un nouveau trajet
     * POST /v1/operator/routes
     */
    public function createRoute($params = []) {
        $user = AuthMiddleware::getUser();
        try {
            validateFields($params, [
                'route_name' => 'required',
                'departure_location_id' => 'required',
                'destination_location_id' => 'required',
                'distance' => 'required',
                'duration' => 'required'
            ]);
            if ($this->routeModel->routeNameExists($params['route_name'])) {
                sendResponse(400, ['message' => 'Un trajet avec ce nom existe déjà']);
                return;
            }
            $params['created_by'] = $user->sub ?? null;
            $routeId = $this->routeModel->createRoute($params);
            sendResponse(201, [
                'message' => 'Trajet créé avec succès',
                'route_id' => $routeId
            ]);
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Trajets (Routes) - Mettre à jour un trajet
     * PUT /v1/operator/routes/{id}
     */
    public function updateRoute($params = []) {
        try {
            $user = AuthMiddleware::getUser();
            $routeId = $params['id'] ?? 0;
            if (!$routeId) {
                sendResponse(400, ['message' => 'ID trajet requis']);
                return;
            }
            validateFields($params, [
                'route_name' => 'required',
                'distance' => 'required',
                'duration' => 'required'
            ]);
            if ($this->routeModel->routeNameExists($params['route_name']) && !$this->routeModel->isRouteNameUnique($params['route_name'], $routeId)) {
                sendResponse(400, ['message' => 'Un trajet avec ce nom existe déjà']);
                return;
            }
            $params['updated_by'] = $user->sub ?? null;
            $success = $this->routeModel->updateRoute($routeId, $params);
            if ($success) {
                sendResponse(200, ['message' => 'Trajet mis à jour avec succès']);
            } else {
                sendResponse(400, ['message' => 'Aucune donnée valide à mettre à jour']);
            }
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Trajets (Routes) - Supprimer un trajet
     * DELETE /v1/operator/routes/{id}
     */
    public function deleteRoute($params = []) {
        try {
            $routeId = $params['id'] ?? 0;
            if (!$routeId) {
                sendResponse(400, ['message' => 'ID trajet requis']);
                return;
            }
            $success = $this->routeModel->deleteRoute($routeId);
            if ($success) {
                sendResponse(200, ['message' => 'Trajet supprimé avec succès']);
            } else {
                sendResponse(404, ['message' => 'Trajet non trouvé']);
            }
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Voyages (Trips) - Récupérer tous les voyages
     * GET /v1/operator/trips
     */
    public function getTrips($params = []) {
        try {
            $page = $params['page'] ?? $_GET['page'] ?? 1;
            $limit = $params['limit'] ?? $_GET['limit'] ?? 20;
            $search = $params['search'] ?? $_GET['search'] ?? '';
            $offset = ($page - 1) * $limit;
            $filters = [];
            if ($search) {
                $filters['search'] = $search;
            }
            $trips = $this->tripModel->getAllTrips($limit, $offset, $filters);
            $total = $this->tripModel->countTrips($filters);
            sendResponse(200, [
                'trips' => $trips,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Voyages (Trips) - Créer un nouveau voyage
     * POST /v1/operator/trips
     */
    public function createTrip($params = []) {
        try {
            $user = AuthMiddleware::getUser();
            validateFields($params, [
                'route_id' => 'required',
                'bus_id' => 'required',
                'driver_id' => 'required',
                'estimated_departure_time' => 'required',
                'estimated_arrival_time' => 'required'
            ]);
            $params['created_by'] = $user->sub ?? null;
            $tripId = $this->tripModel->createTrip($params);
            sendResponse(201, [
                'message' => 'Voyage créé avec succès',
                'trip_id' => $tripId
            ]);
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Voyages (Trips) - Mettre à jour un voyage
     * PUT /v1/operator/trips/{id}
     */
    public function updateTrip($params = []) {
        try {
            $user = AuthMiddleware::getUser();
            $tripId = $params['id'] ?? 0;
            if (!$tripId) {
                sendResponse(400, ['message' => 'ID voyage requis']);
                return;
            }
            validateFields($params, [
                'status' => 'required'
            ]);
            $params['updated_by'] = $user->sub ?? null;
            $success = $this->tripModel->updateTrip($tripId, $params);
            if ($success) {
                sendResponse(200, ['message' => 'Voyage mis à jour avec succès']);
            } else {
                sendResponse(400, ['message' => 'Aucune donnée valide à mettre à jour']);
            }
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Voyages (Trips) - Supprimer un voyage
     * DELETE /v1/operator/trips/{id}
     */
    public function deleteTrip($params = []) {
        try {
            $tripId = $params['id'] ?? 0;
            if (!$tripId) {
                sendResponse(400, ['message' => 'ID voyage requis']);
                return;
            }
            $success = $this->tripModel->deleteTrip($tripId);
            if ($success) {
                sendResponse(200, ['message' => 'Voyage supprimé avec succès']);
            } else {
                sendResponse(404, ['message' => 'Voyage non trouvé']);
            }
        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Récupérer les détails d'un bus spécifique (pour manageBusSeats)
     * GET /v1/operator/buses/{id}
     */
    public function getBusDetails(array $params = []) {
        try {
            $busId = $params['id'] ?? 0;

            if (!$busId) {
                sendResponse(400, ['message' => 'ID du bus requis']);
                return;
            }

            $bus = $this->busModel->getBusDetails($busId);

            if (!$bus) {
                sendResponse(404, ['message' => 'Bus non trouvé']);
                return;
            }
            // La fonction JS attend { bus: busData }
            sendResponse(200, ['bus' => $bus]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur lors de la récupération du bus', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Récupérer les détails d'un plan de sièges spécifique (pour manageBusSeats)
     * GET /v1/operator/seat-plans/{id}
     */
    public function getSeatPlanDetails(array $params = []) {
        try {
            $seatPlanId = $params['id'] ?? 0;

            if (!$seatPlanId) {
                sendResponse(400, ['message' => 'ID du plan de sièges requis']);
                return;
            }

            // getSeatPlan est dans BusModel selon votre structure
            $seatPlan = $this->busModel->getSeatPlanDetails($seatPlanId);

            if (!$seatPlan) {
                sendResponse(404, ['message' => 'Plan de sièges non trouvé']);
                return;
            }
            // La fonction JS attend { seatPlan: seatPlanData }
            sendResponse(200, ['seatPlan' => $seatPlan]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur lors de la récupération du plan de sièges', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Sauvegarder (créer/mettre à jour) en masse les sièges pour un bus (pour saveSelectedSeats)
     * POST /v1/operator/buses/{bus_id}/seats
     */
    public function saveSeatsForBus(array $params = []) {
        try {
            $user = AuthMiddleware::getUser();
            $userId = $user->sub ?? null;

            $busId = $params['bus_id'] ?? 0;
            if (!$busId) {
                sendResponse(400, ['message' => 'ID bus requis']);
                return;
            }

            $requestData = json_decode(file_get_contents('php://input'), true);
            $seatsToSave = $requestData['seats'] ?? [];

            if (empty($seatsToSave) || !is_array($seatsToSave)) {
                sendResponse(400, ['message' => 'Liste de sièges invalide ou manquante']);
                return;
            }

            // Validation de chaque siège
            foreach ($seatsToSave as $seatData) {
                validateFields($seatData, [
                    'seat_number' => 'required',
                    'seat_type' => 'required' // 'standard' ou 'premium'
                ]);
                if (!in_array($seatData['seat_type'], ['standard', 'premium'])) {
                     sendResponse(400, ['message' => 'Type de siège invalide: ' . $seatData['seat_type']]);
                     return;
                }
            }

            $result = $this->busModel->saveOrUpdateSeatsForBus($busId, $seatsToSave, $userId);

            sendResponse(200, [
                'message' => $result['created'] . ' siège(s) créé(s), ' . $result['updated'] . ' siège(s) mis à jour.',
                'details' => $result
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur lors de la sauvegarde des sièges', 'error' => $e->getMessage()]);
        }
    }

    // ==================== MÉTHODES CRUD POUR LA TARIFICATION ====================

    /**
     * CRUD Tarification - Récupérer toutes les tarifications
     * GET /v1/operator/pricing
     */
    public function getPricing($params = []) {
        try {
            $page = $params['page'] ?? $_GET['page'] ?? 1;
            $limit = $params['limit'] ?? $_GET['limit'] ?? 20;
            $search = $params['search'] ?? $_GET['search'] ?? '';
            $offset = ($page - 1) * $limit;

            $filters = [];
            if ($search) {
                $filters['search'] = $search;
            }

            $pricing = $this->tripModel->getAllPricingsWithDetails($limit, $offset);
            $total = $this->tripModel->countPricings($filters);

            sendResponse(200, [
                'pricing' => $pricing,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Tarification - Créer une nouvelle tarification
     * POST /v1/operator/pricing
     */
    public function createPricing($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            validateFields($params, [
                'bus_type' => 'required',
                'seat_type' => 'required',
                'price' => 'required|numeric'
            ]);

            $params['created_by'] = $user->sub ?? null;
            $pricingId = $this->tripModel->createPricing($params);

            sendResponse(201, [
                'message' => 'Tarification créée avec succès',
                'pricing_id' => $pricingId
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Tarification - Mettre à jour une tarification
     * PUT /v1/operator/pricing/{id}
     */
    public function updatePricing($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $pricingId = $params['id'] ?? 0;
            if (!$pricingId) {
                sendResponse(400, ['message' => 'ID tarification requis']);
                return;
            }

            $params['updated_by'] = $user->sub ?? null;

            $success = $this->tripModel->updatePricing($pricingId, $params);

            if ($success) {
                sendResponse(200, ['message' => 'Tarification mise à jour avec succès']);
            } else {
                sendResponse(404, ['message' => 'Tarification non trouvée']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Tarification - Supprimer une tarification
     * DELETE /v1/operator/pricing/{id}
     */
    public function deletePricing($params = []) {
        try {
            $pricingId = $params['id'] ?? 0;
            if (!$pricingId) {
                sendResponse(400, ['message' => 'ID tarification requis']);
                return;
            }

            $success = $this->tripModel->deletePricing($pricingId);

            if ($success) {
                sendResponse(200, ['message' => 'Tarification supprimée avec succès']);
            } else {
                sendResponse(404, ['message' => 'Tarification non trouvée']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    // ==================== MÉTHODES CRUD POUR LES PAIEMENTS ====================

    /**
     * CRUD Paiements - Récupérer tous les paiements
     * GET /v1/operator/payments
     */
    public function getPayments($params = []) {
        try {
            $page = $params['page'] ?? $_GET['page'] ?? 1;
            $limit = $params['limit'] ?? $_GET['limit'] ?? 20;
            $status = $params['status'] ?? $_GET['status'] ?? '';
            $method = $params['method'] ?? $_GET['method'] ?? '';
            $offset = ($page - 1) * $limit;

            $filters = [];
            if ($status) $filters['status'] = $status;
            if ($method) $filters['method'] = $method;

            $payments = $this->paymentModel->getAllPayments($limit, $offset, $filters);
            $total = $this->paymentModel->countPayments($filters);

            sendResponse(200, [
                'payments' => $payments,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Paiements - Rembourser un paiement
     * POST /v1/operator/payments/{id}/refund
     */
    public function refundPayment($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $paymentId = $params['id'] ?? 0;
            if (!$paymentId) {
                sendResponse(400, ['message' => 'ID paiement requis']);
                return;
            }

            $data = json_decode(file_get_contents('php://input'), true) ?? [];
            $refundAmount = $data['amount'] ?? null;
            $reason = $data['reason'] ?? '';

            $payment = $this->paymentModel->getPaymentById($paymentId);
            if (!$payment) {
                sendResponse(404, ['message' => 'Paiement non trouvé']);
                return;
            }

            if ($payment['payment_status'] !== 'successful') {
                sendResponse(400, ['message' => 'Seuls les paiements réussis peuvent être remboursés']);
                return;
            }

            $refundData = [
                'payment_id' => $paymentId,
                'amount' => $refundAmount ?? $payment['amount'],
                'refund_reference' => 'REF_' . time() . '_' . $paymentId,
                'processed_by' => $user->sub ?? null,
                'reason' => $reason
            ];

            $success = $this->paymentModel->createRefund($refundData);

            if ($success) {
                // Mettre à jour le statut du paiement
                $this->paymentModel->updatePayment($paymentId, ['payment_status' => 'refunded']);
                sendResponse(200, ['message' => 'Paiement remboursé avec succès']);
            } else {
                sendResponse(500, ['message' => 'Erreur lors du remboursement']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    // ==================== MÉTHODES CRUD POUR LES UTILISATEURS ====================

    /**
     * CRUD Utilisateurs - Récupérer tous les utilisateurs
     * GET /v1/operator/users
     */
    public function getUsers($params = []) {
        try {

            $page = $params['page'] ?? $_GET['page'] ?? 1;
            $limit = $params['limit'] ?? $_GET['limit'] ?? 20;
            $search = $params['search'] ?? $_GET['search'] ?? '';
            $status = $params['status'] ?? $_GET['status'] ?? '';
            $role = $params['role'] ?? $_GET['role'] ?? '';
            $offset = ($page - 1) * $limit;

            $filters = [];
            if ($search) $filters['search'] = $search;
            if ($status) $filters['status'] = $status;
            if ($role) $filters['role'] = $role;

            $users = $this->userModel->getAllUsers($limit, $offset, $filters);
            $total = $this->userModel->countUsers($filters);

            sendResponse(200, [
                'users' => $users,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Utilisateurs - Créer un nouvel utilisateur
     * POST /v1/operator/users
     */
    public function createUser($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            validateFields($params, [
                'email' => 'required|email',
                'first_name' => 'required',
                'last_name' => 'required',
                'phone' => 'required',
                'password' => 'required'
            ]);

            // Vérifier si l'email existe déjà
            if ($this->userModel->emailExists($params['email'])) {
                sendResponse(400, ['message' => 'Un utilisateur avec cet email existe déjà']);
                return;
            }

            $params['created_by'] = $user->sub ?? null;
            $userId = $this->userModel->createUser($params);

            sendResponse(201, [
                'message' => 'Utilisateur créé avec succès',
                'user_id' => $userId
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Utilisateurs - Mettre à jour un utilisateur
     * PUT /v1/operator/users/{id}
     */
    public function updateUser($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $userId = $params['id'] ?? 0;
            if (!$userId) {
                sendResponse(400, ['message' => 'ID utilisateur requis']);
                return;
            }

            $params['updated_by'] = $user->sub ?? null;

            $success = $this->userModel->updateUser($userId, $params);

            if ($success) {
                sendResponse(200, ['message' => 'Utilisateur mis à jour avec succès']);
            } else {
                sendResponse(404, ['message' => 'Utilisateur non trouvé']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Utilisateurs - Supprimer un utilisateur
     * DELETE /v1/operator/users/{id}
     */
    public function deleteUser($params = []) {
        try {
            $userId = $params['id'] ?? 0;
            if (!$userId) {
                sendResponse(400, ['message' => 'ID utilisateur requis']);
                return;
            }

            $success = $this->userModel->deleteUser($userId);

            if ($success) {
                sendResponse(200, ['message' => 'Utilisateur supprimé avec succès']);
            } else {
                sendResponse(404, ['message' => 'Utilisateur non trouvé']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    // ==================== MÉTHODES CRUD POUR LES SIÈGES ====================

    /**
     * CRUD Sièges - Récupérer tous les sièges
     * GET /v1/operator/seats
     */
    public function getSeats($params = []) {
        try {
            $page = $params['page'] ?? $_GET['page'] ?? 1;
            $limit = $params['limit'] ?? $_GET['limit'] ?? 500;
            $busId = $params['bus_id'] ?? $_GET['bus_id'] ?? '';
            $offset = ($page - 1) * $limit;

            $filters = [];
            if ($busId) $filters['bus_id'] = $busId;

            $seats = $this->busModel->getAllSeats($limit, $offset, $filters);
            $total = $this->busModel->countSeats($filters);

            sendResponse(200, [
                'seats' => $seats,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    // ==================== MÉTHODES CRUD POUR LES PLANS DE SIÈGES ====================

    /**
     * CRUD Plans de sièges - Récupérer tous les plans de sièges
     * GET /v1/operator/seat-plans
     */
    public function getSeatPlans($params = []) {
        try {
            $page = $params['page'] ?? $_GET['page'] ?? 1;
            $limit = $params['limit'] ?? $_GET['limit'] ?? 20;
            $search = $params['search'] ?? $_GET['search'] ?? '';
            $offset = ($page - 1) * $limit;

            $filters = [];
            if ($search) $filters['search'] = $search;

            $seatPlans = $this->busModel->getAllSeatPlans($limit, $offset, $filters);
            $total = $this->busModel->countSeatPlans($filters);

            sendResponse(200, [
                'seatPlans' => $seatPlans,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Plans de sièges - Créer un nouveau plan de sièges
     * POST /v1/operator/seat-plans
     */
    public function createSeatPlan($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            validateFields($params, [
                'plan_config' => 'required',
                'layout_details' => 'required'
            ]);

            $params['created_by'] = $user->sub ?? null;
            $seatPlanId = $this->busModel->createSeatPlan($params);

            sendResponse(201, [
                'message' => 'Plan de sièges créé avec succès',
                'seat_plan_id' => $seatPlanId
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Plans de sièges - Mettre à jour un plan de sièges
     * PUT /v1/operator/seat-plans/{id}
     */
    public function updateSeatPlan($params = []) {
        try {
            $user = AuthMiddleware::getUser();

            $seatPlanId = $params['id'] ?? 0;
            if (!$seatPlanId) {
                sendResponse(400, ['message' => 'ID plan de sièges requis']);
                return;
            }

            $params['updated_by'] = $user->sub ?? null;

            $success = $this->busModel->updateSeatPlan($seatPlanId, $params);

            if ($success) {
                sendResponse(200, ['message' => 'Plan de sièges mis à jour avec succès']);
            } else {
                sendResponse(404, ['message' => 'Plan de sièges non trouvé']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * CRUD Plans de sièges - Supprimer un plan de sièges
     * DELETE /v1/operator/seat-plans/{id}
     */
    public function deleteSeatPlan($params = []) {
        try {
            $seatPlanId = $params['id'] ?? 0;
            if (!$seatPlanId) {
                sendResponse(400, ['message' => 'ID plan de sièges requis']);
                return;
            }

            $success = $this->busModel->deleteSeatPlan($seatPlanId);

            if ($success) {
                sendResponse(200, ['message' => 'Plan de sièges supprimé avec succès']);
            } else {
                sendResponse(404, ['message' => 'Plan de sièges non trouvé']);
            }

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
}
