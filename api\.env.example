# Configuration de la base de données
DB_HOST=localhost
DB_NAME=bus_booking
DB_USER=root
DB_PASS=

# Configuration JWT
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRY=3600

# Configuration SMTP pour les emails
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=EasyBus

# Configuration FedaPay
FEDAPAY_PUBLIC_KEY=pk_sandbox_your_public_key
FEDAPAY_SECRET_KEY=sk_sandbox_your_secret_key
FEDAPAY_ENVIRONMENT=sandbox
FEDAPAY_WEBHOOK_SECRET=your_webhook_secret

# Configuration de l'application
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost/bus-booking
API_VERSION=v1

# Configuration des uploads
UPLOAD_MAX_SIZE=5242880
ALLOWED_EXTENSIONS=jpg,jpeg,png,pdf

# Configuration des notifications
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false

# Configuration de sécurité
BCRYPT_ROUNDS=12
SESSION_LIFETIME=1440
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600
