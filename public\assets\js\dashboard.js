// Configuration de l'API
const API_BASE_URL = 'api/v1';

// Variables globales
let userStats = {};
let recentBookings = [];
let upcomingTrips = [];

// Vérifier l'authentification au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    if (!isAuthenticated()) {
        window.location.href = '/auth.html?return=' + encodeURIComponent(window.location.pathname);
        return;
    }
    
    initializeDashboard();
});

// Initialiser le tableau de bord
async function initializeDashboard() {
    try {
        // Afficher les informations utilisateur
        displayUserInfo();
        
        // Charger les données du tableau de bord
        await loadDashboardData();
        
    } catch (error) {
        console.error('Erreur lors de l\'initialisation:', error);
        showAlert('Erreur lors du chargement du tableau de bord', 'danger');
    }
}

// Afficher les informations utilisateur
function displayUserInfo() {
    const user = getCurrentUser();
    if (user) {
        document.getElementById('userName').textContent = user.first_name || 'Mon compte';
        document.getElementById('welcomeName').textContent = user.first_name || 'Utilisateur';
    }
}

// Charger les données du tableau de bord
async function loadDashboardData() {
    try {
        showLoadingState();
        
        // Appel API pour récupérer les données du tableau de bord
        const response = await apiRequest('users/dashboard');
        
        if (response.stats) {
            userStats = response.stats;
            displayStats(userStats);
        }
        
        if (response.recent_bookings) {
            recentBookings = response.recent_bookings;
            displayRecentBookings(recentBookings);
        }
        
        if (response.upcoming_trips) {
            upcomingTrips = response.upcoming_trips;
            displayUpcomingTrips(upcomingTrips);
        }
        
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        showAlert('Erreur lors du chargement des données', 'danger');
        hideLoadingState();
    }
}

// Afficher l'état de chargement
function showLoadingState() {
    // Les spinners sont déjà dans le HTML
}

// Masquer l'état de chargement
function hideLoadingState() {
    // Sera géré par les fonctions d'affichage
}

// Afficher les statistiques
function displayStats(stats) {
    document.getElementById('totalBookings').textContent = stats.total_bookings || 0;
    document.getElementById('upcomingTrips').textContent = stats.upcoming_trips || 0;
    document.getElementById('completedTrips').textContent = stats.completed_trips || 0;
    document.getElementById('totalSpent').textContent = formatCurrency(stats.total_spent || 0);
}

// Afficher les réservations récentes
function displayRecentBookings(bookings) {
    const container = document.getElementById('recentBookingsContainer');
    
    if (!bookings || bookings.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucune réservation récente</p>
                <a href="index.html" class="btn btn-sm btn-primary">Faire une réservation</a>
            </div>
        `;
        return;
    }
    
    let html = '';
    bookings.forEach(booking => {
        html += `
            <div class="border-bottom pb-3 mb-3">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">${booking.route_name}</h6>
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            ${formatDate(booking.estimated_departure_time)}
                        </small>
                    </div>
                    <span class="booking-status status-${booking.booking_status}">
                        ${getStatusText(booking.booking_status)}
                    </span>
                </div>
                <div class="mt-2">
                    <small class="text-primary fw-bold">${formatCurrency(booking.total_amount)}</small>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Afficher les prochains voyages
function displayUpcomingTrips(trips) {
    const container = document.getElementById('upcomingTripsContainer');
    
    if (!trips || trips.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-calendar-times fa-2x text-muted mb-3"></i>
                <p class="text-muted">Aucun voyage prévu</p>
                <a href="index.html" class="btn btn-sm btn-primary">Planifier un voyage</a>
            </div>
        `;
        return;
    }
    
    let html = '';
    trips.forEach(trip => {
        const departureDate = new Date(trip.estimated_departure_time);
        const isToday = isDateToday(departureDate);
        const isTomorrow = isDateTomorrow(departureDate);
        
        html += `
            <div class="booking-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-2">${trip.route_name}</h5>
                        <div class="row">
                            <div class="col-sm-6">
                                <small class="text-muted d-block">Départ</small>
                                <strong>${formatDateTime(trip.estimated_departure_time)}</strong>
                                <br><small class="text-muted">${trip.boarding_stop}</small>
                            </div>
                            <div class="col-sm-6">
                                <small class="text-muted d-block">Arrivée</small>
                                <strong>${formatDateTime(trip.estimated_arrival_time)}</strong>
                                <br><small class="text-muted">${trip.dropping_stop}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end">
                        ${isToday ? '<span class="badge bg-danger mb-2">Aujourd\'hui</span>' : ''}
                        ${isTomorrow ? '<span class="badge bg-warning mb-2">Demain</span>' : ''}
                        <div class="fw-bold text-primary">${formatCurrency(trip.total_amount)}</div>
                        <small class="text-muted">${trip.ticket_count} place(s)</small>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewBookingDetails(${trip.booking_id})">
                                Détails
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Fonctions utilitaires
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'decimal',
        minimumFractionDigits: 0
    }).format(amount) + ' FCFA';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function isDateToday(date) {
    const today = new Date();
    return date.toDateString() === today.toDateString();
}

function isDateTomorrow(date) {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return date.toDateString() === tomorrow.toDateString();
}

function getStatusText(status) {
    const statusMap = {
        'confirmed': 'Confirmé',
        'pending': 'En attente',
        'cancelled': 'Annulé'
    };
    return statusMap[status] || status;
}

// Actions du tableau de bord
function showProfile() {
    // Rediriger vers la page de profil ou afficher un modal
    showAlert('Fonctionnalité de profil en cours de développement', 'info');
}

function showBookings() {
    // Rediriger vers la page des réservations
    window.location.href = 'bookings.html';
}

function viewBookingDetails(bookingId) {
    // Afficher les détails de la réservation
    window.location.href = `booking-details.html?id=${bookingId}`;
}

// Fonction de déconnexion
function logout() {
    if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
        removeAuthToken();
        localStorage.removeItem('userData');
        window.location.href = 'index.html';
    }
}

// Fonction pour afficher les alertes (réutilisée depuis auth.js)
function showAlert(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" id="${alertId}">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHTML);
    
    // Auto-suppression après 5 secondes
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
