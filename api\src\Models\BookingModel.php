<?php
require_once __DIR__ . '/../Helpers/db.php';

class BookingModel {
    private $db;

    public function __construct() {
        $this->db = getDB();
    }

    // Lister les réservations d’un utilisateur
    public function getUserBookings($user_id) {
        $sql = "SELECT b.booking_id, t.trip_id, r.route_name, 
                       b.boarding_stop_id, b.dropping_stop_id, b.total_amount, b.booking_status 
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                WHERE b.user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':user_id' => $user_id]);
        return $stmt->fetchAll();
    }

    // Récupérer une réservation par ID pour un utilisateur donné
    public function getUserBookingById($booking_id, $user_id) {
        $sql = "SELECT b.booking_id, t.trip_id, r.route_name, 
                       s1.stop_name AS boarding_stop, s2.stop_name AS dropping_stop, 
                       b.total_amount, b.booking_status 
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                JOIN stop s1 ON b.boarding_stop_id = s1.stop_id
                JOIN stop s2 ON b.dropping_stop_id = s2.stop_id
                WHERE b.booking_id = :booking_id AND b.user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':booking_id' => $booking_id, ':user_id' => $user_id]);
        return $stmt->fetch();
    }

    // Récupérer une réservation par ID
    public function getBookingById($booking_id) {
        $sql = "SELECT b.booking_id, t.trip_id, r.route_name, 
                       s1.stop_name AS boarding_stop, s2.stop_name AS dropping_stop, 
                       b.total_amount, b.booking_status 
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                JOIN stop s1 ON b.boarding_stop_id = s1.stop_id
                JOIN stop s2 ON b.dropping_stop_id = s2.stop_id
                WHERE b.booking_id = :booking_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':booking_id' => $booking_id]);
        return $stmt->fetch();
    }

    // Rechercher des réservations par email ou téléphone (pour admin)
    public function searchBookingsByContact($email, $phone) {
        $sql = "SELECT b.*,
                       r.route_name, t.estimated_departure_time,
                       l1.location_name as departure_location,
                       l2.location_name as destination_location
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                JOIN location l1 ON t.departure_location_id = l1.location_id
                JOIN location l2 ON t.destination_location_id = l2.location_id
                JOIN user u ON b.user_id = u.user_id
                WHERE u.email = :email OR u.phone = :phone";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':email' => $email, ':phone' => $phone]);
        return $stmt->fetchAll();
    }

    // Marquer une réservation comme annulée
    public function cancelBooking($booking_id) {
        $sql = "UPDATE booking SET booking_status = 'cancelled' 
                WHERE booking_id = :booking_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':booking_id' => $booking_id]);
        return $stmt->rowCount();
    }

    // Marquer une réservation comme annulée pour un utilisateur donné
    public function cancelUserBooking($booking_id, $user_id) {
        $sql = "UPDATE booking SET booking_status = 'cancelled', cancelled_by = :user_id 
                WHERE booking_id = :booking_id AND user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':booking_id' => $booking_id, ':user_id' => $user_id]);
        return $stmt->rowCount();
    }

    public function getBookingDetailsForEmail($booking_id) {
        $sql = "SELECT b.booking_id, b.total_amount, b.booking_status, b.created_at,
                       r.route_name, t.estimated_departure_time,
                       l1.location_name as departure_location,
                       l2.location_name as destination_location,
                       u.first_name, u.last_name, u.email
                FROM booking b
                JOIN trip t ON b.trip_id = t.trip_id
                JOIN route r ON t.route_id = r.route_id
                JOIN location l1 ON r.departure_location_id = l1.location_id
                JOIN location l2 ON r.destination_location_id = l2.location_id
                JOIN user u ON b.user_id = u.user_id
                WHERE b.booking_id = :booking_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':booking_id' => $booking_id]);
        return $stmt->fetch();
    }

    // Créer une nouvelle réservation
    public function createBooking($data) {
        $sql = "INSERT INTO booking (user_id, trip_id, boarding_stop_id, dropping_stop_id, total_amount, created_by) 
                VALUES (:user_id, :trip_id, :boarding_stop_id, :dropping_stop_id, :total_amount, :created_by)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':user_id' => $data['user_id'],
            ':trip_id' => $data['trip_id'],
            ':boarding_stop_id' => $data['boarding_stop_id'],
            ':dropping_stop_id' => $data['dropping_stop_id'],
            ':total_amount' => $data['total_amount'],
            ':created_by' => $data['user_id']
        ]);
        return $this->db->lastInsertId();
    }

    public function updateBooking($booking_id, $data, $user_id) {
        $sql = "UPDATE booking SET
                    trip_id = :trip_id,
                    boarding_stop_id = :boarding_stop_id,
                    dropping_stop_id = :dropping_stop_id,
                    total_amount = :total_amount,
                    updated_by = :user_id
                WHERE booking_id = :booking_id AND user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':booking_id' => $booking_id,
            ':user_id' => $user_id,
            ':trip_id' => $data['trip_id'],
            ':boarding_stop_id' => $data['boarding_stop_id'],
            ':dropping_stop_id' => $data['dropping_stop_id'],
            ':total_amount' => $data['total_amount']
        ]);
        return $stmt->rowCount();
    }

    public function getTotalBookings() {
        $sql = "SELECT COUNT(*) as total FROM booking";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch();
    }

    public function getTodayBookings() {
        $sql = "SELECT COUNT(*) as total FROM booking WHERE DATE(created_at) = CURDATE()";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch();
    }

    public function getPendingBookings() {
        $sql = "SELECT COUNT(*) as total FROM booking WHERE booking_status = 'pending'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch();
    }

    public function getConfirmedBookings() {
        $sql = "SELECT COUNT(*) as total FROM booking WHERE booking_status = 'confirmed'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch();
    }

    public function getCancelledBookings() {    
        $sql = "SELECT COUNT(*) as total FROM booking WHERE booking_status = 'cancelled'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch();
    }

    public function getBookingsForOperator($page, $limit, $status, $search) {
        $offset = ($page - 1) * $limit;
        $sql = "SELECT b.*, u.first_name, u.last_name, u.email, u.phone
                FROM booking b
                JOIN user u ON b.user_id = u.user_id
                WHERE 1=1";
        if (!empty($status)) {
            $sql .= " AND b.booking_status = :status";
        }
        if (!empty($search)) {
            $sql .= " AND (u.email LIKE :search OR u.phone LIKE :search)";
        }
        $sql .= " ORDER BY b.created_at DESC LIMIT :limit OFFSET :offset";
        $stmt = $this->db->prepare($sql);
        if (!empty($status)) {
            $stmt->bindValue(':status', $status);
        }
        if (!empty($search)) {
            $stmt->bindValue(':search', '%' . $search . '%');
        }
        $stmt->bindValue(':limit', (int)$limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', (int)$offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    // Dans c/laragon/www/bus-booking/api/src/Models/BookingModel.php
// Ajoutez cette méthode :

public function getRecentBookingsForOperator(int $limit = 10): array {
    $sql = "SELECT b.*, u.first_name, u.last_name, u.email, r.route_name, t.estimated_departure_time
            FROM booking b
            JOIN user u ON b.user_id = u.user_id
            JOIN trip t ON b.trip_id = t.trip_id
            JOIN route r ON t.route_id = r.route_id
            ORDER BY b.created_at DESC
            LIMIT :limit";
    $stmt = $this->db->prepare($sql);
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Assurez-vous également que la méthode getBookingsCountForOperator existe et est correcte
// si elle est utilisée par la pagination dans OperatorController::getBookings.
// Voici un exemple si elle est manquante :
public function getBookingsCountForOperator(string $status = '', string $search = ''): int {
    $sql = "SELECT COUNT(b.booking_id) as total
            FROM booking b
            JOIN user u ON b.user_id = u.user_id
            WHERE 1=1";
    $params = [];
    if (!empty($status)) {
        $sql .= " AND b.booking_status = :status";
        $params[':status'] = $status;
    }
    if (!empty($search)) {
        $sql .= " AND (u.email LIKE :search OR u.phone LIKE :search OR u.first_name LIKE :search OR u.last_name LIKE :search)";
        $params[':search'] = '%' . $search . '%';
    }
    $stmt = $this->db->prepare($sql);
    $stmt->execute($params);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return (int)($result['total'] ?? 0);
}

public function getPopularRoutes() {
    $sql = "SELECT r.route_id, r.route_name, COUNT(b.booking_id) as booking_count
            FROM booking b
            JOIN trip t ON b.trip_id = t.trip_id
            JOIN route r ON t.route_id = r.route_id
            GROUP BY r.route_id
            ORDER BY booking_count DESC
            LIMIT 5";
    $stmt = $this->db->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

public function getPeakBookingTimes() {
    $sql = "SELECT DATE_FORMAT(b.created_at, '%H:00') as time_slot, COUNT(b.booking_id) as booking_count
            FROM booking b
            GROUP BY time_slot
            ORDER BY booking_count DESC
            LIMIT 5";
    $stmt = $this->db->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
     * Rapport agrégé des réservations pour l'opérateur
     * @param string $period 'day'|'month'|''
     * @param string $startDate format 'YYYY-MM-DD'
     * @param string $endDate format 'YYYY-MM-DD'
     * @return array
     */
    public function getBookingsReport($period = 'month', $startDate = '', $endDate = ''): array {
        $where = '';
        $params = [];
        if ($startDate && $endDate) {
            $where = "WHERE DATE(b.created_at) BETWEEN :start AND :end";
            $params[':start'] = $startDate;
            $params[':end'] = $endDate;
        } elseif ($period === 'year') {
            $where = "WHERE YEAR(b.created_at) = YEAR(CURDATE())";
        } elseif ($period === 'month') {
            $where = "WHERE MONTH(b.created_at) = MONTH(CURDATE()) AND YEAR(b.created_at) = YEAR(CURDATE())";
        } elseif ($period === 'day') {
            $where = "WHERE DATE(b.created_at) = CURDATE()";
        }

        // Group by period
        if ($period === 'year') {
            $groupBy = "YEAR(b.created_at)";
            $selectPeriod = "YEAR(b.created_at) AS period";
        } elseif ($period === 'month') {
            $groupBy = "DATE(b.created_at)";
            $selectPeriod = "DATE(b.created_at) AS period";
        } else { // day
            $groupBy = "HOUR(b.created_at)";
            $selectPeriod = "HOUR(b.created_at) AS period";
        }

        $sql = "SELECT $selectPeriod, COUNT(b.booking_id) AS total_bookings, SUM(b.total_amount) AS total_amount
                FROM booking b
                $where
                GROUP BY $groupBy
                ORDER BY period DESC";
        $stmt = $this->db->prepare($sql);
        foreach ($params as $k => $v) {
            $stmt->bindValue($k, $v);
        }
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

}