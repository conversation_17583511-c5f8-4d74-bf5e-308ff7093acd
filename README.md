# EasyBus - Système de Réservation de Bus

## Description

EasyBus est un système complet de réservation de tickets de bus en ligne, conçu pour l'Afrique de l'Ouest. Il permet aux utilisateurs de rechercher, réserver et gérer leurs voyages en bus facilement.

## Fonctionnalités

### 🎫 Fonctionnalités Utilisateur
- **Recherche de voyages** : Recherche par ville de départ/arrivée et date
- **Réservation avec ou sans compte** : Possibilité de réserver sans créer de compte
- **Gestion des réservations** : Tableau de bord utilisateur complet
- **Recherche de réservations** : Retrouver ses réservations par email/téléphone
- **Notifications email** : Confirmations automatiques par email
- **Tickets QR Code** : Génération de QR codes pour validation

### 🚌 Fonctionnalités Business
- **Tableau de bord opérateur** : Interface complète de gestion
- **Gestion des voyages** : Création et suivi des trajets
- **Validation des tickets** : Interface de scan/validation des tickets
- **Rapports et analyses** : Statistiques détaillées et métriques
- **Gestion des bus** : Suivi de la flotte et maintenance

### 💳 Paiement
- **Intégration FedaPay** : Passerelle de paiement sécurisée
- **Gestion des remboursements** : Système de remboursement automatisé
- **Suivi des transactions** : Historique complet des paiements

## Architecture Technique

### Backend (API)
- **PHP 8+** avec architecture MVC
- **AltoRouter** pour le routage léger
- **JWT** pour l'authentification
- **PHPMailer** pour les emails
- **MySQL** pour la base de données

### Frontend
- **HTML5/CSS3/JavaScript** vanilla
- **Bootstrap 5** pour le design responsive
- **Chart.js** pour les graphiques
- **Architecture modulaire** sans frameworks lourds

## Installation

### Prérequis
- PHP 8.0+
- MySQL 5.7+
- Composer
- Serveur web (Apache/Nginx)

### Étapes d'installation

1. **Cloner le projet**
```bash
git clone https://github.com/votre-repo/easybus.git
cd easybus
```

2. **Installer les dépendances PHP**
```bash
cd api
composer install
```

3. **Configuration de la base de données**
```bash
# Importer la structure de base de données
mysql -u root -p < bus_booking.sql
```

4. **Configuration de l'environnement**
```bash
# Copier le fichier d'exemple
cp api/.env.example api/.env

# Éditer les variables d'environnement
nano api/.env
```

5. **Configuration du serveur web**
- Pointer le document root vers le dossier racine du projet
- Configurer les règles de réécriture pour l'API

### Variables d'environnement importantes

```env
# Base de données
DB_HOST=localhost
DB_NAME=bus_booking
DB_USER=root
DB_PASS=

# JWT
JWT_SECRET=your_super_secret_key

# Email (PHPMailer)
SMTP_HOST=smtp.gmail.com
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# FedaPay
FEDAPAY_PUBLIC_KEY=pk_sandbox_...
FEDAPAY_SECRET_KEY=sk_sandbox_...
```

## Structure du projet

```
easybus/
├── api/                          # Backend API
│   ├── src/
│   │   ├── Controllers/          # Contrôleurs
│   │   ├── Models/              # Modèles de données
│   │   ├── Services/            # Services (Email, FedaPay)
│   │   ├── Router/              # Système de routage
│   │   ├── Helpers/             # Fonctions utilitaires
│   │   ├── Middlewares/         # Middlewares (Auth, etc.)
│   │   ├── Templates/           # Templates d'emails
│   │   └── config/              # Configuration
│   ├── vendor/                  # Dépendances Composer
│   ├── .env                     # Variables d'environnement
│   └── index.php               # Point d'entrée API
├── css/                         # Feuilles de style
├── js/                          # Scripts JavaScript
├── img/                         # Images
├── index.html                   # Page d'accueil
├── auth.html                    # Authentification
├── dashboard.html               # Tableau de bord utilisateur
├── search-booking.html          # Recherche de réservations
├── operator-dashboard.html      # Tableau de bord opérateur
├── test-api.html               # Tests API
└── bus_booking.sql             # Structure de base de données
```

## API Endpoints

### Authentification
- `POST /api/v1/auth/register` - Inscription
- `POST /api/v1/auth/login` - Connexion
- `POST /api/v1/auth/logout` - Déconnexion
- `POST /api/v1/auth/refresh` - Rafraîchir le token

### Utilisateurs
- `GET /api/v1/users/profile` - Profil utilisateur
- `PUT /api/v1/users/profile` - Modifier le profil
- `GET /api/v1/users/dashboard` - Données du tableau de bord

### Voyages et réservations
- `GET /api/v1/trips` - Rechercher des voyages
- `GET /api/v1/trips/{id}` - Détails d'un voyage
- `POST /api/v1/bookings` - Créer une réservation
- `POST /api/v1/bookings/guest` - Réservation sans compte
- `GET /api/v1/bookings/search` - Rechercher des réservations

### Paiements
- `POST /api/v1/payments` - Créer un paiement
- `GET /api/v1/payments/{id}` - Statut du paiement
- `POST /api/v1/payments/fedapay/webhook` - Webhook FedaPay

### Tickets
- `GET /api/v1/tickets/{code}` - Récupérer un ticket
- `POST /api/v1/tickets/validate` - Valider un ticket
- `GET /api/v1/tickets/{id}/qr` - Générer QR code

### Opérateurs
- `GET /api/v1/operator/dashboard` - Tableau de bord opérateur
- `GET /api/v1/operator/bookings` - Gestion des réservations
- `GET /api/v1/operator/reports` - Rapports et analyses

## Tests

### Test de l'API
Ouvrir `test-api.html` dans un navigateur pour tester les endpoints de l'API.

### Tests manuels
1. **Inscription/Connexion** : Tester via `auth.html`
2. **Recherche de voyages** : Utiliser la page d'accueil
3. **Réservation** : Processus complet de réservation
4. **Tableau de bord** : Vérifier les fonctionnalités utilisateur
5. **Validation tickets** : Tester via le tableau de bord opérateur

## Sécurité

- **Authentification JWT** avec expiration
- **Validation des données** côté serveur
- **Protection CORS** configurée
- **Hashage des mots de passe** avec bcrypt
- **Validation des webhooks** FedaPay

## Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Commit les changements (`git commit -am 'Ajouter nouvelle fonctionnalité'`)
4. Push vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Créer une Pull Request

## Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## Support

Pour toute question ou problème :
- Créer une issue sur GitHub
- Contacter l'équipe de développement

## Roadmap

### Version 1.1
- [ ] Application mobile (React Native)
- [ ] Notifications push
- [ ] Système de fidélité
- [ ] Intégration GPS temps réel

### Version 1.2
- [ ] Multi-langues
- [ ] Système de chat support
- [ ] Intégration réseaux sociaux
- [ ] API publique pour partenaires
