<?php
require_once __DIR__ . '/../Models/PaymentModel.php';
require_once __DIR__ . '/../Models/BookingModel.php';
require_once __DIR__ . '/../Services/FedaPayService.php';
require_once __DIR__ . '/../Controllers/NotificationController.php';
require_once __DIR__ . '/../Helpers/response.php';
require_once __DIR__ . '/../Helpers/validate.php';
require_once __DIR__ . '/../Middlewares/AuthMiddleware.php';

class PaymentController {
    private $paymentModel;
    private $bookingModel;
    private $fedaPayService;
    private $notificationController;

    public function __construct() {
        $this->paymentModel = new PaymentModel();
        $this->bookingModel = new BookingModel();
        $this->fedaPayService = new FedaPayService();
        $this->notificationController = new NotificationController();
    }

    /**
     * Créer un paiement
     * POST /v1/payments
     */
    public function createPayment($params = []) {
        try {
            $data = !empty($params) ? $params : json_decode(file_get_contents('php://input'), true);

            validateFields($data, [
                'booking_id' => 'positiveInt',
                'amount' => 'positiveInt',
                'currency' => 'required',
                'payment_method' => 'required',
                'customer' => 'required'
            ]);

            // Vérifier que la réservation existe
            $booking = $this->bookingModel->getBookingById($data['booking_id']);
            if (!$booking) {
                sendResponse(404, ['message' => 'Réservation non trouvée']);
                return;
            }

            // Vérifier que le montant correspond
            if ($data['amount'] != $booking['total_amount']) {
                sendResponse(400, ['message' => 'Montant incorrect']);
                return;
            }

            // Créer le paiement en base
            $paymentData = [
                'booking_id' => $data['booking_id'],
                'amount' => $data['amount'],
                'currency' => $data['currency'],
                'payment_method' => $data['payment_method'],
                'payment_provider' => 'fedapay',
                'payment_status' => 'pending'
            ];

            $payment_id = $this->paymentModel->createPayment($paymentData);

            // Créer la transaction FedaPay
            $fedaPayData = [
                'description' => "Paiement réservation #{$data['booking_id']}",
                'amount' => $data['amount'],
                'currency' => $data['currency'],
                'callback_url' => $_ENV['APP_URL'] . '/api/v1/payments/fedapay/callback',
                'customer' => $data['customer']
            ];

            $fedaPayResult = $this->fedaPayService->createPayment($fedaPayData);

            if (!$fedaPayResult['success']) {
                sendResponse(500, ['message' => 'Erreur lors de la création du paiement', 'error' => $fedaPayResult['error']]);
                return;
            }

            // Mettre à jour le paiement avec les informations FedaPay
            $this->paymentModel->updatePayment($payment_id, [
                'payment_reference' => $fedaPayResult['reference'],
                'provider_transaction_id' => $fedaPayResult['transaction_id']
            ]);

            sendResponse(201, [
                'message' => 'Paiement créé avec succès',
                'payment_id' => $payment_id,
                'payment_url' => $fedaPayResult['payment_url'],
                'transaction_id' => $fedaPayResult['transaction_id'],
                'reference' => $fedaPayResult['reference']
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Obtenir le statut d'un paiement
     * GET /v1/payments/{id}
     */
    public function getPaymentStatus($params) {
        try {
            $paymentId = $params['id'] ?? 0;

            if (!$paymentId) {
                sendResponse(400, ['message' => 'ID paiement requis']);
                return;
            }

            $payment = $this->paymentModel->getPaymentById($paymentId);

            if (!$payment) {
                sendResponse(404, ['message' => 'Paiement non trouvé']);
                return;
            }

            // Vérifier le statut auprès de FedaPay si nécessaire
            if ($payment['payment_status'] === 'pending' && $payment['provider_transaction_id']) {
                $fedaPayStatus = $this->fedaPayService->getTransactionStatus($payment['provider_transaction_id']);

                if ($fedaPayStatus['success'] && $fedaPayStatus['status'] !== $payment['payment_status']) {
                    // Mettre à jour le statut local
                    $this->paymentModel->updatePaymentStatus($paymentId, $fedaPayStatus['status']);
                    $payment['payment_status'] = $fedaPayStatus['status'];
                }
            }

            sendResponse(200, ['payment' => $payment]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Gérer les webhooks FedaPay
     * POST /v1/payments/fedapay/webhook
     */
    public function handleFedaPayWebhook($params = []) {
        try {
            $payload = file_get_contents('php://input');
            $signature = $_SERVER['HTTP_X_FEDAPAY_SIGNATURE'] ?? '';

            $result = $this->fedaPayService->handleWebhook($payload, $signature);

            if (!$result['success']) {
                sendResponse(400, ['message' => $result['error']]);
                return;
            }

            // Traiter l'événement selon le type
            if (isset($result['action'])) {
                $this->processWebhookEvent($result);
            }

            sendResponse(200, ['message' => 'Webhook traité avec succès']);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }

    /**
     * Traiter les événements de webhook
     */
    private function processWebhookEvent($eventData) {
        $transactionId = $eventData['transaction_id'];

        // Trouver le paiement correspondant
        $payment = $this->paymentModel->getPaymentByTransactionId($transactionId);

        if (!$payment) {
            error_log("Paiement non trouvé pour la transaction: {$transactionId}");
            return;
        }

        switch ($eventData['action']) {
            case 'transaction_approved':
                $this->handlePaymentSuccess($payment);
                break;

            case 'transaction_declined':
            case 'transaction_canceled':
                $this->handlePaymentFailure($payment);
                break;
        }
    }

    /**
     * Traiter un paiement réussi
     */
    private function handlePaymentSuccess($payment) {
        // Mettre à jour le statut du paiement
        $this->paymentModel->updatePaymentStatus($payment['payment_id'], 'successful');

        // Confirmer la réservation
        $this->bookingModel->updateBookingStatus($payment['booking_id'], 'confirmed');

        // Envoyer l'email de confirmation de paiement
        $this->sendPaymentConfirmationEmail($payment);
    }

    /**
     * Traiter un paiement échoué
     */
    private function handlePaymentFailure($payment) {
        // Mettre à jour le statut du paiement
        $this->paymentModel->updatePaymentStatus($payment['payment_id'], 'failed');

        // Annuler la réservation temporaire
        $this->bookingModel->updateBookingStatus($payment['booking_id'], 'cancelled');

        // Libérer les sièges réservés
        $this->bookingModel->releaseSeatsByBooking($payment['booking_id']);
    }

    /**
     * Envoyer l'email de confirmation de paiement
     */
    private function sendPaymentConfirmationEmail($payment) {
        try {
            $booking = $this->bookingModel->getBookingDetailsForEmail($payment['booking_id']);

            if ($booking) {
                $paymentData = [
                    'passenger_name' => $booking['first_name'] . ' ' . $booking['last_name'],
                    'passenger_email' => $booking['passenger_email'],
                    'amount' => $payment['amount'],
                    'currency' => $payment['currency'],
                    'payment_reference' => $payment['payment_reference'],
                    'booking_details' => $booking
                ];

                $this->notificationController->sendPaymentConfirmation($paymentData);
            }
        } catch (Exception $e) {
            error_log('Erreur envoi email confirmation paiement: ' . $e->getMessage());
        }
    }

    /**
     * Rembourser un paiement
     * POST /v1/payments/{id}/refund
     */
    public function refundPayment($params) {
        try {
            $user = AuthMiddleware::authenticate('operator');
            $paymentId = $params['id'] ?? 0;

            if (!$paymentId) {
                sendResponse(400, ['message' => 'ID paiement requis']);
                return;
            }

            $data = json_decode(file_get_contents('php://input'), true);
            $refundAmount = $data['amount'] ?? null;

            $payment = $this->paymentModel->getPaymentById($paymentId);

            if (!$payment) {
                sendResponse(404, ['message' => 'Paiement non trouvé']);
                return;
            }

            if ($payment['payment_status'] !== 'successful') {
                sendResponse(400, ['message' => 'Seuls les paiements réussis peuvent être remboursés']);
                return;
            }

            // Effectuer le remboursement via FedaPay
            $refundResult = $this->fedaPayService->refundTransaction(
                $payment['provider_transaction_id'],
                $refundAmount
            );

            if (!$refundResult['success']) {
                sendResponse(500, ['message' => 'Erreur lors du remboursement', 'error' => $refundResult['error']]);
                return;
            }

            // Enregistrer le remboursement
            $this->paymentModel->createRefund([
                'payment_id' => $paymentId,
                'amount' => $refundResult['amount'],
                'refund_reference' => $refundResult['refund_id'],
                'processed_by' => $user->sub
            ]);

            sendResponse(200, [
                'message' => 'Remboursement effectué avec succès',
                'refund_id' => $refundResult['refund_id'],
                'amount' => $refundResult['amount']
            ]);

        } catch (Exception $e) {
            sendResponse(500, ['message' => 'Erreur serveur', 'error' => $e->getMessage()]);
        }
    }
}